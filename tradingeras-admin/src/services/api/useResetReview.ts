import { useMutation, useQueryClient } from '@tanstack/react-query';

// @ts-ignore
import apiClient from '../api';

export const useResetReview = () => {
  const queryClient = useQueryClient();

  return useMutation((reviewId: string) => apiClient.reviews.reset(reviewId), {
    onSuccess: () => {
      queryClient.invalidateQueries(['admin_reviews']);
    },
    onError: error => {
      console.error('Error resetting review:', error);
    },
  });
};
