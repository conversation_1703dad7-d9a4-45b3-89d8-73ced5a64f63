import { useCallback, useEffect, useMemo, useState } from 'react';
import { usePagination, useSortBy, useTable } from 'react-table';

import useNotification from '../../../hooks/use-notification';
import { useAdminReviews } from '../../../services/api/useAdminReviews';
import { useApproveReview } from '../../../services/api/useApproveReview'; // The approve mutation hook
import { useResetReview } from '../../../services/api/useResetReview'; // The reset mutation hook
import { Review } from '../../../types/shared';
import Table from '../../molecules/table';
import TableContainer from '../../organisms/table-container';

import ReviewModal from './review-modal'; // The modal component

const DEFAULT_PAGE_SIZE = 15;

const ReviewTable = () => {
  const [query, setQuery] = useState('');
  const [offset, setOffset] = useState(0);
  const [pageIndex, setPageIndex] = useState(0); // Local pageIndex state
  const limit = DEFAULT_PAGE_SIZE;
  const [onlyNotApproved] = useState(false);
  const notification = useNotification();

  // State to control modal visibility and selected review
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Fetch the data using the custom hook
  const { data, isLoading } = useAdminReviews({
    offset,
    limit,
    approved: !onlyNotApproved,
  });

  const { mutate: approveReview } = useApproveReview();
  const { mutate: resetReview } = useResetReview();

  // Fallback to empty array if data is not available yet
  const count = data?.count || 0;

  const reviews = useMemo(() => {
    if (data) {
      return data.reviews;
    }
    return [];
  }, [data]);

  const numPages = useMemo(() => {
    if (count) {
      return Math.ceil(count / limit);
    }
    return 0;
  }, [count, limit]);

  // Update pageIndex when offset changes
  useEffect(() => {
    setPageIndex(Math.floor(offset / limit));
  }, [offset, limit]);

  // Memoized table configuration
  const tableConfig = useMemo(() => {
    return {
      columns: [
        {
          Header: 'Posted by',
          accessor: (row: Review) => row.customer?.email || '',
          id: 'customer_email',
        },
        {
          Header: 'Assigned order',
          accessor: (row: Review) => row.order?.id || '',
          id: 'order_id',
        },
        {
          Header: 'Completed',
          accessor: 'completed' as keyof Review,
          Cell: ({ value }: { value: any }) => (value ? 'Yes' : 'No'),
        },
        {
          Header: 'Approved',
          accessor: 'is_approved' as keyof Review,
          Cell: ({ value }: { value: any }) => (value ? 'Yes' : 'No'),
        },
      ],
      data: reviews,
      manualPagination: true,
      initialState: {
        pageSize: limit,
        pageIndex,
      },
      pageCount: numPages,
      autoResetPage: false,
    };
  }, [reviews, numPages, pageIndex, limit]);

  // Set up react-table's pagination and table logic
  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    rows,
    prepareRow,
    nextPage,
    previousPage,
  } = useTable(tableConfig, useSortBy, usePagination);

  const customCanPreviousPage = pageIndex > 0;
  const customCanNextPage = pageIndex < numPages - 1;

  // Handle pagination (next/previous)
  const handleNext = useCallback(() => {
    if (customCanNextPage) {
      setOffset(prevOffset => prevOffset + limit);
      nextPage();
    }
  }, [customCanNextPage, limit, nextPage]);

  const handlePrev = useCallback(() => {
    if (customCanPreviousPage) {
      setOffset(prevOffset => prevOffset - limit);
      previousPage();
    }
  }, [customCanPreviousPage, limit, previousPage]);

  // Handle row click to open modal
  const handleRowClick = (review: Review) => {
    setSelectedReview(review);
    setIsModalOpen(true);
  };

  // Close the modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedReview(null);
  };

  // Approve the review and close the modal
  const handleApproveReview = (reviewId: string) => {
    approveReview(reviewId, {
      onSuccess: () => {
        handleCloseModal();
        notification('Success', 'Approved!', 'success');
      },
      onError: () => {
        notification('Error', 'An error occurred. Please try again.', 'error');
      },
    });
  };

  // Reset the review and close the modal
  const handleResetReview = (reviewId: string) => {
    if (
      window.confirm(
        'Are you sure you want to reset this review? This will allow the user to resubmit their review.',
      )
    ) {
      resetReview(reviewId, {
        onSuccess: () => {
          handleCloseModal();
          notification(
            'Success',
            'Review has been reset. User can now resubmit.',
            'success',
          );
        },
        onError: () => {
          notification('Error', 'An error occurred, try again later', 'error');
        },
      });
    }
  };

  return (
    <>
      {/* Modal for displaying review details */}
      <ReviewModal
        review={selectedReview}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onApprove={handleApproveReview}
        onReset={handleResetReview}
      />

      <TableContainer
        numberOfRows={DEFAULT_PAGE_SIZE}
        hasPagination
        pagingState={{
          count: count,
          offset: offset,
          pageSize: limit,
          title: 'Reviews',
          currentPage: pageIndex + 1,
          pageCount: numPages,
          nextPage: handleNext,
          prevPage: handlePrev,
          hasNext: customCanNextPage,
          hasPrev: customCanPreviousPage,
        }}
        isLoading={isLoading}
      >
        <Table {...getTableProps()} searchValue={query} handleSearch={setQuery}>
          {/* Table Head */}
          <Table.Head>
            {headerGroups.map(headerGroup => (
              <Table.HeadRow
                {...headerGroup.getHeaderGroupProps()}
                key={headerGroup.id}
              >
                {headerGroup.headers.map(col => (
                  <Table.HeadCell {...col.getHeaderProps()} key={col.id}>
                    {col.render('Header')}
                  </Table.HeadCell>
                ))}
              </Table.HeadRow>
            ))}
          </Table.Head>

          {/* Table Body */}
          <Table.Body {...getTableBodyProps()}>
            {rows.map(row => {
              prepareRow(row);
              return (
                <Table.Row
                  {...row.getRowProps()}
                  key={row.id}
                  onClick={() => handleRowClick(row.original)}
                  className="hover:bg-gray-100 cursor-pointer transition duration-150"
                >
                  {row.cells.map((cell, index) => (
                    <Table.Cell {...cell.getCellProps()} key={index}>
                      {cell.render('Cell')}
                    </Table.Cell>
                  ))}
                </Table.Row>
              );
            })}
          </Table.Body>
        </Table>
      </TableContainer>
    </>
  );
};

export default ReviewTable;
