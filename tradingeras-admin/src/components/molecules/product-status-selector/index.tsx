import React, { useMemo } from 'react';
import {
  EllipseGreenSolid,
  EllipseGreySolid,
  EllipseOrangeSolid,
  EllipseRedSolid,
} from '@medusajs/icons';
import { Button, DropdownMenu } from '@medusajs/ui';
import { useAdminStore } from 'medusa-react';

import { ProductStatus } from '../../../types/shared';

type ProductStatusSelectorProps = {
  value: ProductStatus;
  onChange: (newStatus: ProductStatus) => void;
  onClick?: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
};

const options: {
  value: ProductStatus;
  label: string;
  icon: React.ReactNode;
  trust: boolean[];
}[] = [
  {
    value: ProductStatus.DRAFT,
    label: 'Draft',
    icon: <EllipseGreySolid />,
    trust: [false, true],
  },
  {
    value: ProductStatus.PUBLISHED,
    label: 'Published',
    icon: <EllipseGreenSolid />,
    trust: [true, false],
  },
  {
    value: ProductStatus.PROPOSED,
    label: 'Proposed',
    icon: <EllipseOrangeSolid />,
    trust: [false, true],
  },
  {
    value: ProductStatus.COMPLETED,
    label: 'Completed',
    icon: <EllipseOrangeSolid />,
    trust: [],
  },
  {
    value: ProductStatus.REJECTED,
    label: 'Rejected',
    icon: <EllipseRedSolid />,
    trust: [false, true],
  },
  {
    value: ProductStatus.CLOSED,
    label: 'Closed',
    icon: <EllipseRedSolid />,
    trust: [],
  },
];

const ProductStatusSelector: React.FC<ProductStatusSelectorProps> = ({
  onChange,
  value,
  ...rest
}) => {
  const { store } = useAdminStore();
  const currentStatus = useMemo(
    () => options.find(it => it.value === value)!,
    [value],
  );

  const availableOptions = useMemo(() => {
    const storeTrust = !!store?.virtual_is_trusted;

    return options.filter(it => it.trust.includes(storeTrust));
  }, [store]);

  return (
    <DropdownMenu {...rest}>
      <DropdownMenu.Trigger asChild>
        <Button variant="transparent">
          {currentStatus.icon}
          {currentStatus.label}
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end" side="bottom" className="min-w-[200px]">
        {availableOptions.map(({ value, label, icon }) => (
          <DropdownMenu.Item
            key={value}
            onClick={e => {
              e.stopPropagation();
              onChange(value);
            }}
          >
            {icon}
            {label}
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu>
  );
};

export default ProductStatusSelector;
