import React from 'react'

import IconProps from './types/icon-type'

const CalendarIcon: React.FC<IconProps> = ({
  size = '24px',
  color = 'currentColor',
  ...attributes
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...attributes}
    >
      <path
        d="M6.5 4V5.5M13.5 4V5.5M4 14.5V7C4 6.60218 4.15804 6.22064 4.43934 5.93934C4.72064 5.65804 5.10218 5.5 5.5 5.5H14.5C14.8978 5.5 15.2794 5.65804 15.5607 5.93934C15.842 6.22064 16 6.60218 16 7V14.5M4 14.5C4 14.8978 4.15804 15.2794 4.43934 15.5607C4.72064 15.842 5.10218 16 5.5 16H14.5C14.8978 16 15.2794 15.842 15.5607 15.5607C15.842 15.2794 16 14.8978 16 14.5M4 14.5V9.5C4 9.10218 4.15804 8.72064 4.43934 8.43934C4.72064 8.15804 5.10218 8 5.5 8H14.5C14.8978 8 15.2794 8.15804 15.5607 8.43934C15.842 8.72064 16 9.10218 16 9.5V14.5"
        stroke={color}
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.875 10.8672C7.875 11.0743 7.70711 11.2422 7.5 11.2422C7.29289 11.2422 7.125 11.0743 7.125 10.8672C7.125 10.6601 7.29289 10.4922 7.5 10.4922C7.70711 10.4922 7.875 10.6601 7.875 10.8672Z"
        stroke={color}
        strokeWidth="0.75"
      />
      <path
        d="M7.875 13.1943C7.875 13.4014 7.70711 13.5693 7.5 13.5693C7.29289 13.5693 7.125 13.4014 7.125 13.1943C7.125 12.9872 7.29289 12.8193 7.5 12.8193C7.70711 12.8193 7.875 12.9872 7.875 13.1943Z"
        stroke={color}
        strokeWidth="0.75"
      />
      <path
        d="M12.875 10.8672C12.875 11.0743 12.7071 11.2422 12.5 11.2422C12.2929 11.2422 12.125 11.0743 12.125 10.8672C12.125 10.6601 12.2929 10.4922 12.5 10.4922C12.7071 10.4922 12.875 10.6601 12.875 10.8672Z"
        stroke={color}
        strokeWidth="0.75"
      />
      <path
        d="M10.375 10.8672C10.375 11.0743 10.2071 11.2422 10 11.2422C9.79289 11.2422 9.625 11.0743 9.625 10.8672C9.625 10.6601 9.79289 10.4922 10 10.4922C10.2071 10.4922 10.375 10.6601 10.375 10.8672Z"
        stroke={color}
        strokeWidth="0.75"
      />
      <path
        d="M10.375 13.1943C10.375 13.4014 10.2071 13.5693 10 13.5693C9.79289 13.5693 9.625 13.4014 9.625 13.1943C9.625 12.9872 9.79289 12.8193 10 12.8193C10.2071 12.8193 10.375 12.9872 10.375 13.1943Z"
        stroke={color}
        strokeWidth="0.75"
      />
    </svg>
  )
}

export default CalendarIcon
