@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: "Inter";
  src: url("../../fonts/Inter-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("../../fonts/Inter-Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Inter";
  src: url("../../fonts/Inter-SemiBold.ttf") format("truetype");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Roboto Mono";
  src: url("../../fonts/RobotoMono-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Roboto Mono";
  src: url("../../fonts/RobotoMono-Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@layer base {
  html {
    @apply overflow-hidden;
  }
}

@layer components {
  .inter-5xlarge-regular {
    @apply text-5xlarge leading-4xlarge font-sans font-normal;
  }
  .inter-5xlarge-semibold {
    @apply text-5xlarge leading-4xlarge font-sans font-semibold;
  }

  .inter-4xlarge-regular {
    @apply text-4xlarge leading-3xlarge font-sans font-normal;
  }
  .inter-4xlarge-semibold {
    @apply text-4xlarge leading-3xlarge font-sans font-semibold;
  }

  .inter-3xlarge-regular {
    @apply text-3xlarge leading-2xlarge font-sans font-normal;
  }
  .inter-3xlarge-semibold {
    @apply text-3xlarge leading-2xlarge font-sans font-semibold;
  }

  .inter-2xlarge-regular {
    @apply text-2xlarge leading-xlarge font-sans font-normal;
  }
  .inter-2xlarge-semibold {
    @apply text-2xlarge leading-xlarge font-sans font-semibold;
  }

  .inter-xlarge-regular {
    @apply text-xlarge leading-large font-sans font-normal;
  }
  .inter-xlarge-semibold {
    @apply text-xlarge leading-large font-sans font-semibold;
  }

  .inter-large-regular {
    @apply text-large leading-base font-sans font-normal;
  }
  .inter-large-semibold {
    @apply text-large leading-base font-sans font-semibold;
  }

  .inter-base-regular {
    @apply leading-base font-sans text-base font-normal;
  }
  .inter-base-semibold {
    @apply leading-base font-sans text-base font-semibold;
  }

  .inter-small-regular {
    @apply text-small leading-small font-sans font-normal;
  }
  .inter-small-semibold {
    @apply text-small leading-small font-sans font-semibold;
  }

  .inter-xsmall-regular {
    @apply text-xsmall leading-xsmall font-sans font-normal;
  }
  .inter-xsmall-semibold {
    @apply text-xsmall leading-xsmall font-sans font-semibold;
  }

  .mono-5xlarge-regular {
    @apply text-5xlarge leading-4xlarge font-mono font-normal;
  }
  .mono-5xlarge-semibold {
    @apply text-5xlarge leading-4xlarge font-mono font-bold;
  }

  .mono-4xlarge-regular {
    @apply text-4xlarge leading-3xlarge font-mono font-normal;
  }
  .mono-4xlarge-semibold {
    @apply text-4xlarge leading-3xlarge font-mono font-bold;
  }

  .mono-3xlarge-regular {
    @apply text-3xlarge leading-2xlarge font-mono font-normal;
  }
  .mono-3xlarge-semibold {
    @apply text-3xlarge leading-2xlarge font-mono font-bold;
  }

  .mono-2xlarge-regular {
    @apply text-2xlarge leading-xlarge font-mono font-normal;
  }
  .mono-2xlarge-semibold {
    @apply text-2xlarge leading-xlarge font-mono font-bold;
  }

  .mono-xlarge-regular {
    @apply text-xlarge leading-large font-mono font-normal;
  }
  .mono-xlarge-semibold {
    @apply text-xlarge leading-large font-mono font-bold;
  }

  .mono-large-regular {
    @apply text-large leading-base font-mono font-normal;
  }
  .mono-large-semibold {
    @apply text-large leading-base font-mono font-bold;
  }

  .mono-base-regular {
    @apply leading-base font-mono text-base font-normal;
  }
  .mono-base-semibold {
    @apply leading-base font-mono text-base font-bold;
  }

  .mono-small-regular {
    @apply text-small leading-small font-mono font-normal;
  }
  .mono-small-semibold {
    @apply text-small leading-small font-mono font-bold;
  }

  .mono-xsmall-regular {
    @apply text-xsmall leading-xsmall font-mono font-normal;
  }
  .mono-xsmall-semibold {
    @apply text-xsmall leading-xsmall font-mono font-bold;
  }

  .radio-outer-ring > span.indicator[data-state="checked"] {
    @apply rounded-circle shadow-violet-60 shadow-[0_0_0_2px];
  }

  .bold-active-item + span {
    @apply inter-base-semibold;
  }
}

@layer components {
  .react-select-container {
    @apply -mx-3 mb-1 h-6 cursor-text border-0 p-0;

    .react-select__control {
      @apply border-0 bg-inherit shadow-none;
    }

    .react-select__control,
    .react-select__control--is-focused,
    .react-select__control--menu-is-open {
      @apply m-0 h-6 p-0 !important;
    }

    .react-select__value-container--is-multi,
    .react-select__value-container--has-value {
      @apply m-0 h-6 p-0 pl-3 !important;
    }

    .react-select__menu,
    .react-select__menu-list {
      @apply z-[110] mt-0 rounded-t-none !important;
    }

    .react-select__value-container {
      @apply pl-3 pr-0;
    }

    .react-select__indicators {
      @apply flex h-full items-center p-0 pr-3;

      .react-select__indicator {
        @apply p-0;
      }
    }

    .react-select__input {
      @apply mt-0 w-full min-w-[120px] pt-0 !important;
    }

    .react-select__option,
    .react-select__option--is-focused,
    .react-select__option--is-selected {
      @apply bg-grey-0 hover:bg-grey-5 !important;
    }

    .react-select__multi-value,
    .react-select__input-container {
      @apply my-0 py-0;
    }
  }
}

@layer components {
  .badge {
    @apply rounded-rounded inter-small-semibold w-min py-0.5 px-2;
  }

  .badge-disabled {
    @apply bg-grey-50 text-grey-50 bg-opacity-10;
  }

  .badge-primary {
    @apply bg-violet-60 text-violet-60 bg-opacity-10;
  }

  .badge-danger {
    @apply bg-rose-50 bg-opacity-10 text-rose-50;
  }

  .badge-success {
    @apply bg-teal-50 bg-opacity-10 text-teal-50;
  }

  .badge-warning {
    @apply bg-yellow-40 text-yellow-60 bg-opacity-20;
  }

  .badge-ghost {
    @apply text-grey-90 border-grey-20 whitespace-nowrap border;
  }

  .badge-default {
    @apply inter-small-regular bg-grey-10 text-grey-90 whitespace-nowrap;
  }

  .btn {
    @apply rounded-rounded focus:shadow-cta flex items-center justify-center focus:outline-none;
  }

  .btn-large {
    @apply inter-base-semibold px-large py-small;
  }

  .btn-medium {
    @apply inter-base-semibold px-base py-xsmall;
  }

  .btn-small {
    @apply inter-small-semibold px-small py-[6px];
  }

  .btn-primary {
    @apply bg-violet-60 text-grey-0 active:bg-violet-70 disabled:bg-grey-20  disabled:text-grey-40 hover:bg-violet-50;
  }

  .btn-secondary {
    @apply bg-grey-0 text-grey-90 border-grey-20 hover:bg-grey-5 active:bg-grey-5 active:text-violet-60 focus:border-violet-60  disabled:bg-grey-0 disabled:text-grey-30 border;
  }

  .btn-danger {
    @apply bg-grey-0 border-grey-20 hover:bg-grey-10 active:bg-grey-20 disabled:bg-grey-0 disabled:text-grey-30 border text-rose-50;
  }

  .btn-nuclear {
    @apply text-grey-0 hover:bg-rose-40 active:bg-rose-60 disabled:bg-grey-20 disabled:text-grey-40 bg-rose-50;
  }

  .btn-ghost {
    @apply text-grey-90 hover:bg-grey-5 active:bg-grey-5 active:text-violet-60 focus:border-violet-60  disabled:text-grey-30 bg-transparent disabled:bg-transparent;
  }

  .btn-primary-large {
    @apply btn btn-large btn-primary;
  }
  .btn-primary-medium {
    @apply btn btn-medium;
  }
  .btn-primary-small {
    @apply btn btn-small;
  }
  .btn-secondary-large {
    @apply btn btn-large btn-seconday;
  }
  .btn-secondary-medium {
    @apply btn btn-medium btn-seconday;
  }
  .btn-secondary-small {
    @apply btn btn-small btn-seconday;
  }
  .btn-ghost-large {
    @apply btn btn-large btn-ghost;
  }
  .btn-ghost-medium {
    @apply btn btn-medium btn-ghost;
  }
  .btn-ghost-small {
    @apply btn btn-small btn-ghost;
  }
}

@layer components {
  .date-picker {
    @apply border-0 pt-6 outline-none !important;

    .react-datepicker__month-container {
      .react-datepicker__header {
        @apply border-0 bg-inherit;
      }
    }

    .react-datepicker__day-names {
      @apply inter-base-semibold pt-4;

      .react-datepicker__day-name {
        @apply m-0 w-[40px];
      }
    }

    .react-datepicker__month {
      @apply m-0;
    }
    .react-datepicker__day {
      @apply inter-base-regular;
    }
    .react-datepicker__day--today {
      @apply text-grey-90 inter-base-semibold bg-grey-10 rounded !important;
    }

    .react-datepicker__day--outside-month,
    .past {
      @apply text-grey-40 !important;
    }

    .date {
      @apply text-grey-90 relative m-[0px] h-[38px] w-[38px] pt-3 align-middle leading-none;
      :hover {
        @apply cursor-pointer;
      }
    }
    .chosen,
    .react-datepicker__day--keyboard-selected {
      @apply bg-violet-60 text-grey-0 inter-base-semibold leading-none !important;
    }
  }

  .time-list::-webkit-scrollbar {
    /* chrome */
    display: none;
  }

  .time-list {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  .vice-city {
    @apply from-vice-start to-vice-stop bg-gradient-to-tr;
  }

  .hidden-actions[data-state="open"] {
    opacity: 1;
  }

  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply rounded-rounded bg-grey-30;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-grey-40;
  }

  /* TODO: Fix this as it breaks builds when using preset */
  /* .accordion-margin-transition {
    @apply transition-[margin] duration-300 ease-[cubic-bezier(0.87,0,0.13,1)];
  } */

  .col-tree:last-child .bottom-half-dash {
    @apply border-none;
  }
}

.scrolling-touch {
  -webkit-overflow-scrolling: touch;
}
.scrolling-auto {
  -webkit-overflow-scrolling: auto;
}

/* Classes to remove number spinners from inputs of type number */
/* Chrome, Safari, Edge, Opera */
.remove-number-spinner::-webkit-outer-spin-button,
.remove-number-spinner::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
.remove-number-spinner {
  -moz-appearance: textfield;
}
