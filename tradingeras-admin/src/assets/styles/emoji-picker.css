.emoji-picker-react {
  padding: 16px !important;
  border: none !important;
}

.emoji-picker-react .emoji-group {
  padding: 0 !important;
  font-size: 12px !important;
  font-weight: 400 !important;
}

.emoji-picker-react .emoji-group:before {
  font-family: "Inter" !important;
  text-transform: none !important;
  font-size: 12px !important;
  font-weight: 600 !important;
}

.emoji-picker-react .native {
  font-size: 24px !important;
}

.emoji-picker-react .emoji {
  color: #f3f4f6 !important;
}

.emoji-picker-react input.emoji-search {
  background-color: #f9fafb !important;
  border-radius: 4px !important;
  border-color: #e5e7eb !important;
  margin: 0 !important;
  width: 100% !important;
  font-size: 12px !important;
  font-family: "Inter" !important;
  color: #111827 !important;
  caret-color: #7c3aed !important;
}

.emoji-picker-react input.emoji-search::placeholder {
  font-size: 12px !important;
  font-family: "Inter" !important;
  color: #9ca3af !important;
}

.emoji-picker-react .emoji-categories button.icn-smileys_people {
  background-image: url("../svg/happy.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-animals_nature {
  background-image: url("../svg/sprout.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-food_drink {
  background-image: url("../svg/carrot.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-travel_places {
  background-image: url("../svg/plane.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-activities {
  background-image: url("../svg/controller.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-objects {
  background-image: url("../svg/lightbulb.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-symbols {
  background-image: url("../svg/heart.svg") !important;
}

.emoji-picker-react .emoji-categories button.icn-flags {
  background-image: url("../svg/flag.svg") !important;
}

.emoji-picker-react .emoji-categories button {
  width: 32px !important;
  height: 32px !important;
  border-radius: 4px !important;
}

.emoji-picker-react .emoji-categories button.active {
  background-color: white !important;
}

.emoji-picker-react .emoji-categories {
  background-color: #f3f4f6 !important;
  padding: 4px !important;
  border-radius: 4px !important;
  margin-bottom: 8px !important;
}

.emoji-picker-react
  .active-category-indicator-wrapper
  .active-category-indicator {
  display: none !important;
}

.emoji-scroll-wrapper {
  overflow-x: hidden !important;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.emoji-scroll-wrapper::-webkit-scrollbar {
  /* chrome */
  display: none;
}
