{"back-button-go-back": "Назад", "sales-channels-display-available-count": "Доступен в <2>{{availableChannelsCount}}</2> из <6>{{totalChannelsCount}}</6> Каналов Продаж", "activity-drawer-activity": "Действия", "activity-drawer-no-notifications-title": "Здесь так тихо...", "activity-drawer-no-notifications-description": "На данный момент у вас нет никаких уведомлений, но как только вы их получите, они будут здесь.", "activity-drawer-error-title": "О нет...", "activity-drawer-error-description": "Что-то пошло не так при попытке получить ваши уведомления - Мы продолжим попытки!", "activity-drawer-processing": "Обработка...", "analytics-config-form-title": "Обезличьте мои данные об использовании", "analytics-config-form-description": "Вы можете выбрать анонимизацию ваших данных об использовании. Если выбрана эта опция, мы не будем собирать вашу личную информацию, такую как ваше имя и адрес электронной почты.", "analytics-config-form-opt-out": "Отказаться от предоставления моих данных об использовании", "analytics-config-form-opt-out-later": "Вы всегда можете отказаться от предоставления своих данных об использовании в любое время.", "analytics-preferences-success": "Успешно", "analytics-preferences-your-preferences-were-successfully-updated": "Ваши настройки были успешно обновлены", "analytics-preferences-error": "Ошибка", "analytics-preferences-help-us-get-better": "Помогите нам стать лучше", "analytics-preferences-disclaimer": "Чтобы создать наиболее привлекательный опыт электронной коммерции, мы хотели бы получить представление о том, как вы используете Medusa. Информация о пользователях позволяет нам создавать более качественные, привлекательные и полезные продукты. Мы собираем данные только для улучшения продукта. Прочтите, какие данные мы собираем в нашем", "analytics-preferences-documentation": "документация", "analytics-preferences-please-enter-a-valid-email": "Пожалуйста, введите действительный адрес электронной почты", "analytics-preferences-continue": "Продолжить", "currency-input-currency": "Валюта", "currency-input-amount-is-not-valid": "Сумма недействительна", "organisms-success": "Успешно", "organisms-delete-successful": "Удаление прошло успешно", "organisms-are-you-sure-you-want-to-delete": "Вы уверены, что хотите удалить?", "organisms-no-cancel": "Нет, отменить", "organisms-yes-remove": "Да, удалить", "details-collapsible-hide-additional-details": "Скрыть дополнительные сведения", "details-collapsible-show-additional-details": "Показать дополнительные сведения", "edit-user-modal-success": "Успешно", "edit-user-modal-user-was-updated": "Пользователь был обновлен", "edit-user-modal-error": "Ошибка", "edit-user-modal-edit-user": "Редактировать пользователя", "edit-user-modal-first-name-label": "Имя", "edit-user-modal-first-name-placeholder": "Имя...", "edit-user-modal-last-name-label": "Фамилия", "edit-user-modal-last-name-placeholder": "Фамилия...", "edit-user-modal-email": "Электронная почта", "edit-user-modal-cancel": "Отменить", "edit-user-modal-save": "Сохранить", "error-boundary-back-to-dashboard": "Вернуться к приборной панели", "error-boundary-an-unknown-error-occured": "Произошла неизвестная ошибка", "error-boundary-bad-request": "Неудачный запрос", "error-boundary-you-are-not-logged-in": "Вы не вошли в систему", "error-boundary-you-do-not-have-permission-perform-this-action": "У вас нет разрешения на выполнение этого действия", "error-boundary-page-was-not-found": "Страница не была найдена", "error-boundary-an-unknown-server-error-occured": "Произошла неизвестная ошибка сервера", "error-boundary-503": "Сервер в данный момент недоступен", "error-boundary-500": "Произошла ошибка с неустановленными причинами, скорее всего, это связано с технической неполадкой с нашей стороны. Пожалуйста, попробуйте обновить страницу. Если проблема не устраняется, обратитесь к своему администратору.", "error-boundary-400": "Запрос был неправильно сформирован, исправьте ваш запрос и, пожалуйста, повторите попытку.", "error-boundary-401": "Вы не вошли в систему, пожалуйста, войдите, чтобы продолжить.", "error-boundary-403": "У вас нет разрешения на выполнение этого действия, если вы считаете, что это ошибка, обратитесь к своему администратору.", "error-boundary-404": "Запрошенная вами страница не была найдена, пожалуйста, проверьте URL-адрес и повторите попытку.", "error-boundary-500-2": "Сервер не смог обработать ваш запрос, скорее всего, это связано с технической проблемой с нашей стороны. Пожалуйста, попробуйте снова. Если проблема не устраняется, обратитесь к своему администратору.", "error-boundary-503-2": "Сервер временно недоступен, и ваш запрос не смог быть обработан. Пожалуйста, повторите попытку позже. Если проблема не устраняется, обратитесь к своему администратору.", "export-modal-title": "Инициализируйте экспорт ваших данных", "export-modal-cancel": "Отменить", "export-modal-export": "Экспорт", "file-upload-modal-upload-a-new-photo": "Загрузите новое фото", "gift-card-banner-edit": "Редактировать", "gift-card-banner-unpublish": "Отменить публикацию", "gift-card-banner-publish": "Опубликовать", "gift-card-banner-delete": "Удалить", "gift-card-banner-published": "Опубликованные", "gift-card-banner-unpublished": "Неопубликованные", "gift-card-denominations-section-denomination-added": "Добавленный номинал", "gift-card-denominations-section-a-new-denomination-was-successfully-added": "Успешно добавлен новый номинал", "gift-card-denominations-section-a-denomination-with-that-default-value-already-exists": "Номинал с таким значением по умолчанию уже существует", "gift-card-denominations-section-error": "Ошибка", "gift-card-denominations-section-add-denomination": "Добавить номинал", "gift-card-denominations-section-cancel": "Отменить", "gift-card-denominations-section-save-and-close": "Сохранить и закрыть", "gift-card-denominations-section-denomination-updated": "Обновленный номинал", "gift-card-denominations-section-a-new-denomination-was-successfully-updated": "Новый номинал был успешно обновлен", "gift-card-denominations-section-edit-denomination": "Редактировать номинал", "gift-card-denominations-section-denominations": "Номиналы", "gift-card-denominations-section-denomination": "Но<PERSON><PERSON><PERSON><PERSON>", "gift-card-denominations-section-in-other-currencies": "В других валютах", "gift-card-denominations-section-and-more_one": ", и {{count}} больше", "gift-card-denominations-section-and-more_other": ", и {{count}} больше", "gift-card-denominations-section-delete-denomination": "Удалить номинал", "gift-card-denominations-section-confirm-delete": "Вы уверены, что хотите удалить этот номинал?", "gift-card-denominations-section-denomination-deleted": "Номинал удален", "gift-card-denominations-section-denomination-was-successfully-deleted": "Номинал был успешно удален", "gift-card-denominations-section-edit": "Редактировать", "gift-card-denominations-section-delete": "Удалить", "help-dialog-how-can-we-help": "Как мы можем помочь?", "help-dialog-we-usually-respond-in-a-few-hours": "Обычно мы отвечаем в течение нескольких часов", "help-dialog-subject": "Тема", "help-dialog-what-is-it-about": "О чем идет речь?...", "help-dialog-write-a-message": "Напишите сообщение...", "help-dialog-feel-free-to-join-our-community-of": "Не стесняйтесь присоединяться к нашему сообществу", "help-dialog-merchants-and-e-commerce-developers": "продавцы и разработчики электронной коммерции", "help-dialog-send-a-message": "Отправить сообщение", "invite-modal-success": "Успешно", "invite-modal-invitation-sent-to": "Приглашение отправлено к {{user}}", "invite-modal-error": "Ошибка", "invite-modal-member": "Участник", "invite-modal-admin": "Администратор", "invite-modal-developer": "Разработчик", "invite-modal-invite-users": "Пригласить пользователей", "invite-modal-email": "Электронная почта", "invite-modal-role": "Роль", "invite-modal-select-role": "Выберите роль", "invite-modal-cancel": "Отменить", "invite-modal-invite": "Пригласить", "login-card-no-match": "Эти учетные данные не соответствуют нашим записям.", "login-card-log-in-to-medusa": "Войдите в Medusa", "login-card-email": "Электронная почта", "login-card-password": "Пароль", "login-card-forgot-your-password": "Забыли свой пароль?", "metadata-add-metadata": "Добавить метаданные", "product-attributes-section-edit-attributes": "Редактировать атрибуты", "product-attributes-section-dimensions": "Размеры", "product-attributes-section-configure-to-calculate-the-most-accurate-shipping-rates": "Настройте для расчета наиболее точных тарифов на доставку", "product-attributes-section-customs": "Таможные данные", "product-attributes-section-cancel": "Отменить", "product-attributes-section-save": "Сохранить", "product-attributes-section-mid-code": "MID-код", "product-attributes-section-hs-code": "Код ТН ВЭД", "product-attributes-section-country-of-origin": "Страна происхождения", "product-general-section-success": "Успешно", "product-general-section-successfully-updated-sales-channels": "Каналы продаж успешно обновлены", "product-general-section-error": "Ошибка", "product-general-section-failed-to-update-sales-channels": "Не удалось обновить каналы продаж", "product-general-section-edit-general-information": "Редактировать общую информацию", "product-general-section-gift-card": "Подарочная карта", "product-general-section-product": "<PERSON><PERSON><PERSON><PERSON>", "product-general-section-metadata": "Метаданные", "product-general-section-cancel": "Отменить", "product-general-section-save": "Сохранить", "product-general-section-delete": "Удалить", "product-general-section-edit-sales-channels": "Редактирование каналов продаж", "product-general-section-published": "Опубликованные", "product-general-section-draft": "Черновик", "product-general-section-details": "Подробности", "product-general-section-subtitle": "Подзаголовок", "product-general-section-handle": "Путь", "product-general-section-type": "Тип", "product-general-section-collection": "Коллекция", "product-general-section-category": "Категория", "product-general-section-discountable": "Со скидкой", "product-general-section-true": "Верно", "product-general-section-false": "Ложно", "product-general-section-count_one": "{{count}}", "product-general-section-count_other": "{{count}}", "product-general-section-sales-channels": "Каналы продаж", "product-media-section-edit-media": "Редактирование медиа", "product-media-section-upload-images-error": "Что-то пошло не так при попытке загрузить изображения.", "product-media-section-file-service-not-configured": "Возможно, у вас не настроена файловая служба. Пожалуйста, свяжитесь со своим администратором", "product-media-section-error": "Ошибка", "product-media-section-media": "Медиа", "product-media-section-add-images-to-your-product": "Добавьте изображения к своему товару.", "product-media-section-cancel": "Отменить", "product-media-section-save-and-close": "Сохранить и закрыть", "product-raw-section-raw-gift-card": "Необработанная подарочная карта", "product-raw-section-raw-product": "Необработанный товар", "product-thumbnail-section-success": "Успешно", "product-thumbnail-section-successfully-deleted-thumbnail": "Миниатюра успешно удалена.", "product-thumbnail-section-error": "Ошибка", "product-thumbnail-section-edit": "Редактировать", "product-thumbnail-section-upload": "Загрузить", "product-thumbnail-section-upload-thumbnail-error": "Что-то пошло не так при попытке загрузить миниатюру.", "product-thumbnail-section-you-might-not-have-a-file-service-configured-please-contact-your-administrator": "Возможно, у вас не настроена файловая служба. Пожалуйста, свяжитесь со своим администратором", "product-thumbnail-section-upload-thumbnail": "Загрузить миниатюру", "product-thumbnail-section-thumbnail": "Миниатюра", "product-thumbnail-section-used-to-represent-your-product-during-checkout-social-sharing-and-more": "Используется для представления вашего товара при оформлении заказа, публикации в социальных сетях и многого другого.", "product-thumbnail-section-cancel": "Отменить", "product-thumbnail-section-save-and-close": "Сохранить и закрыть", "product-variant-tree-count_one": "{{count}}", "product-variant-tree-count_other": "{{count}}", "product-variant-tree-add-prices": "Добавить цены", "product-variants-section-add-variant": "Добавить вариант", "product-variants-section-cancel": "Отменить", "product-variants-section-save-and-close": "Сохранить и закрыть", "product-variants-section-edit-stock-inventory": "Редактировать запасы и инвентаризацию", "product-variants-section-edit-variant": "Редактировать вариант", "edit-variants-modal-cancel": "Отменить", "edit-variants-modal-save-and-go-back": "Сохраните и вернуться назад", "edit-variants-modal-save-and-close": "Сохранить и закрыть", "edit-variants-modal-edit-variant": "Редактировать вариант", "edit-variants-modal-update-success": "Варианты были успешно обновлены", "edit-variants-modal-edit-variants": "Редактировать варианты", "edit-variants-modal-product-variants": "Варианты товар", "edit-variants-modal-variant": "Вар<PERSON><PERSON><PERSON>т", "edit-variants-modal-inventory": "Инвентарь", "product-variants-section-edit-prices": "Редактировать цены", "product-variants-section-edit-variants": "Редактировать варианты", "product-variants-section-edit-options": "Редактировать опции", "product-variants-section-product-variants": "Варианты товара", "product-variants-section-error": "Ошибка", "product-variants-section-failed-to-update-product-options": "Не удалось обновить опции товара", "product-variants-section-success": "Успешно", "product-variants-section-successfully-updated-product-options": "Успешно обновленные опции товара", "product-variants-section-product-options": "Опции товара", "product-variants-section-option-title": "Заголовок опции", "product-variants-section-option-title-is-required": "Требуется заголовок опции", "product-variants-section-add-an-option": "Добавьте опцию", "product-variants-section-inventory": "Инвентарь", "product-variants-section-title": "Заголовок", "product-variants-section-sku": "SKU", "product-variants-section-ean": "МНА", "product-variants-section-manage-inventory": "Управление запасами", "product-variants-section-duplicate-variant": "Продублировать вариант", "product-variants-section-delete-variant-label": "Удалить вариант", "product-variants-section-yes-delete": "Да, удалить", "product-variants-section-delete-variant-heading": "Удалить вариант", "product-variants-section-confirm-delete": "Вы уверены, что хотите удалить этот вариант? ", "product-variants-section-note-deleting-the-variant-will-also-remove-inventory-items-and-levels": " Примечание: Удаление варианта также приведет к удалению предметов инвентаря и уровней", "reset-token-card-error": "Ошибка", "reset-token-card-reset-your-password": "Сбросьте свой пароль", "reset-token-card-password-reset-description": "Введите свой адрес электронной почты ниже, и мы<1></1>вышлем вам инструкции о том, как сбросить<3></3>ваш пароль.", "reset-token-card-email": "Электронная почта", "reset-token-card-this-is-not-a-valid-email": "Это недействительная электронная почта", "reset-token-card-send-reset-instructions": "Отправить инструкции по сбросу", "reset-token-card-successfully-sent-you-an-email": "Успешно отправили вам электронное письмо", "reset-token-card-go-back-to-sign-in": "Вернуться на страницу входа в систему", "rma-return-product-table-product-details": "Подробная информация о товаре", "rma-return-product-table-quantity": "Количество", "rma-select-product-table-product-details": "Подробная информация о товаре", "rma-select-product-table-quantity": "Количество", "rma-select-product-table-refundable": "Возвратный", "rma-select-product-table-images-witch-count_one": "{{count}}", "rma-select-product-table-images-witch-count_other": "{{count}}", "rma-select-product-table-select-reason": "Выберите причину", "sidebar-store": "Мага<PERSON>ин", "sidebar-orders": "Заказы", "sidebar-products": "Товары", "sidebar-categories": "Категории", "sidebar-customers": "Клиенты", "sidebar-inventory": "Инвентарь", "sidebar-discounts": "Скидки", "sidebar-gift-cards": "Подарочные карты", "sidebar-pricing": "Цены", "sidebar-settings": "Настройки", "table-container-soothed-offset_one": "{{soothedOffset}} - {{pageSize}} из {{count}} {{title}}", "table-container-soothed-offset_other": "{{soothedOffset}} - {{pageSize}} из {{count}} {{title}}", "table-container-current-page": "{{currentPage}} из {{soothedPageCount}}", "timeline-request-return": "Запрос на возврат", "timeline-register-exchange": "Регистрация обмена", "timeline-register-claim": "Зарегистрировать претензию", "timeline-success": "Успешно", "timeline-added-note": "Добавленное примечание", "timeline-error": "Ошибка", "timeline-timeline": "Временная шкала", "upload-modal-new": "новое", "upload-modal-updates": "обновления", "upload-modal-drop-your-file-here-or": "Скиньте свой файл сюда, или", "upload-modal-click-to-browse": "нажмите, чтобы просмотреть.", "upload-modal-only-csv-files-are-supported": "Поддерживаются только файлы .csv.", "upload-modal-import-file-title": "Импортировать {{fileTitle}}", "upload-modal-cancel": "Отменить", "upload-modal-import-list": "Список импорта", "add-products-modal-add-products": "Добавить товары", "add-products-modal-search-by-name-or-description": "Поиск по названию или описанию...", "add-products-modal-cancel": "Отменить", "add-products-modal-save": "Сохранить", "add-products-modal-product-details": "Подробная информация о товаре", "add-products-modal-status": "Статус", "add-products-modal-variants": "Варианты", "templates-general": "Общие", "templates-first-name": "Имя", "templates-last-name": "Фамилия", "templates-company": "Компания", "templates-phone": "Телефон", "templates-billing-address": "Платежный адрес", "templates-shipping-address": "Адрес доставки", "templates-address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "templates-address-1": "Адрес 1", "templates-address-2": "Адрес 2", "templates-postal-code": "Почтовый индекс", "templates-city": "Город", "templates-province": "Область", "templates-country": "Страна", "templates-metadata": "Метаданные", "collection-modal-success": "Успешно", "collection-modal-successfully-updated-collection": "Коллекция успешно обновлена.", "collection-modal-error": "Ошибка", "collection-modal-successfully-created-collection": "Коллекция успешно создана.", "collection-modal-edit-collection": "Редактировать коллекцию", "collection-modal-add-collection": "Добавить коллекцию", "collection-modal-description": "Чтобы создать коллекцию, все, что вам нужно, - это заголовок и дескриптор.", "collection-modal-details": "Подробности", "collection-modal-title-label": "Заголовок", "collection-modal-title-placeholder": "Солнцезащитные очки", "collection-modal-handle-label": "Путь", "collection-modal-handle-placeholder": "солнцезащитные очки", "collection-modal-slug-description": "URL-адр<PERSON><PERSON> Slug для коллекции. Будет сгенерировано автоматически, если оставить поле пустым.", "collection-modal-metadata": "Метаданные", "collection-modal-cancel": "Отменить", "collection-modal-save-collection": "Сохранить коллекцию", "collection-modal-publish-collection": "Опубликовать коллекцию", "collection-product-table-add-products": "Добавить товары", "collection-product-table-products": "Товары", "collection-product-table-search-products": "Поиск товаров", "collection-product-table-cancel": "Отменить", "collection-product-table-save": "Сохранить", "collection-product-table-sort-by": "Сортировать по", "collection-product-table-all": "Все", "collection-product-table-newest": "Новейшие", "collection-product-table-oldest": "Старейшие", "collection-product-table-title": "Заголовок", "collection-product-table-decide-status-published": "Опубликован", "collection-product-table-draft": "Черновик", "collection-product-table-proposed": "Предложен", "collection-product-table-rejected": "Отклонен", "collection-product-table-remove-product-from-collection": "Удалить товар из коллекции", "collection-product-table-product-removed-from-collection": "Товар изъят из коллекции", "collections-table-delete-collection": "Удалить коллекцию", "collections-table-confirm-delete": "Вы уверены, что хотите удалить эту коллекцию?", "collections-table-edit": "Редактировать", "collections-table-delete": "Удалить", "collections-table-title": "Заголовок", "collections-table-handle": "Путь", "collections-table-created-at": "Создано в", "collections-table-updated-at": "Обновлено в", "collections-table-products": "Товары", "customer-group-table-details": "Подробности", "customer-group-table-delete": "Удалить", "customer-group-table-success": "Успешно", "customer-group-table-group-deleted": "Группа удалена", "customer-group-table-error": "Ошибка", "customer-group-table-failed-to-delete-the-group": "Не удалось удалить группу", "customer-group-table-customer-groups": "Группы клиентов", "customer-group-table-delete-from-the-group": "Удалить из группы", "customer-group-table-customer-groups-title": "Группы клиентов", "customer-group-table-groups": "Группы", "customer-group-table-all": "Все", "customer-group-table-edit-customers": "Редактирование клиентов", "customer-group-table-customers": "Клиенты", "customer-group-table-cancel": "Отменить", "customer-group-table-save": "Сохранить", "customer-orders-table-orders": "Заказы", "customer-orders-table-transfer-order": "Заказ трансфера", "customer-orders-table-paid": "Оплачено", "customer-orders-table-awaiting": "В ожидании", "customer-orders-table-requires-action": "Требуются действие", "customer-orders-table-n-a": "Н/Д", "customer-orders-table-fulfilled": "Выполнено", "customer-orders-table-shipped": "Отправлено", "customer-orders-table-not-fulfilled": "Не выполнено", "customer-orders-table-partially-fulfilled": "Частично выполнено", "customer-orders-table-partially-shipped": "Частично отгружен", "customer-orders-table-order": "Зак<PERSON>з", "customer-orders-table-remainder-more": "+ {{remainder}} больше", "customer-orders-table-date": "Дата", "customer-orders-table-fulfillment": "Выполнение", "customer-orders-table-status": "Статус", "customer-orders-table-total": "Общий", "customer-table-customers": "Клиенты", "customer-table-edit": "Редактировать", "customer-table-details": "Подробности", "customer-table-date-added": "Дата добавления", "customer-table-name": "Имя", "customer-table-email": "Электронная почта", "customer-table-orders": "Заказы", "discount-filter-dropdown-filters": "Фильтры", "discount-table-discounts": "Скидки", "discount-table-search-by-code-or-description": "Поиск по коду или описанию...", "discount-table-success": "Успешно", "discount-table-successfully-copied-discount": "Скидка успешно скопирована.", "discount-table-error": "Ошибка", "discount-table-scheduled": "Запла<PERSON><PERSON><PERSON>ован", "discount-table-expired": "Истек", "discount-table-active": "Акти<PERSON><PERSON>н", "discount-table-disabled": "Отключен", "discount-table-free-shipping": "Бесплатная доставка", "discount-table-code": "<PERSON>од", "discount-table-description": "Описание", "discount-table-amount": "Сумма", "discount-table-status": "Статус", "discount-table-redemptions": "Выкупы", "discount-table-delete-discount": "Удалить скидку", "discount-table-confirm-delete": "Вы уверены, что хотите удалить эту скидку?", "discount-table-publish": "Опубликовать", "discount-table-unpublish": "Отменить публикацию", "discount-table-successfully-published-discount": "Скидка успешно опубликована.", "discount-table-successfully-unpublished-discount": "Скидка успешно отменена.", "discount-table-duplicate": "Дублировать", "discount-table-delete": "Удалить", "draft-order-table-draft-orders": "Черновик заказов", "draft-order-table-completed": "Завершено", "draft-order-table-open": "Открыть", "draft-order-table-draft": "Черновик", "draft-order-table-order": "Зак<PERSON>з", "draft-order-table-date-added": "Дата добавления", "draft-order-table-customer": "Кли<PERSON><PERSON>т", "draft-order-table-status": "Статус", "gift-card-filter-dropdown-is-in-the-last": "в последнем", "gift-card-filter-dropdown-is-older-than": "старше, чем", "gift-card-filter-dropdown-is-after": "после", "gift-card-filter-dropdown-is-before": "до", "gift-card-filter-dropdown-is-equal-to": "равен", "gift-card-filter-dropdown-filters": "Фильтры", "gift-card-filter-dropdown-status": "Статус", "gift-card-filter-dropdown-payment-status": "Статус платежа", "gift-card-filter-dropdown-fulfillment-status": "Статус выполнения", "gift-card-filter-dropdown-date": "Дата", "gift-card-table-gift-cards": "Подарочные карты", "gift-card-table-code": "<PERSON>од", "gift-card-table-order": "Зак<PERSON>з", "gift-card-table-original-amount": "Первоначальная сумма", "gift-card-table-balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gift-card-table-region-has-been-deleted": "Регион был удален", "gift-card-table-none": "Нет", "gift-card-table-created": "Создано", "image-table-file-name": "Имя файла", "image-table-thumbnail": "Миниатюра", "image-table-select-thumbnail-image-for-product": "Выберите изображение, которое вы хотите использовать в качестве миниатюры для этого продукта.", "inventory-table-inventory-items": "Предметы инвентаря", "inventory-table-actions-adjust-availability": "Отрегулировать доступность", "inventory-table-view-product": "Просмотр товара", "inventory-table-success": "Успешно", "inventory-table-inventory-item-updated-successfully": "Предмет в инвентаре успешно обновлен.", "inventory-table-adjust-availability": "Отрегулировать доступность", "inventory-table-cancel": "Отменить", "inventory-table-save-and-close": "Сохранить и закрыть", "inventory-table-item": "Предмет", "inventory-table-variant": "Вар<PERSON><PERSON><PERSON>т", "inventory-table-sku": "SKU", "inventory-table-reserved": "Зарезервирован", "inventory-table-in-stock": "В наличии", "order-filter-dropdown-filters": "Фильтры", "order-filter-dropdown-status": "Статус", "order-filter-dropdown-payment-status": "Статус платежа", "order-filter-dropdown-fulfillment-status": "Статус выполнения", "order-filter-dropdown-regions": "Регионы", "order-filter-dropdown-sales-channel": "Канал продаж", "order-filter-dropdown-date": "Дата", "order-table-paid": "Оплачено", "order-table-awaiting": "В ожидании", "order-table-requires-action": "Требуется действие", "order-table-canceled": "Отменен", "order-table-n-a": "Н/Д", "order-table-order": "Зак<PERSON>з", "order-table-date-added": "Дата добавления", "order-table-customer": "Кли<PERSON><PERSON>т", "order-table-fulfillment": "Выполнен", "order-table-payment-status": "Статус платежа", "order-table-sales-channel": "Канал продаж", "order-table-total": "Общий", "order-table-filters-complete": "Завер<PERSON>ен", "order-table-filters-incomplete": "Незавершен", "price-list-table-filters": "Фильтры", "price-list-table-status": "Статус", "price-list-table-type": "Тип", "price-list-table-price-lists": "Прайс-листы", "price-list-table-success": "Успешно", "price-list-table-successfully-copied-price-list": "Прайс-лист успешно скопирован.", "price-list-table-error": "Ошибка", "price-list-table-delete-price-list": "Удалить прайс-лист", "price-list-table-confirm-delete": "Вы уверены, что хотите удалить этот прайс-лист?", "price-list-table-successfully-deleted-the-price-list": "Прайс-лист успешно удален", "price-list-table-successfully-unpublished-price-list": "Публикация прайс-листа успешно отменена", "price-list-table-successfully-published-price-list": "Прайс-лист успешно опубликован", "price-list-table-unpublish": "Отменить публикацию", "price-list-table-publish": "Опубликовать", "price-list-table-delete": "Удалить", "price-list-table-name": "Имя", "price-list-table-description": "Описание", "price-list-table-groups": "Группы", "price-list-table-other-more": "+ {{other}} больше", "price-overrides-apply-overrides-on-selected-variants": "Применить изменения к выбранным вариантам", "price-overrides-apply-on-all-variants": "Применить ко всем вариантам", "price-overrides-prices": "Цены", "price-overrides-cancel": "Отменить", "price-overrides-save-and-close": "Сохранить и закрыть", "price-overrides-show-regions": "Показать регионы", "product-table-products": "Товары", "product-table-copy-success": "Успешно", "product-table-copy-created-a-new-product": "Создан новый товар", "product-table-copy-error": "Ошибка", "product-table-delete-product": "Удалить товар", "product-table-confirm-delete": "Вы уверены, что хотите удалить этот товар?", "product-table-edit": "Редактировать", "product-table-unpublish": "Отменить публикацию", "product-table-publish": "Опубликовать", "product-table-draft": "черновик", "product-table-published": "опубликовано", "product-table-success": "Успешно", "product-table-successfully-unpublished-product": "Товар успешно удален из публикации", "product-table-successfully-published-product": "Товар успешно опубликован", "product-table-error": "Ошибка", "product-table-duplicate": "Дублировать", "product-table-delete": "Удалить", "product-table-proposed": "Предложено", "product-table-published-title": "Опубликовано", "product-table-rejected": "Отклонено", "product-table-draft-title": "Черновик", "product-table-name": "Имя", "product-table-collection": "Коллекция", "product-table-status": "Статус", "product-table-availability": "Доступность", "product-table-inventory": "Инвентарь", "product-table-inventory-in-stock-count_one": " в наличии {{count}} вариант(ов)", "product-table-inventory-in-stock-count_other": " в наличии  {{count}} вариант(ов)", "reservation-form-location": "Местоположение", "reservation-form-choose-where-you-wish-to-reserve-from": "Выберите, откуда вы хотите забронировать.", "reservation-form-item-to-reserve": "Предмет для резервирования", "reservation-form-select-the-item-that-you-wish-to-reserve": "Выберите предмет, который вы хотите зарезервировать.", "reservation-form-item": "Предмет", "reservation-form-in-stock": "В наличии", "reservation-form-available": "Доступно", "reservation-form-reserve": "<PERSON>ез<PERSON><PERSON><PERSON>", "reservation-form-remove-item": "Удалить предмет", "reservation-form-description": "Описание", "reservation-form-what-type-of-reservation-is-this": "Что это за тип резервирования?", "reservations-table-reservations": "Резервирование", "reservations-table-edit": "Редактировать", "reservations-table-delete": "Удалить", "reservations-table-confirm-delete": "Вы уверены, что хотите отменить это резервирование?", "reservations-table-remove-reservation": "Удалить резервирование", "reservations-table-reservation-has-been-removed": "Резервирование было удалено", "new-success": "Успешно", "new-successfully-created-reservation": "Резервирование успешно создано", "new-error": "Ошибка", "new-cancel": "Отмена", "new-save-reservation": "Сохранить резервирование", "new-reserve-item": "Резервный предмет", "new-metadata": "Метаданные", "reservations-table-order-id": "ID заказа", "reservations-table-description": "Описание", "reservations-table-created": "Создано", "reservations-table-quantity": "Количество", "search-modal-start-typing-to-search": "Начните вводить текст для поиска...", "search-modal-clear-search": "Очистить поиск", "search-modal-or": "или", "search-modal-to-navigate": "перейти", "search-modal-to-select-and": "выбрать и", "search-modal-to-search-anytime": "искать в любое время", "templates-settings": "Настройки", "templates-manage-the-settings-for-your-medusa-store": "Управляйте настройками вашего магазина Medusa", "transfer-orders-modal-info": "Информация", "transfer-orders-modal-customer-is-already-the-owner-of-the-order": "Клиент уже является владельцем заказа", "transfer-orders-modal-success": "Успешно", "transfer-orders-modal-successfully-transferred-order-to-different-customer": "Заказ успешно передан другому клиенту", "transfer-orders-modal-error": "Ошибка", "transfer-orders-modal-could-not-transfer-order-to-different-customer": "Не удалось передать заказ другому клиенту", "transfer-orders-modal-transfer-order": "Передать заказ", "transfer-orders-modal-order": "Зак<PERSON>з", "transfer-orders-modal-current-owner": "Текущий владелец", "transfer-orders-modal-the-customer-currently-related-to-this-order": "Клиент в настоящее время связан с этим заказом", "transfer-orders-modal-new-owner": "Новый владелец", "transfer-orders-modal-the-customer-to-transfer-this-order-to": "Клиент, которому необходимо передать этот заказ", "transfer-orders-modal-cancel": "Отмена", "transfer-orders-modal-confirm": "Подтвердить", "templates-edit-user": "Редактировать пользователя", "templates-remove-user": "Удалить пользователя", "templates-resend-invitation": "Отправить приглашение повторно", "templates-success": "Успешно", "templates-invitiation-link-has-been-resent": "Ссылка на приглашение была повторно отправлена", "templates-copy-invite-link": "Скопировать ссылку для приглашения", "templates-invite-link-copied-to-clipboard": "Ссылка на приглашение скопирована в буфер обмена", "templates-remove-invitation": "Удалить приглашение", "templates-expired": "Истекло", "templates-pending": "В ожидании", "templates-all": "Все", "templates-member": "Участник", "templates-admin": "Администратор", "templates-no-team-permissions": "Нет разрешений для команды", "templates-status": "Статус", "templates-active": "Акти<PERSON><PERSON>н", "templates-name": "Имя", "templates-email": "Электронная почта", "templates-team-permissions": "Разрешения команды", "templates-confirm-remove": "Вы уверены, что хотите удалить этого пользователя?", "templates-remove-user-heading": "Удалить пользователя", "templates-user-has-been-removed": "Пользователь был удален", "templates-confirm-remove-invite": "Вы уверены, что хотите удалить это приглашение?", "templates-remove-invite": "Удалить приглашение", "templates-invitiation-has-been-removed": "Приглашение было удалено", "multiselect-choose-categories": "Выберите категории", "domain-categories-multiselect-selected-with-counts_one": "{{count}}", "domain-categories-multiselect-selected-with-counts_other": "{{count}}", "details-success": "Успешно", "details-updated-products-in-collection": "Обновлены товары в коллекции", "details-error": "Ошибка", "details-back-to-collections": "Вернуться к коллекциям", "details-edit-collection": "Редактировать коллекцию", "details-delete": "Удалить", "details-metadata": "Метаданные", "details-edit-products": "Редактировать товары", "details-products-in-this-collection": "Товары из этой коллекции", "details-raw-collection": "Необработанная коллекция", "details-delete-collection": "Удалить коллекцию", "details-successfully-deleted-collection": "Коллекция успешно удалена.", "details-yes-delete": "Да, удалить", "details-successfully-updated-customer": "Клиент обновлен успешно.", "details-customer-details": "Подробная информация о клиенте", "details-general": "Общие данные", "details-first-name": "Имя", "details-lebron": "Леброн", "details-last-name": "Фамилия", "details-james": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details-email": "Электронная почта", "details-phone-number": "Номер телефона", "details-cancel": "Отменить", "details-save-and-close": "Сохранить и закрыть", "details-edit": "Редактировать", "details-back-to-customers": "Вернуться к клиентам", "details-first-seen": "Впервые увидел", "details-phone": "Телефон", "details-orders": "Заказы", "details-user": "Пользователь", "details-orders_one": "<PERSON><PERSON><PERSON><PERSON><PERSON>ов {{count}}", "details-orders_other": "<PERSON><PERSON><PERSON><PERSON><PERSON>ов {{count}}", "details-an-overview-of-customer-orders": "Обзор заказов клиентов", "details-raw-customer": "Необработанный клиент", "groups-group-updated": "Группа обновлена", "groups-group-created": "Группа создана", "groups-the-customer-group-has-been-updated": "Группа клиентов обновлена.", "groups-the-customer-group-has-been-created": "Группа клиентов создана.", "groups-edit-customer-group": "Редактировать группу клиентов", "groups-create-a-new-customer-group": "Создать новую группу клиентов", "groups-details": "Подробности", "groups-metadata": "Метаданные", "groups-cancel": "Отменить", "groups-edit-group": "Редактировать группу", "groups-publish-group": "Опубликовать группу", "groups-no-customers-in-this-group-yet": "В этой группе пока нет клиентов", "groups-customers": "Клиенты", "groups-edit": "Редактировать", "groups-delete": "Удалить", "groups-yes-delete": "Да, удалить", "groups-delete-the-group": "Удалить группу", "groups-group-deleted": "Группа удалена", "groups-confirm-delete-customer-group": "Вы уверены, что хотите удалить эту группу клиентов?", "groups-back-to-customer-groups": "Вернуться к группам клиентов", "groups-new-group": "Новая группа", "add-condition-conditions-were-successfully-added": "Условия были успешно добавлены", "add-condition-discount-conditions-updated": "Обновлены условия предоставления скидок", "add-condition-use-conditions-must-be-used-within-a-conditions-provider": "useConditions должен использоваться внутри ConditionsProvider.", "collections-search": "Поиск...", "collections-cancel": "Отменить", "collections-save-and-go-back": "Сохранить и вернуться", "collections-save-and-close": "Сохранить и закрыть", "customer-groups-search": "Поиск...", "customer-groups-cancel": "Отменить", "customer-groups-save-and-go-back": "Сохранить и вернуться", "customer-groups-save-and-close": "Сохранить и закрыть", "product-types-search": "Поиск...", "product-types-cancel": "Отменить", "product-types-save-and-go-back": "Сохранить и вернуться", "product-types-save-and-close": "Сохранить и закрыть", "products-search": "Поиск...", "products-cancel": "Отменить", "products-save-and-go-back": "Сохранить и вернуться", "products-save-and-close": "Сохранить и закрыть", "tags-search": "Поиск...", "tags-cancel": "Отменить", "tags-save-and-go-back": "Сохранить и вернуться", "tags-save-and-close": "Сохранить и закрыть", "edit-condition-add-conditions": "Добавить условия", "edit-condition-selected-with-count_one": "{{count}}", "edit-condition-selected-with-count_other": "{{count}}", "edit-condition-deselect": "Отменить выбор", "edit-condition-remove": "Удалить", "edit-condition-add": "Добавить", "edit-condition-title": "Редактировать {{type}} в условии скидки", "edit-condition-close": "Закрыть", "edit-condition-success": "Успешно", "edit-condition-the-resources-were-successfully-added": "Ресурсы успешно добавлены", "edit-condition-error": "Ошибка", "edit-condition-failed-to-add-resources": "Не удалось добавить ресурсы", "edit-condition-the-resources-were-successfully-removed": "Ресурсы успешно удалены", "edit-condition-failed-to-remove-resources": "Не удалось удалить ресурсы.", "edit-condition-use-edit-condition-context-must-be-used-within-an-edit-condition-provider": "useEditConditionContext должен использоваться внутри EditConditionProvider", "conditions-conditions": "Условия", "conditions-add-condition-label": "Добавить условие", "conditions-this-discount-has-no-conditions": "Эта скидка не имеет условий", "conditions-success": "Успешно", "conditions-condition-removed": "Условие удалено", "conditions-error": "Ошибка", "conditions-edit-condition": "Редактировать условие", "conditions-delete-condition": "Удалить условие", "conditions-discount-is-applicable-to-specific-products": "Скидка распространяется на отдельные товары", "conditions-discount-is-applicable-to-specific-collections": "Скидка распространяется на отдельные коллекции", "conditions-discount-is-applicable-to-specific-product-tags": "Скидка распространяется на определенные теги продукта", "conditions-discount-is-applicable-to-specific-product-types": "Скидка распространяется на отдельные виды товаров", "conditions-discount-is-applicable-to-specific-customer-groups": "Скидка распространяется на определенные группы клиентов", "configurations-success": "Успешно", "configurations-discount-updated-successfully": "Скидка успешно обновлена", "configurations-error": "Ошибка", "configurations-edit-configurations": "Редактировать конфигурации", "configurations-cancel": "Отменить", "configurations-save": "Сохранить", "configurations-configurations": "Конфигурации", "configurations-start-date": "Дата начала", "configurations-end-date": "Дата окончания", "configurations-delete-configuration": "Удалить конфигурацию", "configurations-discount-end-date-removed": "Дата окончания действия скидки удалена", "configurations-number-of-redemptions": "Количество погашений", "configurations-redemption-limit-removed": "Лимит погашения удален.", "configurations-delete-setting": "Удалить настройку", "configurations-discount-duration-removed": "Срок действия скидки отменен", "general-success": "Успешно", "general-discount-updated-successfully": "Скидка успешно обновлена", "general-error": "Ошибка", "general-edit-general-information": "Редактировать общую информацию", "general-details": "Подробности", "general-metadata": "Метаданные", "general-cancel": "Отменить", "general-save-and-close": "Сохранить и закрыть", "general-delete-promotion": "Удалить рекламную акцию", "general-confirm-delete-promotion": "Вы уверены, что хотите удалить эту рекламную акцию?", "general-promotion-deleted-successfully": "Рекламная акция успешно удалена", "general-discount-published-successfully": "Скидка успешно опубликована", "general-discount-drafted-successfully": "Скидка оформлена успешно", "general-delete-discount": "Удалить скидку", "general-template-discount": "Скидка по шаблону", "general-published": "Опубликовано", "general-draft": "Черновик", "general-discount-amount": "Сумма скидки", "general-valid-regions": "Допустимые регионы", "general-total-redemptions": "Всего погашений", "general-free-shipping": "БЕСПЛАТНАЯ ДОСТАВКА", "general-unknown-discount-type": "Неизвестный тип скидки", "details-discount-deleted": "Скидка удалена", "details-confirm-delete-discount": "Вы уверены, что хотите удалить эту скидку?", "details-delete-discount": "Удалить скидку", "details-back-to-discounts": "Вернуться к скидкам", "details-raw-discount": "Необработанная скидка", "discounts-add-discount": "Добавить скидку", "discount-form-add-conditions": "Добавить условия", "discount-form-choose-a-condition-type": "Выберите тип условия", "discount-form-you-can-only-add-one-of-each-type-of-condition": "Вы можете добавить только одно условие каждого типа.", "discount-form-you-cannot-add-any-more-conditions": "Вы не можете добавить больше условий", "discount-form-cancel": "Отменить", "discount-form-save": "Сохранить", "add-condition-tables-cancel": "Отменить", "add-condition-tables-save-and-add-more": "Сохранить и добавить еще", "add-condition-tables-save-and-close": "Сохранить и закрыть", "add-condition-tables-search-by-title": "Поиск по названию...", "add-condition-tables-search-groups": "Поиск по группам...", "add-condition-tables-search-products": "Поиск товаров...", "add-condition-tables-search-by-tag": "Поиск по тегу...", "add-condition-tables-search-by-type": "Поиск по типу...", "details-condition-tables-search-by-title": "Поиск по названию...", "details-condition-tables-search-groups": "Поиск по группам...", "details-condition-tables-cancel": "Отменить", "details-condition-tables-save-and-add-more": "Сохранить и добавить еще", "details-condition-tables-save-and-close": "Сохранить и закрыть", "details-condition-tables-search-products": "Поиск товаров...", "details-condition-tables-search-by-tag": "Поиск по тегу...", "details-condition-tables-search-by-type": "Поиск по типу...", "edit-condition-tables-search-by-title": "Поиск по названию...", "edit-condition-tables-title": "Название", "edit-condition-tables-search-groups": "Поиск по группам...", "edit-condition-tables-cancel": "Отменить", "edit-condition-tables-delete-condition": "Удалить условие", "edit-condition-tables-save": "Сохранить", "edit-condition-tables-search-products": "Поиск товаров...", "edit-condition-tables-search-by-tag": "Поиск по тегу...", "edit-condition-tables-search-by-type": "Поиск по типу...", "shared-title": "Название", "shared-products": "Товары", "shared-applies-to-the-selected-items": "Применяется к выбранным предметам.", "shared-applies-to-all-items-except-the-selected-items": "Применяется ко всем предметам, за исключением выбранных предметов.", "shared-members": "Участники", "shared-status": "Статус", "shared-variants": "Варианты", "shared-tag": "Тег", "shared-type": "Тип", "edit-conditions-modal-title": "Редактировать {{title}}", "form-use-discount-form-must-be-a-child-of-discount-form-context": "useDiscountForm должен быть дочерним элементом DiscountFormContext", "discount-form-error": "Ошибка", "discount-form-save-as-draft": "Сохранить как черновик", "discount-form-publish-discount": "Опубликовать скидку", "discount-form-create-new-discount": "Создать новую скидку", "discount-form-discount-type": "Тип скидки", "discount-form-select-a-discount-type": "Выбрать тип скидки", "discount-form-allocation": "Распределение", "discount-form-general": "Общие", "discount-form-configuration": "Конфигурация", "discount-form-discount-code-application-disclaimer": "Код скидки применяется с момента нажатия кнопки публикации и навсегда, если его не трогать.", "discount-form-conditions": "Условия", "discount-form-discount-code-apply-to-all-products-if-left-untouched": "Код скидки распространяется на все товары, если их не трогать.", "discount-form-add-conditions-to-your-discount": "Добавьте условия к вашей скидке", "discount-form-metadata": "Метаданные", "discount-form-metadata-usage-description": "Метаданные позволяют вам добавлять дополнительную информацию к вашей скидке.", "condition-item-remainder-more": "+{{remainder}} больше", "conditions-edit": "Редактировать", "conditions-product": "<PERSON><PERSON><PERSON><PERSON>", "conditions-collection": "Коллекция", "conditions-tag": "Тег", "conditions-customer-group": "Группа клиентов", "conditions-type": "Тип", "conditions-add-condition": "Добавить условие", "sections-start-date": "Дата начала", "sections-schedule-the-discount-to-activate-in-the-future": "Запланируйте скидку для активации в будущем.", "sections-select-discount-start-date": "Если вы хотите запланировать активацию скидки в будущем, вы можете установить здесь дату начала, в противном случае скидка вступит в силу немедленно.", "sections-start-time": "Время начала", "sections-discount-has-an-expiry-date": "У скидки есть срок действия?", "sections-schedule-the-discount-to-deactivate-in-the-future": "Запланируйте отключение скидки в будущем.", "sections-select-discount-end-date": "Если вы хотите запланировать деактивацию скидки в будущем, вы можете установить дату истечения срока действия здесь.", "sections-expiry-date": "Дата истечения", "sections-expiry-time": "Время истечения", "sections-limit-the-number-of-redemptions": "Ограничить количество погашений?", "sections-limit-applies-across-all-customers-not-per-customer": "Ограничение распространяется на всех клиентов, а не на одного клиента.", "sections-limit-discount-number-of-uses": "Если вы хотите ограничить количество раз, когда клиент может воспользоваться этой скидкой, вы можете установить ограничение здесь.", "sections-number-of-redemptions": "Количество погашений", "sections-availability-duration": "Срок доступности?", "sections-set-the-duration-of-the-discount": "Установите длительность скидки.", "sections-select-a-discount-type": "Выбрать тип скидки", "sections-total-amount": "Общая сумма", "sections-apply-to-the-total-amount": "Применить к общей сумме", "sections-item-specific": "Конкретный предмет", "sections-apply-to-every-allowed-item": "Применяется ко всем разрешенным предметам", "sections-percentage": "Процент", "sections-fixed-amount": "Фиксированная сумма", "sections-discount-in-whole-numbers": "Скидка в целых числах", "sections-you-can-only-select-one-valid-region-if-you-want-to-use-the-fixed-amount-type": "Если вы хотите использовать тип фиксированной суммы, вы можете выбрать только один действительный регион.", "sections-free-shipping": "Бесплатная доставка", "sections-override-delivery-amount": "Изменить сумму доставки", "sections-at-least-one-region-is-required": "Укажите хотя бы один регион", "sections-choose-valid-regions": "Выберите допустимые регионы", "sections-code": "<PERSON>од", "sections-summersale-10": "SUMMERSALE10", "sections-code-is-required": "Требуется код", "sections-amount-is-required": "Требуемая сумма", "sections-amount": "Сумма", "sections-customer-invoice-code": "Код, который ваши клиенты будут вводить при оформлении заказа. Это будет указано в счете вашего клиента.", "sections-uppercase-letters-and-numbers-only": "Только заглавные буквы и цифры.", "sections-description": "Описание", "sections-summer-sale-2022": "Летняя распродажа 2022", "sections-this-is-a-template-discount": "Это шаблонная скидка", "sections-template-discounts-description": "Шаблонные скидки позволяют определить набор правил, которые можно использовать для группы скидок. Это полезно в кампаниях, которые должны генерировать уникальные коды для каждого пользователя, но правила для всех уникальных кодов должны быть одинаковыми.", "discount-form-product": "<PERSON><PERSON><PERSON><PERSON>", "discount-form-only-for-specific-products": "Только для определенных товаров", "discount-form-choose-products": "Выбрать товары", "discount-form-customer-group": "Группа клиентов", "discount-form-only-for-specific-customer-groups": "Только для определенных групп клиентов", "discount-form-choose-groups": "Выбрать группы", "discount-form-tag": "Тег", "discount-form-only-for-specific-tags": "Только для определенных тегов", "discount-form-collection": "Коллекция", "discount-form-only-for-specific-product-collections": "Только для определенных коллекций товаров", "discount-form-choose-collections": "Выбрать коллекции", "discount-form-type": "Тип", "discount-form-only-for-specific-product-types": "Только для определенных типов товаров", "discount-form-choose-types": "Выбрать типы", "utils-products": "товары", "utils-groups": "группы", "utils-tags": "теги", "utils-collections": "коллекции", "utils-types": "типы", "gift-cards-created-gift-card": "Подарочная карта создана.", "gift-cards-custom-gift-card-was-created-successfully": "Специальная подарочная карта успешно создана.", "gift-cards-error": "Ошибка", "gift-cards-custom-gift-card": "Индивидуальная подарочная карта", "gift-cards-details": "Подробности", "gift-cards-receiver": "Получатель", "gift-cards-cancel": "Отменить", "gift-cards-create-and-send": "Создать и отправить", "details-updated-gift-card": "Обновленная подарочная карта", "details-gift-card-was-successfully-updated": "Подарочная карта успешно обновлена", "details-failed-to-update-gift-card": "Не удалось обновить подарочную карту.", "details-edit-gift-card": "Редактировать подарочную карту", "details-details": "Подробности", "details-edit-details": "Редактировать подробности", "details-update-balance-label": "Обновить баланс", "details-updated-status": "Статус обновлен", "details-successfully-updated-the-status-of-the-gift-card": "Статус подарочной карты успешно обновлен.", "details-back-to-gift-cards": "Вернуться к подарочным картам", "details-original-amount": "Первоначальная сумма", "details-balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details-region": "Регион", "details-expires-on": "Истекает в", "details-created": "Создано", "details-raw-gift-card": "Необработанная подарочная карта", "details-balance-updated": "Баланс обновлен", "details-gift-card-balance-was-updated": "Баланс подарочной карты был обновлен", "details-failed-to-update-balance": "Не удалось обновить баланс", "details-update-balance": "Обновить баланс", "manage-back-to-gift-cards": "Вернуться к подарочным картам", "gift-cards-please-enter-a-name-for-the-gift-card": "Пожалуйста, введите название подарочной карты", "gift-cards-please-add-at-least-one-denomination": "Добавьте хотя бы один номинал", "gift-cards-denominations": "Номиналы", "gift-cards-success": "Успешно", "gift-cards-successfully-created-gift-card": "Подарочная карта успешно создана", "gift-cards-create-gift-card": "Создать подарочную карту", "gift-cards-gift-card-details": "Подробности о подарочной карте", "gift-cards-name": "Имя", "gift-cards-the-best-gift-card": "Лучшая подарочная карта", "gift-cards-description": "Описание", "gift-cards-the-best-gift-card-of-all-time": "Лучшая подарочная карта всех времен", "gift-cards-thumbnail": "Миниатюра", "gift-cards-delete": "Удалить", "gift-cards-size-recommended": "рекомендуется разрешение 1200 x 1600 (3:4), до 10 МБ каждое", "gift-cards-amount": "Сумма", "gift-cards-add-denomination": "Добавить номинал", "gift-cards-create-publish": "Создать и опубликовать", "gift-cards-successfully-updated-gift-card": "Подарочная карта успешно обновлена.", "gift-cards-gift-cards": "Подарочные карты", "gift-cards-manage": "Управляйте подарочными картами вашего магазина Medusa", "gift-cards-are-you-ready-to-sell-your-first-gift-card": "Готовы ли вы продать свою первую подарочную карту?", "gift-cards-no-gift-card-has-been-added-yet": "Подарочная карта еще не добавлена.", "gift-cards-history": "История", "gift-cards-see-the-history-of-purchased-gift-cards": "Посмотреть историю купленных подарочных карт", "gift-cards-successfully-deleted-gift-card": "Подарочная карта успешно удалена.", "gift-cards-yes-delete": "Да, удалить", "gift-cards-delete-gift-card": "Удалить подарочную карту", "inventory-filters": "Фильтры", "address-form-address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "address-form-company": "Компания", "address-form-address-1": "Адрес 1", "address-form-this-field-is-required": "Это поле обязательно к заполнению", "address-form-address-2": "Адрес 2", "address-form-postal-code": "Почтовый индекс", "address-form-city": "Город", "address-form-country": "Страна", "edit-sales-channels-edit-channels": "Редактировать каналы", "edit-sales-channels-add-channels": "Добавить каналы", "general-form-location-name": "Название местоположения", "general-form-flagship-store-warehouse": "Флагманский магазин, склад", "general-form-name-is-required": "Имя обязательно", "location-card-delete-location": "Удалить местоположение", "location-card-confirm-delete": "Вы уверены, что хотите удалить это местоположение. При этом также будут удалены все уровни запасов и резервирования, связанные с этим местоположением.", "location-card-success": "Успешно", "location-card-location-deleted-successfully": "Местоположение успешно удалено", "location-card-error": "Ошибка", "location-card-edit-details": "Edit details", "location-card-delete": "Delete", "location-card-connected-sales-channels": "Connected sales channels", "sales-channels-form-add-sales-channels": "Add sales channels", "sales-channels-form-edit-channels": "Edit channels", "sales-channels-section-not-connected-to-any-sales-channels-yet": "Not connected to any sales channels yet", "edit-success": "Успешно", "edit-location-edited-successfully": "Location edited successfully", "edit-error": "Ошибка", "edit-edit-location-details": "Edit Location Details", "edit-metadata": "Метаданные", "edit-cancel": "Отменить", "edit-save-and-close": "Сохранить и закрыть", "new-location-added-successfully": "Местоположение успешно добавлено", "new-location-created": "Местоположение создано успешно, но при связывании каналов продаж произошла ошибка.", "new-cancel-location-changes": "Вы уверены, что хотите отменить с несохраненными изменениями?", "new-yes-cancel": "Да, отменить", "new-no-continue-creating": "Нет, продолжить создавать", "new-add-location": "Добавить местоположение", "new-add-new-location": "Добавить новое местоположение", "new-general-information": "Общая информация", "new-location-details": "Укажите подробную информацию об этом местоположении", "new-select-location-channel": "Укажите, через какие каналы продаж можно приобрести товары в этом местоположении.", "oauth-complete-installation": "Полная установка", "claim-type-form-refund": "Возврат денег", "claim-type-form-replace": "Замена", "items-to-receive-form-items-to-receive": "Предметы для получения", "items-to-receive-form-product": "<PERSON><PERSON><PERSON><PERSON>", "items-to-receive-form-quantity": "Количество", "items-to-receive-form-refundable": "Подлежит возврату", "add-return-reason-reason-for-return": "Причина возврата", "add-return-reason-reason": "Причина", "add-return-reason-choose-a-return-reason": "Выбрать причину возврата", "add-return-reason-note": "Примечание", "add-return-reason-product-was-damaged-during-shipping": "Товар был поврежден во время транспортировки", "add-return-reason-cancel": "Отменить", "add-return-reason-save-and-go-back": "Сохранить и вернуться", "add-return-reason-select-reason-title": "Выбрать причину", "add-return-reason-edit-reason": "Редактировать причину", "add-return-reason-select-reason": "Выбрать причину", "items-to-return-form-items-to-claim": "Предметы, на которые можно претендовать", "items-to-return-form-items-to-return": "Предметы возврата", "items-to-return-form-product": "<PERSON><PERSON><PERSON><PERSON>", "items-to-return-form-quantity": "Количество", "items-to-return-form-refundable": "Подлежит возврату", "add-additional-items-screen-go-back": "Вернуться назад", "add-additional-items-screen-add-products": "Добавить продукты", "add-additional-items-screen-add-product-variants": "Добавить варианты товара", "add-additional-items-screen-search-products": "Поиск товаров", "add-additional-items-screen-variant-price-missing": "В этом варианте нет цены для региона/валюты этого заказа, и его нельзя выбрать.", "add-additional-items-screen-stock": "За<PERSON><PERSON><PERSON>", "add-additional-items-screen-price": "Цена", "add-additional-items-screen-price-overridden-in-price-list-applicable-to-this-order": "Цена была изменена в прайс-листе, применимом к этому заказу.", "items-to-send-form-items-to-send": "Предметы для отправки", "items-to-send-form-add-products": "Добавить товары", "items-to-send-form-product": "<PERSON><PERSON><PERSON><PERSON>", "items-to-send-form-quantity": "Количество", "items-to-send-form-price": "Цена", "items-to-send-form-price-overridden-in-price-list-applicable-to-this-order": "Цена была изменена в прайс-листе, применимом к этому заказу.", "refund-amount-form-cancel-editing-refund-amount": "Отменить редактирование суммы возврата", "refund-amount-form-edit-refund-amount": "Редактировать сумму возврата", "refund-amount-form-refund-amount-cannot-be-negative": "Сумма возврата не может быть отрицательной", "refund-amount-form-the-refund-amount-must-be-at-least-0": "Сумма возврата должна быть не менее 0", "reservation-indicator-awaiting-reservation-count": "{{awaitingReservation}} предметы не зарезервированы", "reservation-indicator-this-item-has-been-fulfilled": "Этот предмет был выполнен.", "edit-reservation-button-quantity-item-location-name": "{{quantity}} предмет: ${{locationName}}", "reservation-indicator-edit-reservation": "Редактировать резервирование", "rma-summaries-claimed-items": "Заявленные предметы", "rma-summaries-replacement-items": "Сменные предметы", "rma-summaries-customer-refund-description": "Клиент получит полный возврат средств за заявленные предметы, так как стоимость замены предметов и доставки вычтена не будет. В качестве альтернативы вы можете установить индивидуальную сумму возврата при получении возвращенных предметов или вместо этого произвести обмен.", "rma-summaries-refund-amount": "Сумма возврата", "rma-summaries-the-customer-will-be-refunded-once-the-returned-items-are-received": "Клиенту будет возвращена сумма, как только возвращенные предметы будут получены", "rma-summaries-the-customer-will-be-refunded-immediately": "Клиенту будут возвращены деньги немедленно", "rma-summaries-receiving": "Получение", "rma-summaries-free": "Бесплатно", "send-notification-form-return": "возврат", "send-notification-form-exchange": "обмен", "send-notification-form-claim": "претензия", "send-notification-form-send-notifications": "Отправить уведомления", "send-notification-form-if-unchecked-the-customer-will-not-receive-communication": "Если флажок снят, клиент не получит сообщения об этом {{subject}}.", "shipping-address-form-shipping-address": "Адрес доставки", "shipping-address-form-ship-to-a-different-address": "Отправить на другой адрес", "shipping-address-form-cancel": "Отменить", "shipping-address-form-save-and-go-back": "Сохранить и вернуться", "shipping-address-form-shipping-information": "Информация о доставке", "shipping-form-shipping-for-return-items": "Доставка для возвратных предметов", "shipping-form-shipping-for-replacement-items": "Доставка предметов для замены", "shipping-form-shipping-method-is-required": "Необходимо указать способ доставки", "shipping-form-choose-shipping-method": "Выбрать способ доставки", "shipping-form-shipping-method": "Способ доставки", "shipping-form-add-custom-price": "Добавить индивидуальную цену", "shipping-form-return-shipping-for-items-claimed-by-the-customer-is-complimentary": "Обратная доставка предметов, заявленных клиентом, осуществляется бесплатно.", "shipping-form-shipping-for-replacement-items-is-complimentary": "Доставка предметов для замены осуществляется бесплатно.", "components-decrease-quantity": "Уменьшить количество", "components-increase-quantity": "Увеличьте количество", "details-successfully-updated-address": "Адрес успешно обновлен", "details-billing-address": "Платежный адрес", "details-shipping-address": "Адрес доставки", "details-contact": "Кон<PERSON><PERSON><PERSON>т", "details-location": "Местоположение", "claim-are-you-sure-you-want-to-close": "Вы уверены, что хотите закрыть?", "claim-you-have-unsaved-changes-are-you-sure-you-want-to-close": "У вас есть несохраненные изменения. Вы уверены, что хотите закрыть?", "claim-please-select-a-reason": "Пожалуйста, выберите причину", "claim-a-shipping-method-for-replacement-items-is-required": "Требуется указать способ доставки заменяемых предметов", "claim-successfully-created-claim": "Заявка успешно создана.", "claim-created": "Заявка на заказ #{{display_id}} была успешно создана", "claim-error-creating-claim": "Ошибка при создании заявки.", "claim-create-claim": "Создать заявку", "claim-location": "Местоположение", "claim-choose-which-location-you-want-to-return-the-items-to": "Выберите, куда вы хотите вернуть предметы.", "claim-select-location-to-return-to": "Выберите местоположение, в которое нужно вернуть", "claim-cancel": "Отменить", "claim-submit-and-close": "Отправить и закрыть", "create-fulfillment-error": "Ошибка", "create-fulfillment-please-select-a-location-to-fulfill-from": "Пожалуйста, выберите место для выполнения из", "create-fulfillment-cant-allow-this-action": "Невозможно разрешить это действие", "create-fulfillment-trying-to-fulfill-more-than-in-stock": "Попытка выполнить больше, чем есть в наличии", "create-fulfillment-successfully-fulfilled-order": "Успешно выполненный заказ", "create-fulfillment-successfully-fulfilled-swap": "Успешно выполнен обмен", "create-fulfillment-successfully-fulfilled-claim": "Заявка успешно удовлетворена", "create-fulfillment-success": "Успешно", "create-fulfillment-cancel": "Отменить", "create-fulfillment-create-fulfillment": "Создать выполнение", "create-fulfillment-create-fulfillment-title": "Создать выполнение", "create-fulfillment-locations": "Местоположения", "create-fulfillment-choose-where-you-wish-to-fulfill-from": "Выберите, откуда вы хотите выполнить заказ.", "create-fulfillment-items-to-fulfill": "Предметы для выполнения", "create-fulfillment-select-the-number-of-items-that-you-wish-to-fulfill": "Выберите количество предметов, которые хотите выполнить.", "create-fulfillment-send-notifications": "Отправлять уведомления", "create-fulfillment-when-toggled-notification-emails-will-be-sent": "При включении будут отправляться уведомления по электронной почте.", "create-fulfillment-quantity-is-not-valid": "Недопустимое количество", "detail-cards-allocated": "Выделено", "detail-cards-not-fully-allocated": "Не полностью выделено", "detail-cards-subtotal": "Промежуточный итог", "detail-cards-shipping": "Доставка", "detail-cards-tax": "Налог", "detail-cards-total": "Общий", "detail-cards-edit-order": "Редактировать заказ", "detail-cards-allocate": "Выделить", "detail-cards-discount": "Скидка:", "detail-cards-original-total": "Исходная сумма", "details-successfully-updated-the-email-address": "Адрес электронной почты успешно обновлен.", "details-email-address": "Адрес электронной почты", "details-save": "Сохранить", "details-order-id-copied": "ID заказа скопирован.", "details-email-copied": "Электронная почта скопирована.", "details-cancel-order-heading": "Отменить заказ", "details-are-you-sure-you-want-to-cancel-the-order": "Вы уверены, что хотите отменить заказ?", "order-details-display-id": "заказ #{{display_id}}", "details-successfully-canceled-order": "Заказ успешно отменен", "details-go-to-customer": "Перейти к клиенту", "details-transfer-ownership": "Передача владения", "details-edit-shipping-address": "Редактировать адрес доставки", "details-edit-billing-address": "Редактировать платежный адрес", "details-edit-email-address": "Редактировать адрес электронной почты", "details-back-to-orders": "Вернуться к заказам", "details-cancel-order": "Отменить заказ", "details-payment": "Пла<PERSON><PERSON><PERSON>", "details-refunded": "Возвращен", "details-total-paid": "Итого", "details-fulfillment": "Выполнено", "details-create-fulfillment": "Создать выполнение", "details-shipping-method": "Способ доставки", "details-customer": "Кли<PERSON><PERSON>т", "details-shipping": "Доставка", "details-billing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "details-raw-order": "Необработанный заказ", "mark-shipped-successfully-marked-order-as-shipped": "Заказ успешно помечен как доставлен", "mark-shipped-successfully-marked-swap-as-shipped": "Замена успешно помечена как доставлена", "mark-shipped-successfully-marked-claim-as-shipped": "Заявка успешно помечана как доставленая.", "mark-shipped-success": "Успешно", "mark-shipped-error": "Ошибка", "mark-shipped-mark-fulfillment-shipped": "Доставка помечана выполненой", "mark-shipped-tracking": "Отслеживание", "mark-shipped-tracking-number-label": "Номер отслеживания", "mark-shipped-tracking-number": "Номер отслеживания...", "mark-shipped-add-additional-tracking-number": "+ Добавить дополнительный номер отслеживания", "mark-shipped-send-notifications": "Отправлять уведомления", "mark-shipped-cancel": "Отменить", "mark-shipped-complete": "Завершить", "order-line-warning": "Предупреждение", "order-line-cannot-duplicate-an-item-without-a-variant": "Невозможно дублировать предмет без варианта.", "order-line-error": "Ошибка", "order-line-failed-to-duplicate-item": "Не удалось продублировать предмет", "order-line-success": "Успешно", "order-line-item-removed": "Предмет удален", "order-line-failed-to-remove-item": "Не удалось удалить предмет", "order-line-item-added": "Предмет добавлен", "order-line-failed-to-replace-the-item": "Не удалось заменить предмет", "order-line-replace-product-variants": "Заменить варианты товара", "order-line-replace-with-other-item": "Заменить другим предметом", "order-line-duplicate-item": "Продублировать предмет", "order-line-remove-item": "Удалить предмет", "order-line-line-item-cannot-be-edited": "Эта позиция является частью выполнения и не может быть отредактирована. Отмените выполнение, чтобы отредактировать позицию.", "order-line-new": "Новое", "order-line-modified": "Модифицированный", "receive-return-please-select-at-least-one-item-to-receive": "Пожалуйста, выберите хотя бы один предмет для получения", "receive-return-successfully-received-return": "Возврат успешно получен", "receive-return-received-return-for-order": "Получен возврат заказа #{{display_id}}", "receive-return-failed-to-receive-return": "Не удалось получить возврат", "receive-return-receive-return": "Получить возврат", "receive-return-location": "Местоположение", "receive-return-choose-location": "Выберите, в какое место вы хотите вернуть предметы.", "receive-return-select-location-to-return-to": "Выберите местоположение, в которое нужно вернуть", "receive-return-no-inventory-levels-exist-for-the-items-at-the-selected-location": "Для товаров в выбранном местоположении не существует уровней запасов", "receive-return-cancel": "Отменить", "receive-return-save-and-close": "Сохранить и закрыть", "refund-success": "Успешно", "refund-successfully-refunded-order": "Заказ успешно возвращен", "refund-error": "Ошибка", "refund-create-a-refund": "Создать возврат", "refund-attention": "Внимание!", "refund-system-payment-disclaimer": "Один или несколько ваших платежей являются системными. Имейте в виду, что Medusa не занимается фиксацией и возвратом средств по таким платежам.", "refund-details": "Подробности", "refund-cannot-refund-more-than-the-orders-net-total": "Мы не можем вернуть сумму, превышающую чистую сумму заказа.", "refund-discount": "Скидка", "refund-reason": "Причина", "refund-note": "Примечание", "refund-discount-for-loyal-customer": "Скидка для постоянных клиентов", "refund-send-notifications": "Отправлять уведомления", "refund-cancel": "Отменить", "refund-complete": "Завершить", "reservation-reservation-was-deleted": "Резервация удалена", "reservation-the-allocated-items-have-been-released": "Выделенные предметы были освобождены.", "reservation-error": "Ошибка", "reservation-failed-to-delete-the-reservation": "Не удалось удалить резервацию. ", "reservation-reservation-was-updated": "Резервирование обновлено", "reservation-the-reservation-change-was-saved": "Изменение резервации сохранено.", "reservation-errors": "Ошибки", "reservation-failed-to-update-reservation": "Не удалось обновить резервирование", "reservation-edit-reservation": "Редактировать резервирование", "reservation-location": "Местоположение", "reservation-choose-which-location-you-want-to-ship-the-items-from": "Выберите, из какого места вы хотите отправить предметы.", "reservation-items-to-allocate-title": "Предметы для распределения", "reservation-select-the-number-of-items-that-you-wish-to-allocate": "Выберите количество предметов, которые вы хотите распределить.", "reservation-max-reservation-requested": " / {{maxReservation}} запрошено", "reservation-reserved": " зарезервировано", "reservation-description": "Описание", "reservation-what-type-of-reservation-is-this": "Что это за тип резервирования?", "reservation-metadata": "Метаданные", "reservation-remove-metadata": "Удалить метаданные", "reservation-add-metadata": "Добавить метаданные", "reservation-delete-reservation": "Удалить резервирование", "reservation-cancel": "Отменить", "reservation-save-and-close": "Сохранить и закрыть", "reservation-couldnt-allocate-items": "Не удалось выделить предметы", "reservation-items-allocated": "Выделенные предметы", "reservation-items-have-been-allocated-successfully": "Предметы были успешно распределены", "reservation-save-reservation": "Сохранить резервирование", "reservation-loading": "Загрузка...", "reservation-allocate-order-items": "Распределить предметы заказа", "reservation-choose-where-you-wish-to-allocate-from": "Выберите, откуда вы хотите выделить", "reservation-items-to-allocate": "Предметы для распределения", "returns-success": "Успешно", "returns-successfully-returned-order": "Заказ успешно возвращен", "returns-error": "Ошибка", "returns-request-return": "Запросить возврат", "returns-items-to-return": "Предметы возврата", "returns-choose-which-location-you-want-to-return-the-items-to": "Выберите, куда вы хотите вернуть предметы.", "returns-select-location-to-return-to": "Выберите местоположение, в которое нужно вернуть", "returns-selected-location-has-no-inventory-levels": "В выбранном местоположении нет уровней запасов для выбранных товаров. Возврат можно запросить, но он не может быть получен до тех пор, пока не будет создан уровень запасов для выбранного местоположения.", "returns-shipping": "Доставка", "returns-choose-retur,-shipping-method": "Выберите, какой способ доставки вы хотите использовать для этого возврата.", "returns-total-refund": "Общий возврат", "returns-amount": "Сумма", "returns-send-notifications": "Отправлять уведомления", "returns-notify-customer-of-created-return": "Уведомить клиента о созданном возврате", "returns-back": "Назад", "returns-submit": "Отправить", "rma-sub-modals-search-for-additional": "Поиск дополнительных", "rma-sub-modals-general": "Общие", "rma-sub-modals-first-name": "Имя", "rma-sub-modals-last-name": "Фамилия", "rma-sub-modals-phone": "Телефон", "rma-sub-modals-shipping-address": "Адрес доставки", "rma-sub-modals-address-1": "Адрес 1", "rma-sub-modals-address-2": "Адрес 2", "rma-sub-modals-province": "Область", "rma-sub-modals-postal-code": "Почтовый индекс", "rma-sub-modals-city": "Город", "rma-sub-modals-country": "Страна", "rma-sub-modals-back": "Назад", "rma-sub-modals-add": "Добавить", "rma-sub-modals-name": "Имя", "rma-sub-modals-status": "Статус", "rma-sub-modals-in-stock": "В наличии", "rma-sub-modals-products": "Товары", "rma-sub-modals-search-products": "Поиск товаров..", "rma-sub-modals-reason-for-return": "Причина возврата", "rma-sub-modals-reason": "Причина", "rma-sub-modals-note": "Примечание", "swap-success": "Успешно", "swap-successfully-created-exchange": "Обмен успешно создан", "swap-error": "Ошибка", "swap-register-exchange": "Регистрация обмена", "swap-items-to-return": "Предметы возврата", "swap-shipping": "Доставка", "swap-shipping-method": "Способ доставки", "swap-add-a-shipping-method": "Добавить способ доставки", "swap-location": "Местоположение", "swap-choose-which-location-you-want-to-return-the-items-to": "Выберите, куда вы хотите вернуть предметы.", "swap-select-location-to-return-to": "Выберите местоположение, в которое нужно вернуть", "swap-items-to-send": "Предметы для отправки", "swap-add-product": "Добавить товар", "swap-return-total": "Общий возврат", "swap-additional-total": "Дополнительный итог", "swap-outbond-shipping": "Исходящая доставка", "swap-calculated-at-checkout": "Рассчитывается при оформлении заказа", "swap-estimated-difference": "Предполагаемая разница", "swap-send-notifications": "Отправлять уведомления", "swap-if-unchecked-the-customer-will-not-receive-communication-about-this-exchange": "Если флажок снят, клиент не получит сообщения об этом обмене.", "swap-complete": "Завершить", "templates-shipped": "Доставлен", "templates-fulfilled": "Выполнен", "templates-canceled": "Отменен", "templates-partially-fulfilled": "Частично выполнен", "templates-fulfillment-status-requires-action": "Требуется действие", "templates-awaiting-fulfillment": "Ожидается выполнение", "templates-partially-shipped": "Частично доставлен", "templates-cancel-fulfillment-heading": "Отменить выполнение?", "templates-are-you-sure-you-want-to-cancel-the-fulfillment": "Вы уверены, что хотите отменить выполнение?", "templates-successfully-canceled-swap": "Обмен успешно отменен.", "templates-error": "Ошибка", "templates-successfully-canceled-claim": "Заявка успешно отменена", "templates-successfully-canceled-fulfillment": "Выполнение успешно отменено", "templates-fulfillment-has-been-canceled": "Выполнение отменено", "templates-fulfilled-by-provider": "{{title}} Выполнен {{provider}}", "templates-not-shipped": "Не доставлен", "templates-tracking": "Отслеживание", "templates-shipped-from": "Доставлено из", "templates-shipping-from": "Доставка из", "templates-mark-shipped": "Отметить доставленным", "templates-cancel-fulfillment": "Отменить выполнение", "templates-completed": "Завершено", "templates-processing": "Обработка", "templates-requires-action": "Требуется действие", "templates-capture-payment": "Получение платежа", "templates-successfully-captured-payment": "Платеж успешно зачислен", "templates-refund": "Возврат денег", "templates-total-for-swaps": "Итого по обменам", "templates-refunded-for-swaps": "Возврат средств за обмен", "templates-refunded-for-returns": "Возврат средств за возврат", "templates-manually-refunded": "Возврат средств вручную", "templates-net-total": "Чистая сумма", "templates-paid": "Оплачено", "templates-awaiting-payment": "Ожидание оплаты", "templates-payment-status-requires-action": "Требуется действие", "draft-orders-completed": "Завершено", "draft-orders-open": "Открыть", "draft-orders-mark-as-paid": "Отметить как оплачено", "draft-orders-success": "Успешно", "draft-orders-successfully-mark-as-paid": "Успешно отмечено как оплачено", "draft-orders-error": "Ошибка", "draft-orders-successfully-canceled-order": "Заказ успешно отменен", "draft-orders-back-to-draft-orders": "Вернуться к черновым заказам", "on-mark-as-paid-confirm-order-id": "Заказ #{{display_id}}", "draft-orders-go-to-order": "Перейти к заказу", "draft-orders-cancel-draft-order": "Отменить черновой заказ", "draft-orders-draft-order": "Черновой заказа", "draft-orders-email": "Электронная почта", "draft-orders-phone": "Телефон", "draft-orders-amount": "Сумма {{currency_code}}", "draft-orders-payment": "Оплата", "draft-orders-subtotal": "Промежуточный итог", "draft-orders-shipping": "Доставка", "draft-orders-tax": "Налог", "draft-orders-total-to-pay": "Общая сумма к оплате", "draft-orders-payment-link": "Ссылка для оплаты:", "draft-orders-configure-payment-link-in-store-settings": "Настройте ссылку для оплаты в настройках магазина", "draft-orders-shipping-method": "Способ доставки", "draft-orders-data": "Данные", "draft-orders-1-item": "(1 предмет)", "draft-orders-customer": "Кли<PERSON><PERSON>т", "draft-orders-edit-shipping-address": "Редактировать адрес доставки", "draft-orders-edit-billing-address": "Редактировать платежный адрес", "draft-orders-go-to-customer": "Перейти к Клиенту", "draft-orders-contact": "Кон<PERSON><PERSON><PERSON>т", "draft-orders-billing": "Выставление счетов", "draft-orders-raw-draft-order": "Необработанный черновой заказ", "draft-orders-are-you-sure": "Вы уверены?", "draft-orders-remove-resource-heading": "Удалить {{resource}}", "draft-orders-remove-resource-success-text": "{{resource}} был удален", "draft-orders-this-will-create-an-order-mark-this-as-paid-if-you-received-the-payment": "Это создаст заказ. Отметьте это как оплаченное, если вы получили платеж.", "draft-orders-mark-paid": "Отметить оплаченым", "draft-orders-cancel": "Отменить", "draft-orders-create-draft-order": "Создать черновой заказ", "edit-amount-paid": "Выплаченная сумма", "edit-new-total": "Новый итог", "edit-difference-due": "Разница из-за", "edit-back": "Назад", "edit-save-and-go-back": "Сохранить и вернуться", "edit-order-edit-set-as-requested": "Набор для редактирования заказа в соответствии с запросом", "edit-failed-to-request-confirmation": "Не удалось запросить подтверждение", "edit-added-successfully": "Успешно добавлено", "edit-error-occurred": "Возникла ошибка", "edit-add-product-variants": "Добавить варианты товара", "edit-edit-order": "Редактировать заказ", "edit-items": "Предметы", "edit-add-items": "Добавить предметы", "edit-filter-items": "Фильтровать предметы...", "edit-note": "Примечание", "edit-add-a-note": "Добавить примечание...", "variants-table-location": " в {{location}}", "edit-product": "<PERSON><PERSON><PERSON><PERSON>", "edit-in-stock": "В наличии", "edit-price": "Цена", "edit-products": "Товары", "edit-search-product-variants": "Поиск вариантов товара...", "orders-success": "Успешно", "orders-successfully-initiated-export": "Экспорт успешно начат", "orders-error": "Ошибка", "orders-export-orders": "Экспортные заказы", "components-billing-address": "Платежный адрес", "components-use-same-as-shipping": "Используйте то же, что и доставку", "components-e-g-gift-wrapping": "Например, подарочная упаковка", "components-title": "Заголовок", "components-price": "Цена", "components-quantity": "Количество", "components-back": "Назад", "components-add": "Добавить", "components-items-for-the-order": "Предметы для заказа", "components-details": "Подробности", "components-price-excl-taxes": "Цена (без налогов)", "components-add-custom": "Добавить индивидуальный", "components-add-existing": "Добавить существующий", "components-add-products": "Добавить товары", "components-add-custom-item": "Добавить индивидуальный предмет", "components-choose-region": "Выберите регион", "components-region": "Регион", "select-shipping-to-name": "(К {{name}})", "components-attention": "Внимание!", "components-no-options-for-orders-without-shipping": "У вас нет никаких вариантов для заказов без доставки. Пожалуйста, добавьте один (например, \"Выполнение заказа в магазине\"), сняв флажок \"Показывать на веб-сайте\" в настройках региона, и продолжайте.", "components-choose-a-shipping-method": "Выбрать способ доставки", "components-set-custom-price": "Установить индивидуальную цену", "components-custom-price": "Индивидуальная цена", "components-customer-and-shipping-details": "Подробности о клиенте и доставке", "components-find-existing-customer": "Найти существующего клиента", "components-email": "Электронная почта", "components-choose-existing-addresses": "Выбрать существующие адреса", "components-create-new": "Создавать новое", "components-the-discount-is-not-applicable-to-the-selected-region": "Скидка не распространяется на выбранный регион.", "components-the-discount-code-is-invalid": "Код скидки недействителен.", "components-add-discount": "Добавить скидку", "components-summer-10": "SUMMER10", "components-discount": "Скидка", "select-shipping-code": "(Код: {{code}})", "components-type": "Тип", "components-value": "Ценность", "components-address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components-shipping-method": "Способ доставки", "components-billing-details": "Платежные реквизиты", "components-edit": "Редактировать", "form-use-new-order-form-must-be-used-within-new-order-form-provider": "useNewOrderForm должен использоваться внутри NewOrderFormProvider.", "new-order-created": "Заказ создан", "new-create-draft-order": "Создать черновой заказ", "batch-job-price-list-prices": "Цены прайс-листа", "batch-job-upload-a-csv-file-with-variants": "Загрузите CSV-файл с вариантами и ценами, чтобы обновить прайс-лист. Обратите внимание, что все существующие цены будут удалены.", "batch-job-unsure-about-how-to-arrange-your-list": "Не знаете, как составить список?", "batch-job-download-the-template-file-below-and-update-your-prices": "Загрузите файл шаблона ниже и обновите цены.", "details-back-to-pricing": "Вернуться к Ценам", "details-raw-price-list": "Необработанный прайс-лист", "sections-customer-groups": "Группы клиентов", "sections-last-edited": "Последнее редактирование", "sections-price-overrides": "Изменение цены", "sections-more": "более", "sections-delete-price-list-heading": "Удалить прайс-лист", "sections-are-you-sure-you-want-to-delete-this-price-list": "Вы уверены, что хотите удалить этот прайс-лист?", "sections-success": "Успешно", "sections-price-list-deleted-successfully": "Прайс-лист успешно удален", "sections-edit-price-list-details": "Редактировать сведения о прайс-листе", "sections-delete-price-list": "Удалить прайс-лист", "edit-prices-overrides-edit-price-overrides": "Редактировать переопределения цен", "edit-prices-overrides-success": "Успешно", "edit-prices-overrides-price-overrides-updated": "Переопределения цен обновлены", "edit-prices-overrides-cancel": "Отменить", "edit-prices-overrides-save": "Сохранить", "edit-prices-overrides-count_one": "{{count}}", "edit-prices-overrides-count_other": "{{count}}", "edit-prices-overrides-add-prices": "Добавить цены", "prices-details-edit-prices": "Редактировать цены", "prices-details-prices": "Цены", "prices-details-you-will-be-able-to-override-the-prices-for-the-products-you-add-here": "Вы сможете редактировать цены на товары, которые вы добавляете здесь", "prices-details-remove-from-list": "Удалить из списка", "prices-details-edit-manually": "Редактировать вручную", "prices-details-import-price-list": "Прайс-лист на импорт", "prices-table-search-by-name-or-sku": "Поиск по названию или артикулу...", "prices-table-edit-prices": "Редактировать цены", "prices-table-remove-product": "Удалить товар", "prices-table-success": "Успешно", "prices-table-deleted-prices-of-product": "Удаленные цены на товар: {{title}}", "prices-table-error": "Ошибка", "prices-table-name": "Имя", "prices-table-collection": "Коллекция", "prices-table-no-collection": "Нет коллекции", "prices-table-variants": "Варианты", "pricing-add-price-list": "Добавить прайс-лист", "pricing-price-lists": "Прайс-листы", "form-header-error": "Ошибка", "form-header-success": "Успешно", "form-header-successfully-updated-price-list": "Прайс-лист успешно обновлен.", "form-header-publish-price-list": "Опубликовать прайс-лист", "form-header-save-as-draft": "Сохранить как черновик", "form-header-save-changes": "Сохранить изменения", "form-header-cancel": "Отменить", "pricing-form-create-new-price-list": "Create new price list", "pricing-form-edit-price-list": "Редактировать прайс-лист", "sections-configuration": "Конфигурация", "sections-optional-configuration-for-the-price-list": "Дополнительная конфигурация для прайс-листа", "sections-price-overrides-time-application": "Изменения цен применяются с момента нажатия кнопки публикации и навсегда, если их не менять.", "sections-price-overrides-has-a-start-date": "У изменения цен есть дата начала?", "sections-schedule-the-price-overrides-to-activate-in-the-future": "Запланируйте переопределение цен для активации в будущем.", "sections-price-overrides-has-an-expiry-date": "У переопределения цены есть срок действия?", "sections-schedule-the-price-overrides-to-deactivate-in-the-future": "Запланируйте переопределение цен для деактивации в будущем.", "sections-end-date": "Дата окончания", "sections-customer-availabilty": "Доступность клиентов", "sections-specifiy-which-customer-groups-the-price-overrides-should-apply-for": "Укажите, для каких групп клиентов следует применять переопределение цен.", "sections-customer-groups-label": "Группы клиентов", "sections-general": "Общий", "sections-general-information-for-the-price-list": "Общая информация по прайс-листу.", "sections-name": "Имя", "sections-b-2-b-black-friday": "B2B, Черная пятница...", "sections-for-our-business-partners": "Для наших деловых партнеров...", "sections-tax-inclusive-prices": "Цены с учетом налогов", "sections-choose-to-make-all-prices-in-this-list-inclusive-of-tax": "Выберите, чтобы все цены в этом списке включали налог.", "sections-prices": "Цены", "sections-you-will-be-able-to-override-the-prices-for-the-products-you-add-here": "Вы сможете изменить цены на товары, которые вы добавляете здесь", "sections-define-the-price-overrides-for-the-price-list": "Определите переопределения цен для прайс-листа", "sections-edit-prices-label": "Редактировать цены", "sections-remove-from-list": "Удалить из списка", "sections-search-by-name-or-sku": "Поиск по названию или артикулу...", "sections-edit-prices": "Редактировать цены", "sections-price-list-type": "Тип прайс-листа", "sections-select-the-type-of-the-price-list": "Выберите тип прайс-листа", "sections-sale-prices-compare-to-price-override": "В отличие от цен на распродажу, переопределение цены не оповестит клиента о том, что цена является частью распродажи.", "sections-sale": "Распродажа", "sections-use-this-if-you-are-creating-prices-for-a-sale": "Используйте это, если вы создаете цены для распродажи.", "sections-override": "Переопределение", "sections-use-this-to-override-prices": "Используйте это для переопределения цен.", "components-success": "Успешно", "components-successfully-updated-category-tree": "Дерево категорий успешно обновлено.", "components-error": "Ошибка", "components-failed-to-update-category-tree": "Не удалось обновить дерево категорий", "components-delete": "Удалить", "components-category-deleted": "Категория удалена", "components-category-deletion-failed": "Удаление категории не удалось", "components-category-status-is-inactive": "Статус категории неактивен", "components-category-visibility-is-private": "Доступность категории конфиденциальна", "components-add-category-item-to": "Добавить предмет категории в", "modals-public": "Общественный", "modals-private": "Частный", "modals-active": "Активный", "modals-inactive": "Неактивный", "modals-success": "Успешно", "modals-successfully-created-a-category": "Категория успешно создана", "modals-failed-to-create-a-new-category": "Не удалось создать новую категорию", "modals-error": "Ошибка", "modals-save-category": "Сохранить категорию", "modals-add-category-to": "Добавить категорию в {{name}}", "modals-add-category": "Добавить категорию", "modals-details": "Подробности", "modals-name": "Имя", "modals-give-this-category-a-name": "Дайте этой категории название", "modals-handle": "Путь", "modals-custom-handle": "Пользовательский путь", "modals-description": "Описание", "modals-give-this-category-a-description": "Дайте этой категории описание", "modals-status": "Статус", "modals-visibility": "Видимость", "modals-successfully-updated-the-category": "Категория успешно обновлена", "modals-failed-to-update-the-category": "Не удалось обновить категорию", "modals-edit-product-category": "Редактировать категорию продукта", "modals-cancel": "Отменить", "modals-save-and-close": "Сохранить и закрыть", "pages-no-product-categories-yet": "Категории продуктов пока нет. Используйте кнопку выше, чтобы создать свою первую категорию.", "pages-add-category": "Добавить категорию", "pages-product-categories": "Категории товаров", "pages-helps-you-to-keep-your-products-organized": "Помогает вам поддерживать порядок в ваших товарах.", "batch-job-success": "Успешно", "batch-job-import-confirmed-for-processing-progress-info-is-available-in-the-activity-drawer": "Импорт подтвержден для обработки. Информация о прогрессе доступна в ящике активности.", "batch-job-error": "Ошибка", "batch-job-import-failed": "Импорт не удался", "batch-job-failed-to-delete-the-csv-file": "Не удалось удалить CSV-файл", "batch-job-failed-to-cancel-the-batch-job": "Не удалось отменить пакетное задание", "batch-job-products-list": "список товаров", "batch-job-download-template": "Загрузите шаблон ниже, чтобы убедиться, что вы используете правильный формат.", "batch-job-imports-description": "С помощью импорта вы можете добавлять или обновлять товары. Чтобы обновить существующие товары/варианты, вы должны задать существующий идентификатор в столбцах Товар/Вариант id. Если значение не задано, будет создана новая запись. Перед импортом товара у вас будет запрошено подтверждение.", "products-filters": "Фильтры", "products-status": "Статус", "products-tags": "Теги", "products-spring-summer": "Весна, лето...", "new-sales-channels": "Каналы продаж", "new-this-product-will-only-be-available-in-the-default-sales-channel-if-left-untouched": "Этот товар будет доступен только в канале продаж по умолчанию, если его оставить нетронутым.", "new-change-availablity": "Изменить доступность", "add-variants-a-variant-with-these-options-already-exists": "Вариант с такими опциями уже существует.", "add-variants-product-options": "Варианты товара", "add-variants-options-are-used-to-define-the-color-size-etc-of-the-product": "Опции используются для определения цвета, размера и т.д. товара.", "add-variants-option-title": "Название опции", "add-variants-variations-comma-separated": "Варианты (через запятую)", "add-variants-color": "Цвет...", "add-variants-already-exists": "уже существует", "add-variants-blue-red-black": "<PERSON><PERSON><PERSON><PERSON>, красный, черный...", "add-variants-add-an-option": "Добавить опцию", "add-variants-product-variants": "Варианты товара", "add-variants-you-must-add-at-least-one-product-option-before-you-can-begin-adding-product-variants": "Вы должны добавить по крайней мере один вариант товара, прежде чем сможете начать добавлять варианты товара.", "add-variants-variant": "Вар<PERSON><PERSON><PERSON>т", "add-variants-inventory": "Инвентарь", "add-variants-add-a-variant": "Добавьте вариант", "add-variants-create-variant": "Создать вариант", "add-variants-cancel": "Отменить", "add-variants-save-and-close": "Сохранить и закрыть", "new-variant-a-variant-with-these-options-already-exists": "Вариант с этими опциями уже существует.", "new-variant-are-you-sure-you-want-to-delete-this-variant": "Вы уверены, что хотите удалить этот вариант?", "new-variant-delete-variant": "Удалить вариант", "new-variant-edit": "Редактировать", "new-variant-delete": "Удалить", "new-variant-edit-variant": "Редактировать вариант", "new-variant-cancel": "Отменить", "new-variant-save-and-close": "Сохранить и закрыть", "new-something-went-wrong-while-trying-to-upload-images": "Что-то пошло не так при попытке загрузить изображения.", "new-no-file-service-configured": "Возможно, у вас не настроена файловая служба. Пожалуйста, свяжитесь со своим администратором", "new-upload-thumbnail-error": "Что-то пошло не так при попытке загрузить миниатюру.", "new-save-as-draft": "Сохранить как черновик", "new-publish-product": "Опубликовать товар", "new-general-information-title": "Основная информация", "new-to-start-selling-all-you-need-is-a-name-and-a-price": "Чтобы начать продавать, все, что вам нужно, - это название и цена.", "new-organize-product": "Организовать товар", "new-add-variations-of-this-product": "Добавьте варианты этого товара.", "new-offer-your-customers-different-options-for-color-format-size-shape-etc": "Предложите своим клиентам различные варианты цвета, формата, размера, формы и т.д.", "new-used-for-shipping-and-customs-purposes": "Используется для транспортных и таможенных целей.", "new-dimensions": "Размеры", "new-customs": "Таможня", "new-used-to-represent-your-product-during-checkout-social-sharing-and-more": "Используется для представления вашего товара при оформлении заказа, публикации в социальных сетях и многого другого.", "new-media": "Медиа", "new-add-images-to-your-product": "Добавьте изображения к своему продукту.", "overview-import-products": "Импортировать товары", "overview-export-products": "Экспортировать товары", "overview-new-product": "Новый товар", "overview-new-collection": "Новая коллекция", "overview-success": "Успешно", "overview-successfully-created-collection": "Коллекция успешно создана", "overview-error": "Ошибка", "overview-successfully-initiated-export": "Экспорт успешно начат", "modals-add-sales-channels": "Добавьте каналы продаж", "modals-find-channels": "Найти каналы", "modals-updated-the-api-key": "Обновлен ключ API", "modals-failed-to-update-the-api-key": "Не удалось обновить ключ API.", "modals-edit-api-key-details": "Редактировать сведения о ключе API", "modals-title": "Заголовок", "modals-name-your-key": "Назовите свой ключ", "modals-sales-channels-added-to-the-scope": "Каналы продаж добавлены в объем", "modals-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "Произошла ошибка при добавлении каналов продаж в область действия ключа.", "modals-add-and-go-back": "Добавить и вернуться назад", "modals-add-and-close": "Добавить и закрыть", "modals-sales-channels-removed-from-the-scope": "Каналы продаж исключены из области действия", "modals-error-occurred-while-removing-sales-channels-from-the-scope-of-the-key": "Произошла ошибка при удалении каналов продаж из области действия ключа.", "modals-edit-sales-channels": "Редактировать каналы продаж", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_one": "{{count}}", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_other": "{{count}}", "modals-deselect": "Отменить выбор", "modals-remove": "Удалить", "modals-add-channels": "Добавить каналы", "modals-close": "Закрыть", "pages-sales-channels": "Каналы продаж", "pages-connect-as-many-sales-channels-to-your-api-key-as-you-need": "Подключите к вашему API-ключу столько каналов продаж, сколько вам нужно.", "pages-add-sales-channels": "Добавьте каналы продаж", "pages-edit-sales-channels": "Редактировать каналы продаж", "pages-success": "Успешно", "pages-created-a-new-api-key": "Создан новый ключ API", "pages-error": "Ошибка", "pages-failed-to-create-a-new-api-key": "Не удалось создать новый ключ API.", "pages-sales-channels-added-to-the-scope": "Каналы продаж добавлены в объем", "pages-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "Произошла ошибка при добавлении каналов продаж в область действия ключа", "pages-publish-api-key": "Опубликовать ключ API", "pages-create-api-key": "Создать ключ API", "pages-create-and-manage-api-keys-right-now-this-is-only-related-to-sales-channels": "Создавайте ключи API и управляйте ими. Сейчас это касается только каналов продаж.", "pages-create-api-key-label": "Создать ключ API", "pages-back-to-settings": "Вернуться к настройкам", "pages-publishable-api-keys": "Публикуемые ключи API", "pages-these-publishable-keys-will-allow-you-to-authenticate-api-requests": "Эти публикуемые ключи позволят вам аутентифицировать запросы API.", "tables-name": "Имя", "tables-token": "Токен", "tables-done": "сделано", "tables-copy-to-clipboard": "Скопировать в буфер обмена", "tables-created": "Создано", "tables-status": "Статус", "tables-revoked": "Анну<PERSON>ировано", "tables-live": "Акти<PERSON><PERSON>н", "tables-edit-api-key-details": "Редактировать сведения о ключе API", "tables-edit-sales-channels": "Редактирование каналов продаж", "tables-copy-token": "Скопировать токен", "tables-revoke-token": "Отозвать токен", "tables-delete-api-key": "Удалить ключ API", "tables-yes-delete": "Да, удалить", "tables-api-key-deleted": "Ключ API удален", "tables-are-you-sure-you-want-to-delete-this-public-key": "Вы действительно хотите удалить этот открытый ключ?", "tables-delete-key": "Удалить ключ", "tables-yes-revoke": "Да, отозвать", "tables-api-key-revoked": "Ключ API отозван", "tables-are-you-sure-you-want-to-revoke-this-public-key": "Вы уверены, что хотите отозвать этот открытый ключ?", "tables-revoke-key": "Отозвать ключ", "tables-api-keys": "API-ключи", "tables-no-keys-yet-use-the-above-button-to-create-your-first-publishable-key": "Ключей пока нет, используйте кнопку выше, чтобы создать свой первый доступный для публикации ключ", "tables-title": "Заголовок", "tables-description": "Описание", "tables-no-added-sales-channels": "Нет добавленных каналов продаж", "tables-sales-channels": "Каналы продаж", "form-title": "Заголовок", "form-website-app-amazon-physical-store-pos-facebook-product-feed": "Веб-сай<PERSON>, приложение, Amazon, POS-терминал в физическом магазине, фид товаров на Facebook...", "form-description": "Описание", "form-available-products-at-our-website-app": "Доступные товары на нашем сайте, в приложении...", "form-success": "Успешно", "form-the-sales-channel-is-successfully-updated": "Канал продаж успешно обновлен", "form-error": "Ошибка", "form-failed-to-update-the-sales-channel": "Не удалось обновить канал продаж.", "form-sales-channel-details": "Подробности о канале продаж", "form-general-info": "Главная информация", "form-name": "Имя", "form-close": "Закрывать", "form-save": "Сохранить", "pages-draft": "Черновик", "pages-control-which-products-are-available-in-which-channels": "Контролируйте, какие продукты доступны по каким каналам", "pages-search-by-title-or-description": "Поиск по названию или описанию", "pages-confirm-delete-sales-channel": "Вы уверены, что хотите удалить этот канал продаж? Настройки, которые вы сделали, исчезнут навсегда.", "pages-delete-channel-heading": "Удалить канал", "pages-edit-general-info": "Редактировать общую информацию", "pages-add-products": "Добавить продукты", "pages-delete-channel": "Удалить канал", "pages-disabled": "Отключен", "pages-enabled": "Включено", "tables-collection": "Коллекция", "tables-start-building-your-channels-setup": "Начните создавать настройки своих каналов...", "tables-no-products-in-channels": "Вы еще не добавили товары в эти каналы, но как только вы это сделаете, они появятся здесь.", "tables-add-products": "Добавить товары", "tables-details": "Подробности", "tables-remove-from-the-channel": "Удалить с канала", "tables-products": "Товары", "sales-channels-table-placeholder-selected-with-counts_one": "{{count}}", "sales-channels-table-placeholder-selected-with-counts_other": "{{count}}", "tables-remove": "Удалить", "components-successfully-updated-currency": "Валюта успешно обновлена", "components-default": "По умолчанию", "default-store-currency-success": "Успешно", "default-store-currency-successfully-updated-default-currency": "Валюта по умолчанию успешно обновлена.", "default-store-currency-error": "Ошибка", "default-store-currency-default-store-currency": "Валюта магазина по умолчанию", "default-store-currency-this-is-the-currency-your-prices-are-shown-in": "Это валюта, в которой отображаются ваши цены.", "store-currencies-success": "Успешно", "store-currencies-successfully-updated-currencies": "Валюты успешно обновлены.", "store-currencies-error": "Ошибка", "store-currencies-cancel": "Отменить", "store-currencies-save-and-go-back": "Сохранить и вернуться", "store-currencies-save-and-close": "Сохранить и закрыть", "store-currencies-add-store-currencies": "Добавить валюты магазина", "store-currencies-current-store-currencies": "Текущие валюты магазина", "store-currencies-close": "Закрыть", "current-currencies-screen-selected-with-count_one": "{{count}}", "current-currencies-screen-selected-with-count_other": "{{count}}", "store-currencies-deselect": "Отменить выбор", "store-currencies-remove": "Удалить", "store-currencies-add-currencies": "Добавить валюты", "store-currencies-store-currencies": "Валюты магазина", "store-currencies-all-the-currencies-available-in-your-store": "Все валюты, доступные в вашем магазине.", "store-currencies-edit-currencies": "Редактировать валюты", "currencies-an-unknown-error-occurred": "Произошла неизвестная ошибка", "currencies-error": "Ошибка", "currencies-back-to-settings": "Вернуться к настройкам", "currencies-manage-the-markets-that-you-will-operate-within": "Управляйте рынками, на которых вы будете работать.", "currencies-include-or-exclude-taxes": "Решите, хотите ли вы включать или исключать налоги всякий раз, когда определяете цену в этой валюте", "currencies-tax-incl-prices": "Цены включая налоги", "settings-error": "Ошибка", "settings-malformed-swap-url": "Неправильно сформированный URL-адрес обмена", "settings-malformed-payment-url": "Неправильно сформированный URL-адрес платежа", "settings-malformed-invite-url": "Неправильно сформированный URL-адрес приглашения", "settings-success": "Успешно", "settings-successfully-updated-store": "Магазин успешно обновлен", "settings-back-to-settings": "Вернуться к настройкам", "settings-save": "Сохранить", "settings-cancel": "Отменить", "settings-store-details": "Подробная информация о магазине", "settings-manage-your-business-details": "Управляйте деталями своей компании", "settings-general": "Общие", "settings-store-name": "Название магазина", "settings-medusa-store": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings-advanced-settings": "Расширенные настройки", "settings-swap-link-template": "Шаблон ссылки обмена", "settings-draft-order-link-template": "Шаблон ссылки на черновой вариант заказа", "settings-invite-link-template": "Шаблон пригласительной ссылки", "settings-manage-the-general-settings-for-your-store": "Управляйте общими настройками вашего магазина", "settings-manage-the-settings-for-your-store-apos-s-extensions": "Управляйте настройками расширений вашего магазина", "edit-user-information-success": "Успешно", "edit-user-information-your-information-was-successfully-updated": "Ваша информация была успешно обновлена", "edit-user-information-edit-information": "Редактировать информацию", "edit-user-information-cancel": "Отменить", "edit-user-information-submit-and-close": "Отправить и закрыть", "personal-information-back-to-settings": "Вернуться к настройкам", "personal-information-personal-information": "Персональная информация", "personal-information-manage-your-medusa-profile": "Управляйте своим профилем Medusa", "personal-information-language-settings-title": "Язык", "personal-information-language-settings-description": "Измените язык администратора Medusa", "personal-information-language-settings-help-us-translate": "Помогите нам перевести", "personal-information-usage-insights-title": "Информация об использовании", "usage-insights-disabled": "Отключено", "usage-insights-active": "Акти<PERSON><PERSON>н", "usage-insights-share-usage-insights-and-help-us-improve-medusa": "Поделитесь информацией об использовании и помогите нам улучшить Medusa", "usage-insights-edit-preferences": "Редактировать настройки", "usage-insights-success": "Успешно", "usage-insights-your-information-was-successfully-updated": "Ваша информация была успешно обновлена", "usage-insights-error": "Ошибка", "usage-insights-cancel": "Отменить", "usage-insights-submit-and-close": "Отправить и закрыть", "region-form-title": "Заголовок", "region-form-europe": "Европа", "region-form-currency-code-is-required": "Требуется код валюты", "region-form-currency": "Валюта", "region-form-choose-currency": "Выбрать валюту", "region-form-default-tax-rate": "Налоговая ставка по умолчанию", "region-form-tax-rate-is-required": "Необходимо указать налоговую ставку", "region-form-tax-rate-must-be-equal-to-or-less-than-100": "Налоговая ставка должна быть равна или меньше 100", "region-form-default-tax-code": "Налоговый кодекс по умолчанию", "region-form-countries": "Страны", "region-form-choose-countries": "Выберите страны", "region-form-tax-inclusive-prices": "Цены с учетом налогов", "region-form-when-enabled-region-prices-will-be-tax-inclusive": "Если эта опция включена, региональные цены будут включать налоги.", "region-form-payment-providers-are-required": "Требуются платежные провайдеры", "region-form-payment-providers": "Платежные провайдеры", "region-form-choose-payment-providers": "Выберите поставщиков платежных услуг...", "region-form-fulfillment-providers-are-required": "Требуются поставщики услуг по выполнению работ", "region-form-fulfillment-providers": "Поставщики услуг по выполнению заказов", "region-form-choose-fulfillment-providers": "Выберите поставщиков услуг по выполнению заказов...", "shipping-option-card-success": "Успешно", "shipping-option-card-shipping-option-updated": "Способ доставки обновлен", "shipping-option-card-error": "Ошибка", "shipping-option-card-edit-shipping-option": "Редактировать способ доставки", "shipping-option-card-fulfillment-method": "Способ выполнения", "shipping-option-card-cancel": "Отменить", "shipping-option-card-save-and-close": "Сохранить и закрыть", "shipping-option-card-shipping-option-has-been-deleted": "Опция доставки была удалена", "shipping-option-card-flat-rate": "Фиксированная ставка", "shipping-option-card-calcualted": "Рассчитано", "shipping-option-card-min-subtotal": "Мин. промежуточный итог:", "shipping-option-card-max-subtotal": "Макс. промежуточный итог:", "shipping-option-card-admin": "Администратор", "shipping-option-card-store": "Мага<PERSON>ин", "shipping-option-card-edit": "Редактировать", "shipping-option-card-delete": "Удалить", "shipping-option-form-visible-in-store": "Отображается в магазине", "shipping-option-form-enable-or-disable-the-shipping-option-visiblity-in-store": "Включите или отключите отображение опции доставки в магазине.", "shipping-option-form-details": "Подробности", "shipping-option-form-title": "Заголовок", "shipping-option-form-title-is-required": "Необходимо указать заголовок", "shipping-option-form-price-type": "Price Type", "shipping-option-form-flat-rate": "Фиксированная ставка", "shipping-option-form-calculated": "Рассчитано", "shipping-option-form-choose-a-price-type": "Выбрать тип цены", "shipping-option-form-price": "Цена", "shipping-option-form-shipping-profile": "Профиль доставки", "shipping-option-form-choose-a-shipping-profile": "Выбрать профиль доставки", "shipping-option-form-fulfillment-method": "Способ выполнения", "shipping-option-form-choose-a-fulfillment-method": "Выбрать способ выполнения", "shipping-option-form-requirements": "Требования", "shipping-option-form-min-subtotal-must-be-less-than-max-subtotal": "Минимальный промежуточный итог должен быть меньше максимального. промежуточный итог", "shipping-option-form-min-subtotal": "Мин. промежуточный итог", "shipping-option-form-max-subtotal": "Макс. промежуточный итог", "shipping-option-form-metadata": "Метаданные", "general-section-success": "Успешно", "general-section-region-was-successfully-updated": "Регион успешно обновлен", "general-section-error": "Ошибка", "general-section-edit-region-details": "Редактировать данные региона", "general-section-details": "Подробности", "general-section-providers": "Провайдеры", "general-section-metadata": "Метаданные", "general-section-cancel": "Отменить", "general-section-save-and-close": "Сохранить и закрыть", "edit-something-went-wrong": "Что-то пошло не так...", "edit-no-region-found": "Мы не можем найти регион с таким ID, используйте меню слева, чтобы выбрать регион.", "return-shipping-options-success": "Успешно", "return-shipping-options-shipping-option-created": "Способ доставки создан", "return-shipping-options-error": "Ошибка", "return-shipping-options-add-return-shipping-option": "Добавить вариант обратной доставки", "return-shipping-options-cancel": "Отменить", "return-shipping-options-save-and-close": "Сохранить и закрыть", "return-shipping-options-return-shipping-options": "Способы обратной доставки", "return-shipping-options-add-option": "Добавить способ", "return-shipping-options-enter-specifics-about-available-regional-return-shipment-methods": "Введите сведения о доступных региональных методах обратной доставки.", "shipping-options-success": "Успешно", "shipping-options-shipping-option-created": "Способ доставки создан", "shipping-options-error": "Ошибка", "shipping-options-add-shipping-option": "Добавить способ доставки", "shipping-options-cancel": "Отменить", "shipping-options-save-and-close": "Сохранить и закрыть", "shipping-options-shipping-options": "Способы доставки", "shipping-options-add-option": "Добавить способ", "shipping-options-enter-specifics-about-available-regional-shipment-methods": "Введите информацию о доступных региональных методах доставки.", "new-region-created": "Регион создан", "new-create-region": "Создать регион", "new-details": "Подробности", "new-add-the-region-details": "Добавить сведения о регионе.", "new-providers": "Провайдеры", "new-add-which-fulfillment-and-payment-providers-should-be-available-in-this-region": "Добавьте, какие поставщики услуг исполнения и оплаты должны быть доступны в этом регионе.", "region-overview-regions": "Регионы", "region-overview-manage-the-markets-that-you-will-operate-within": "Управляйте рынками, на которых вы будете работать.", "region-overview-not-configured": "Не настроено", "region-overview-fulfillment-providers": "Поставщики услуг по выполнению заказов:", "return-reasons-notification-success": "Успешно", "return-reasons-created-a-new-return-reason": "Создана новая причина возврата", "return-reasons-success": "успешно", "return-reasons-error": "Ошибка", "return-reasons-cannot-create-a-return-reason-with-an-existing-value": "Невозможно создать причину возврата с существующим значением", "return-reasons-add-reason": "Добавить причину", "return-reasons-value-is-required": "Требуется значение", "return-reasons-value": "Значение", "return-reasons-label-is-required": "Требуется надпись", "return-reasons-label": "Надпись", "return-reasons-description": "Описание", "return-reasons-customer-received-the-wrong-size": "Клиент получил не тот размер", "return-reasons-cancel": "Отменить", "return-reasons-create": "Создать", "return-reasons-success-title": "Успешно", "return-reasons-successfully-updated-return-reason": "Причина возврата успешно обновлена", "return-reasons-duplicate-reason": "Дублировать причину", "return-reasons-delete-reason": "Удалить причину", "return-reasons-save": "Сохранить", "return-reasons-details": "Подробности", "return-reasons-delete-return-reason": "Удалить причину возврата", "return-reasons-are-you-sure-you-want-to-delete-this-return-reason": "Вы уверены, что хотите удалить причину возврата?", "return-reasons-back-to-settings": "Вернуться к настройкам", "return-reasons-return-reasons": "Причины возврата", "return-reasons-add-reason-label": "Добавить причину", "return-reasons-manage-reasons-for-returned-items": "Управляйте причинами возврата предметов", "taxes-details": "Подробности", "taxes-new-tax-rate": "Новая налоговая ставка", "taxes-tax-calculation-settings": "Настройки расчета налогов", "taxes-success": "Успешно", "taxes-successfully-updated-tax-rate": "Налоговая ставка успешно обновлена.", "taxes-error": "Ошибка", "taxes-overrides": "Переопределения", "taxes-product-rules": "Правила использования товара", "taxes-product-rules-description_one": "Применяется к {{count}} productWithCount", "taxes-product-rules-description_other": "Применяется к {{count}} productWithCount", "taxes-product-type-rules": "Правила для типов товаров", "taxes-product-type-rules-description_one": "Применяет<PERSON><PERSON> к {{count}} товару typeWithCount", "taxes-product-type-rules-description_other": "Применяетс<PERSON> к {{count}} товарам typeWithCount", "taxes-shipping-option-rules": "Правила выбора способа доставки", "taxes-applies-to-shipping-option-with-count_one": "Применяетс<PERSON> к {{count}} доставке optionWithCount", "taxes-applies-to-shipping-option-with-count_other": "Применяетс<PERSON> к {{count}} доставкам optionWithCount", "taxes-add-overrides": "Добавить переопределения", "taxes-cancel": "Отменить", "taxes-save": "Сохранить", "taxes-name": "Имя", "taxes-default": "По умолчанию", "taxes-rate-name": "Название тарифа", "taxes-tax-rate": "Налоговая ставка", "taxes-tax-code": "Налоговый кодекс", "taxes-edit-tax-rate": "Редактировать налоговую ставку", "taxes-back-to-settings": "Вернуться к настройкам", "taxes-regions": "Регионы", "taxes-select-the-region-you-wish-to-manage-taxes-for": "Выберите регион, для которого вы хотите управлять налогами", "taxes-go-to-region-settings": "Перейти в настройки региона", "taxes-successfully-created-tax-rate": "Налоговая ставка успешно создана.", "taxes-add-tax-rate": "Добавить налоговую ставку", "taxes-applies-to-product-type-with-count_one": "Применяет<PERSON><PERSON> к {{count}} товару typeWithCount", "taxes-applies-to-product-type-with-count_other": "Применяетс<PERSON> к {{count}} товарам typeWithCount", "taxes-create": "Создать", "taxes-select-products": "Выбрать товары", "taxes-select-product-types-label": "Выбрать типы товаров", "taxes-product-types": "Типы товаров", "taxes-system-tax-provider": "Провайдер системных налогов", "taxes-region-tax-settings-were-successfully-updated": "Настройки регионального налога успешно обновлены.", "taxes-tax-provider": "Налоговый провайдер", "taxes-calculate-taxes-automatically": "Автоматически рассчитывать налоги?", "taxes-automatically-apply-tax-calculations-to-carts": "Если этот флажок установлен, Medusa автоматически применит налоговые расчеты к корзинам в этом регионе. Если флажок снят, вам придется вручную рассчитать налоги при оформлении заказа. При использовании стороннего налогового провайдера рекомендуется взимать налоги вручную, чтобы избежать выполнения слишком большого количества запросов", "taxes-apply-tax-to-gift-cards": "Облагать налогом подарочные карты?", "taxes-apply-taxes-to-gift-cards": "Если флажок установлен, к подарочным картам при оформлении заказа будут применяться налоги. В некоторых странах налоговое законодательство требует, чтобы подарочные карты при покупке облагались налогом.", "taxes-search-products": "Поиск товаров..", "taxes-select-shipping-option": "Выбрать способ доставки", "taxes-shipping-options": "Способы доставки", "taxes-delete-tax-rate-heading": "Удалить налоговую ставку", "taxes-confirm-delete": "Вы уверены, что хотите удалить эту налоговую ставку?", "taxes-tax-rate-was-deleted": "Налоговая ставка была удалена.", "taxes-edit": "Редактировать", "taxes-delete-tax-rate": "Удалить налоговую ставку", "taxes-delete-rule": "Удалить правило", "taxes-type": "Тип", "taxes-products": "Товары", "taxes-select-individual-products": "Выбрать отдельные товары", "taxes-select-product-types": "Выбрать типы товаров", "taxes-select-shipping-options": "Выбрать способы доставки", "taxes-back": "Назад", "taxes-add": "Добавить", "taxes-code": "<PERSON>од", "users-invite-users": "Пригласить пользователей", "users-back-to-settings": "Back to settings", "users-the-team": "Команда", "users-manage-users-of-your-medusa-store": "Управляйте пользователями вашего магазина Medusa", "users-count_one": "{{count}}", "users-count_other": "{{count}}"}