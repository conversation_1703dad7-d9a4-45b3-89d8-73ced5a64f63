{"back-button-go-back": "戻る", "filter-menu-trigger": "表示する", "filter-menu-clear-button": "クリア", "filter-menu-select-item-default-placeholder": "フィルタを選択する", "filter-menu-select-item-clear-button": "選択したオプションをクリアする", "filter-menu-select-item-selected": "選択中", "filter-menu-date-item-before": "前", "filter-menu-date-item-after": "後ろ", "filter-menu-date-item-between": "間に", "sales-channels-display-available-count": "<2>{{availableChannelsCount}}</2> / <6>{{totalChannelsCount}}</6>の販売チャンネルで利用可能", "activity-drawer-activity": "アクティビティ", "activity-drawer-no-notifications-title": "ここは静かです...", "activity-drawer-no-notifications-description": "現在通知はありませんが、通知があるとここに表示されます。", "activity-drawer-error-title": "おっと...", "activity-drawer-error-description": "通知を取得しようとしている間に何か問題が発生しました - それでも試みます！", "activity-drawer-processing": "処理中...", "analytics-config-form-title": "利用データの匿名化", "analytics-config-form-description": "利用データの匿名化を選択することができます。このオプションが選択されている場合、お名前やメールアドレスなどの個人情報は収集しません。", "analytics-config-form-opt-out": "利用データの共有をオプトアウトする", "analytics-config-form-opt-out-later": "いつでも利用データの共有をオプトアウトすることができます。", "analytics-preferences-success": "成功", "analytics-preferences-your-preferences-were-successfully-updated": "設定が正常に更新されました", "analytics-preferences-error": "エラー", "analytics-preferences-help-us-get-better": "私たちがよりよくなる手助けをしてください", "analytics-preferences-disclaimer": "Medusaの使用方法を知ることで、より魅力的なEC体験を作りたいと考えています。ユーザーのインサイトは、より良い、より魅力的で使いやすい製品を作るために役立ちます。製品の改善のためにのみデータを収集しています。私たちが収集するデータについては、私たちの〔ドキュメント〕をお読みください", "analytics-preferences-documentation": "有効なメールアドレスを入力してください", "analytics-preferences-please-enter-a-valid-email": "続ける", "analytics-preferences-continue": "通貨", "currency-input-currency": "金額が有効ではありません", "currency-input-amount-is-not-valid": "成功", "organisms-success": "削除が成功しました", "organisms-delete-successful": "本当に削除しますか？", "organisms-are-you-sure-you-want-to-delete": "いいえ、キャンセル", "organisms-no-cancel": "はい、削除します", "organisms-yes-remove": "追加の詳細を非表示", "details-collapsible-hide-additional-details": "追加の詳細を表示", "details-collapsible-show-additional-details": "成功", "edit-user-modal-success": "ユーザーが更新されました", "edit-user-modal-user-was-updated": "エラー", "edit-user-modal-edit-user": "ユーザーを編集する", "edit-user-modal-first-name-label": "名前", "edit-user-modal-first-name-placeholder": "名前...", "edit-user-modal-last-name-label": "姓", "edit-user-modal-last-name-placeholder": "姓...", "edit-user-modal-email": "メール", "edit-user-modal-cancel": "キャンセル", "edit-user-modal-save": "保存", "error-boundary-back-to-dashboard": "ダッシュボードに戻る", "error-boundary-an-unknown-error-occured": "不明なエラーが発生しました", "error-boundary-bad-request": "不正なリクエスト", "error-boundary-you-are-not-logged-in": "ログインしていません", "error-boundary-you-do-not-have-permission-perform-this-action": "この操作を実行する権限がありません", "error-boundary-page-was-not-found": "ページが見つかりませんでした", "error-boundary-an-unknown-server-error-occured": "不明なサーバーエラーが発生しました", "error-boundary-503": "現在、サーバーは利用できません", "error-boundary-500": "原因不明のエラーが発生しました。これはおそらく私たちの技術的な問題に起因するものです。ページを更新してみてください。問題が続く場合は、管理者に連絡してください。", "error-boundary-400": "リクエストが不正です。リクエストを修正して再試行してください。", "error-boundary-401": "ログインしていません。続行するにはログインしてください。", "error-boundary-403": "この操作を実行する権限がありません。もしミスだと思ったら、管理者に連絡してください。", "error-boundary-404": "指定されたページが見つかりませんでした。URLを確認してもう一度試してください。", "error-boundary-500-2": "サーバーが要求を処理できませんでした。これはほとんどが私たちの側の技術的な問題によるものです。もう一度お試しください。問題が続く場合は、管理者にお問い合わせください。", "error-boundary-503-2": "サーバーが一時的に利用できないため、要求を処理できませんでした。後でもう一度お試しください。問題が続く場合は、管理者にお問い合わせください。", "export-modal-title": "データのエクスポートを初期化します", "export-modal-cancel": "キャンセル", "export-modal-export": "エクスポートする", "file-upload-modal-upload-a-new-photo": "新しい写真をアップロードする", "gift-card-banner-edit": "編集する", "gift-card-banner-unpublish": "非公開にする", "gift-card-banner-publish": "公開する", "gift-card-banner-delete": "削除する", "gift-card-banner-published": "公開中", "gift-card-banner-unpublished": "非公開", "gift-card-denominations-section-denomination-added": "デノミネーションが追加されました", "gift-card-denominations-section-a-new-denomination-was-successfully-added": "新しいデノミネーションが正常に追加されました", "gift-card-denominations-section-a-denomination-with-that-default-value-already-exists": "そのデフォルト値を持つデノミネーションはすでに存在します", "gift-card-denominations-section-error": "エラー", "gift-card-denominations-section-add-denomination": "デノミネーションを追加する", "gift-card-denominations-section-cancel": "キャンセル", "gift-card-denominations-section-save-and-close": "保存して閉じる", "gift-card-denominations-section-denomination-updated": "表記名が更新されました", "gift-card-denominations-section-a-new-denomination-was-successfully-updated": "新しい表記名が正常に更新されました", "gift-card-denominations-section-edit-denomination": "表記名を編集する", "gift-card-denominations-section-denominations": "表記名", "gift-card-denominations-section-denomination": "表記名", "gift-card-denominations-section-in-other-currencies": "他の通貨で", "gift-card-denominations-section-and-more_one": "さらに{{count}}件", "gift-card-denominations-section-and-more_other": "さらに{{count}}件", "gift-card-denominations-section-delete-denomination": "表記名を削除する", "gift-card-denominations-section-confirm-delete": "本当にこの表記名を削除しますか？", "gift-card-denominations-section-denomination-deleted": "表記名が削除されました", "gift-card-denominations-section-denomination-was-successfully-deleted": "表記名が正常に削除されました", "gift-card-denominations-section-edit": "編集する", "gift-card-denominations-section-delete": "削除する", "help-dialog-how-can-we-help": "なにかお手伝いできますか？", "help-dialog-we-usually-respond-in-a-few-hours": "通常数時間以内に返信いたします", "help-dialog-subject": "件名", "help-dialog-what-is-it-about": "何についてですか？...", "help-dialog-write-a-message": "メッセージを入力してください...", "help-dialog-feel-free-to-join-our-community-of": "私たちのコミュニティに自由に参加してください", "help-dialog-merchants-and-e-commerce-developers": "マーチャントとEコマース開発者", "help-dialog-send-a-message": "メッセージを送る", "invite-modal-success": "成功", "invite-modal-invitation-sent-to": "{{user}}への招待を送信しました", "invite-modal-error": "エラー", "invite-modal-member": "メンバー", "invite-modal-admin": "管理者", "invite-modal-developer": "開発者", "invite-modal-invite-users": "ユーザーを招待する", "invite-modal-email": "メール", "invite-modal-role": "役割", "invite-modal-select-role": "役割を選択してください", "invite-modal-cancel": "キャンセル", "invite-modal-invite": "招待する", "login-card-no-match": "これらの資格情報は当社の記録と一致しません。", "login-card-log-in-to-medusa": "Medusaにログインする", "login-card-email": "メール", "login-card-password": "パスワード", "login-card-forgot-your-password": "パスワードをお忘れですか？", "metadata-add-metadata": "メタデータを追加する", "product-attributes-section-edit-attributes": "属性を編集する", "product-attributes-section-dimensions": "寸法", "product-attributes-section-configure-to-calculate-the-most-accurate-shipping-rates": "最も正確な送料を計算するように設定する", "product-attributes-section-customs": "税関", "product-attributes-section-cancel": "キャンセル", "product-attributes-section-save": "保存する", "product-attributes-section-title": "属性", "product-attributes-section-height": "高さ", "product-attributes-section-width": "幅", "product-attributes-section-length": "長さ", "product-attributes-section-weight": "重さ", "product-attributes-section-mid-code": "MIDコード", "product-attributes-section-hs-code": "HSコード", "product-attributes-section-country-of-origin": "原産国", "product-general-section-success": "成功", "product-general-section-successfully-updated-sales-channels": "販売チャネルを正常に更新しました", "product-general-section-error": "エラー", "product-general-section-failed-to-update-sales-channels": "販売チャネルの更新に失敗しました", "product-general-section-edit-general-information": "一般情報を編集する", "product-general-section-gift-card": "ギフトカード", "product-general-section-product": "商品", "product-general-section-metadata": "メタデータ", "product-general-section-cancel": "キャンセル", "product-general-section-save": "保存", "product-general-section-delete": "削除", "product-general-section-edit-sales-channels": "販売チャネルの編集", "product-general-section-published": "公開済み", "product-general-section-draft": "下書き", "product-general-section-details": "詳細", "product-general-section-subtitle": "字幕", "product-general-section-handle": "ハンドル", "product-general-section-type": "タイプ", "product-general-section-collection": "コレクション", "product-general-section-category": "カテゴリー", "product-general-section-discountable": "割引可能", "product-general-section-true": "はい", "product-general-section-false": "いいえ", "product-general-section-count_one": "{{count}}", "product-general-section-count_other": "{{count}}", "product-general-section-sales-channels": "販売チャネル", "product-media-section-edit-media": "メディアを編集する", "product-media-section-upload-images-error": "画像のアップロード中に問題が発生しました。", "product-media-section-file-service-not-configured": "ファイルサービスが構成されていない可能性があります。管理者に連絡してください。", "product-media-section-error": "エラー", "product-media-section-media": "メディア", "product-media-section-add-images-to-your-product": "製品に画像を追加してください。", "product-media-section-cancel": "キャンセル", "product-media-section-save-and-close": "保存して閉じる", "product-raw-section-raw-gift-card": "生のギフトカード", "product-raw-section-raw-product": "生の製品", "product-thumbnail-section-success": "成功", "product-thumbnail-section-successfully-deleted-thumbnail": "サムネイルを削除しました", "product-thumbnail-section-error": "エラー", "product-thumbnail-section-edit": "編集する", "product-thumbnail-section-upload": "アップロードする", "product-thumbnail-section-upload-thumbnail-error": "サムネイルのアップロード中に問題が発生しました。", "product-thumbnail-section-you-might-not-have-a-file-service-configured-please-contact-your-administrator": "ファイルサービスが構成されていない可能性があります。管理者に連絡してください", "product-thumbnail-section-upload-thumbnail": "サムネイルをアップロードする", "product-thumbnail-section-thumbnail": "サムネイル", "product-thumbnail-section-used-to-represent-your-product-during-checkout-social-sharing-and-more": "チェックアウトやソーシャル共有などで製品を表すために使用されます。", "product-thumbnail-section-cancel": "キャンセル", "product-thumbnail-section-save-and-close": "保存して閉じる", "product-variant-tree-count_one": "{{count}}", "product-variant-tree-count_other": "{{count}}", "product-variant-tree-add-prices": "価格を追加", "product-variants-section-add-variant": "バリアントを追加", "product-variants-section-cancel": "キャンセル", "product-variants-section-save-and-close": "保存して閉じる", "product-variants-section-edit-stock-inventory": "在庫と在庫の編集", "product-variants-section-edit-variant": "バリアントの編集", "edit-variants-modal-cancel": "キャンセル", "edit-variants-modal-save-and-go-back": "保存して戻る", "edit-variants-modal-save-and-close": "保存して閉じる", "edit-variants-modal-edit-variant": "バリアントの編集", "edit-variants-modal-update-success": "バリアントが正常に更新されました", "edit-variants-modal-edit-variants": "バリアントの編集", "edit-variants-modal-product-variants": "製品のバリアント", "edit-variants-modal-variant": "バリアント", "edit-variants-modal-inventory": "在庫", "product-variants-section-edit-prices": "価格を編集", "product-variants-section-edit-variants": "バリエーションの編集", "product-variants-section-edit-options": "オプションの編集", "product-variants-section-product-variants": "商品バリエーション", "product-variants-section-error": "エラー", "product-variants-section-failed-to-update-product-options": "商品オプションの更新に失敗しました", "product-variants-section-success": "成功", "product-variants-section-successfully-updated-product-options": "商品オプションの更新に成功しました", "product-variants-section-product-options": "商品オプション", "product-variants-section-option-title": "オプションのタイトル", "product-variants-section-option-title-is-required": "オプションのタイトルは必須です", "product-variants-section-add-an-option": "オプションを追加", "product-variants-section-inventory": "在庫", "product-variants-section-title": "タイトル", "product-variants-section-sku": "SKU", "product-variants-section-ean": "EAN", "product-variants-section-manage-inventory": "在庫管理", "product-variants-section-duplicate-variant": "バリエーションの複製", "product-variants-section-delete-variant-label": "バリエーションの削除", "product-variants-section-yes-delete": "はい、削除します", "product-variants-section-delete-variant-heading": "バリエーションの削除", "product-variants-section-confirm-delete": "このバリアントを削除してもよろしいですか？", "product-variants-section-note-deleting-the-variant-will-also-remove-inventory-items-and-levels": "注意：バリアントを削除すると、在庫アイテムとレベルも削除されます。", "reset-token-card-error": "エラー", "reset-token-card-reset-your-password": "パスワードをリセットする", "reset-token-card-password-reset-description": "以下にメールアドレスを入力すると、パスワードをリセットする方法についての<1></1>指示が送信されます。", "reset-token-card-email": "メールアドレス", "reset-token-card-this-is-not-a-valid-email": "有効なメールアドレスではありません", "reset-token-card-send-reset-instructions": "リセットの指示を送信する", "reset-token-card-successfully-sent-you-an-email": "メールが正常に送信されました", "reset-token-card-go-back-to-sign-in": "サインインに戻る", "rma-return-product-table-product-details": "製品の詳細", "rma-return-product-table-quantity": "数量", "rma-select-product-table-product-details": "製品の詳細", "rma-select-product-table-quantity": "数量", "rma-select-product-table-refundable": "返金可能", "rma-select-product-table-images-witch-count_one": "{{count}}", "rma-select-product-table-images-witch-count_other": "{{count}}", "rma-select-product-table-select-reason": "理由の選択", "sidebar-store": "ストア", "sidebar-orders": "注文", "sidebar-products": "製品", "sidebar-categories": "カテゴリー", "sidebar-customers": "顧客", "sidebar-inventory": "在庫", "sidebar-discounts": "割引", "sidebar-gift-cards": "ギフトカード", "sidebar-pricing": "価格設定", "sidebar-settings": "設定", "table-container-soothed-offset_one": "{{soothedOffset}} - {{pageSize}} / {{count}} {{title}}", "table-container-soothed-offset_other": "{{soothedOffset}} - {{pageSize}} / {{count}} {{title}}", "table-container-current-page": "{{currentPage}} / {{soothedPageCount}}", "timeline-request-return": "返品リクエスト", "timeline-register-exchange": "交換登録", "timeline-register-claim": "クレーム登録", "timeline-success": "成功", "timeline-added-note": "メモを追加", "timeline-error": "エラー", "timeline-timeline": "タイムライン", "upload-modal-new": "新着", "upload-modal-updates": "更新", "upload-modal-drop-your-file-here-or": "ここにファイルをドロップするか、", "upload-modal-click-to-browse": "クリックして参照してください。", "upload-modal-only-csv-files-are-supported": ".csvファイルのみサポートされています。", "upload-modal-import-file-title": "{{fileTitle}}をインポートする", "upload-modal-cancel": "キャンセル", "upload-modal-import-list": "リストをインポートする", "add-products-modal-add-products": "商品を追加する", "add-products-modal-search-by-name-or-description": "名前や説明で検索する...", "add-products-modal-cancel": "キャンセル", "add-products-modal-save": "保存する", "add-products-modal-product-details": "商品の詳細", "add-products-modal-status": "ステータス", "add-products-modal-variants": "バリアント", "templates-general": "一般", "templates-first-name": "名", "templates-last-name": "姓", "templates-company": "会社", "templates-phone": "電話番号", "templates-billing-address": "請求先住所", "templates-shipping-address": "配送先住所", "templates-address": "住所", "templates-address-1": "住所 1", "templates-address-2": "住所 2", "templates-postal-code": "郵便番号", "templates-city": "市", "templates-province": "州", "templates-country": "国", "templates-metadata": "メタデータ", "collection-modal-success": "成功", "collection-modal-successfully-updated-collection": "コレクションの更新に成功しました", "collection-modal-error": "エラー", "collection-modal-successfully-created-collection": "コレクションの作成に成功しました", "collection-modal-edit-collection": "コレクションの編集", "collection-modal-add-collection": "コレクションの追加", "collection-modal-description": "コレクションを作成するには、タイトルとハンドルが必要です。", "collection-modal-details": "詳細", "collection-modal-title-label": "タイトル", "collection-modal-title-placeholder": "サングラス", "collection-modal-handle-label": "ハンドル", "collection-modal-handle-placeholder": "サングラス", "collection-modal-slug-description": "コレクションのURLスラッグ。空欄の場合は自動生成されます。", "collection-modal-metadata": "メタデータ", "collection-modal-cancel": "キャンセル", "collection-modal-save-collection": "コレクションを保存", "collection-modal-publish-collection": "コレクションを公開", "collection-product-table-add-products": "商品を追加", "collection-product-table-products": "商品", "collection-product-table-search-products": "商品を検索", "collection-product-table-cancel": "キャンセル", "collection-product-table-save": "保存", "collection-product-table-sort-by": "並べ替え", "collection-product-table-all": "すべて", "collection-product-table-newest": "新しい順", "collection-product-table-oldest": "古い順", "collection-product-table-title": "タイトル", "collection-product-table-decide-status-published": "公開済み", "collection-product-table-draft": "下書き", "collection-product-table-proposed": "提案済み", "collection-product-table-rejected": "却下済み", "collection-product-table-remove-product-from-collection": "コレクションから商品を削除", "collection-product-table-product-removed-from-collection": "コレクションから商品が削除されました", "collections-table-delete-collection": "コレクションを削除する", "collections-table-confirm-delete": "このコレクションを削除してもよろしいですか？", "collections-table-edit": "編集", "collections-table-delete": "削除", "collections-table-title": "タイトル", "collections-table-handle": "ハンドル", "collections-table-created-at": "作成日", "collections-table-updated-at": "更新日", "collections-table-products": "商品", "customer-group-table-details": "詳細", "customer-group-table-delete": "削除", "customer-group-table-success": "成功", "customer-group-table-group-deleted": "グループが削除されました", "customer-group-table-error": "エラー", "customer-group-table-failed-to-delete-the-group": "グループの削除に失敗しました", "customer-group-table-customer-groups": "顧客グループ", "customer-group-table-delete-from-the-group": "グループから削除する", "customer-group-table-customer-groups-title": "顧客グループ", "customer-group-table-groups": "グループ", "customer-group-table-all": "すべて", "customer-group-table-edit-customers": "顧客を編集", "customer-group-table-customers": "顧客", "customer-group-table-cancel": "キャンセル", "customer-group-table-save": "保存", "customer-orders-table-orders": "注文", "customer-orders-table-transfer-order": "注文を移動", "customer-orders-table-paid": "支払い済み", "customer-orders-table-awaiting": "待機中", "customer-orders-table-requires-action": "対応が必要", "customer-orders-table-n-a": "N/A", "customer-orders-table-fulfilled": "完了済み", "customer-orders-table-shipped": "出荷済み", "customer-orders-table-not-fulfilled": "未完了", "customer-orders-table-partially-fulfilled": "一部完了", "customer-orders-table-partially-shipped": "一部出荷済み", "customer-orders-table-order": "注文", "customer-orders-table-remainder-more": "+ {{remainder}} もっと", "customer-orders-table-date": "日付", "customer-orders-table-fulfillment": "達成", "customer-orders-table-status": "ステータス", "customer-orders-table-total": "合計", "customer-table-customers": "顧客", "customer-table-edit": "編集", "customer-table-details": "詳細", "customer-table-date-added": "追加日", "customer-table-name": "名前", "customer-table-email": "メールアドレス", "customer-table-orders": "注文", "discount-filter-dropdown-filters": "フィルター", "discount-table-discounts": "割引", "discount-table-search-by-code-or-description": "コードまたは説明で検索...", "discount-table-success": "成功", "discount-table-successfully-copied-discount": "割引を正常にコピーしました", "discount-table-error": "エラー", "discount-table-scheduled": "予約済み", "discount-table-expired": "期限切れ", "discount-table-active": "有効な", "discount-table-disabled": "無効な", "discount-table-free-shipping": "送料無料", "discount-table-code": "コード", "discount-table-description": "説明", "discount-table-amount": "金額", "discount-table-status": "ステータス", "discount-table-redemptions": "引き換え", "discount-table-delete-discount": "ディスカウントを削除", "discount-table-confirm-delete": "このディスカウントを削除してもよろしいですか？", "discount-table-publish": "公開する", "discount-table-unpublish": "非公開にする", "discount-table-successfully-published-discount": "ディスカウントを正常に公開しました", "discount-table-successfully-unpublished-discount": "ディスカウントを正常に非公開にしました", "discount-table-duplicate": "複製", "discount-table-delete": "削除", "draft-order-table-draft-orders": "下書き注文", "draft-order-table-completed": "完了", "draft-order-table-open": "オープン", "draft-order-table-draft": "下書き", "draft-order-table-order": "注文", "draft-order-table-date-added": "追加日", "draft-order-table-customer": "顧客", "draft-order-table-status": "ステータス", "gift-card-filter-dropdown-is-in-the-last": "最後の中にある", "gift-card-filter-dropdown-is-older-than": "より古い", "gift-card-filter-dropdown-is-after": "の後", "gift-card-filter-dropdown-is-before": "の前", "gift-card-filter-dropdown-is-equal-to": "と等しい", "gift-card-filter-dropdown-filters": "フィルター", "gift-card-filter-dropdown-status": "ステータス", "gift-card-filter-dropdown-payment-status": "支払い状況", "gift-card-filter-dropdown-fulfillment-status": "履行状況", "gift-card-filter-dropdown-date": "日付", "gift-card-table-gift-cards": "ギフトカード", "gift-card-table-code": "コード", "gift-card-table-order": "注文", "gift-card-table-original-amount": "元の金額", "gift-card-table-balance": "残高", "gift-card-table-region-has-been-deleted": "地域が削除されました", "gift-card-table-none": "なし", "gift-card-table-created": "作成された", "image-table-file-name": "ファイル名", "image-table-thumbnail": "サムネイル", "image-table-select-thumbnail-image-for-product": "この商品のサムネイルに使用する画像を選択してください", "inventory-table-inventory-items": "在庫アイテム", "inventory-table-actions-adjust-availability": "可用性の調整", "inventory-table-view-product": "商品を表示する", "inventory-table-success": "成功", "inventory-table-inventory-item-updated-successfully": "在庫アイテムが正常に更新されました", "inventory-table-adjust-availability": "可用性の調整", "inventory-table-cancel": "キャンセル", "inventory-table-save-and-close": "保存して閉じる", "inventory-table-item": "アイテム", "inventory-table-variant": "バリアント", "inventory-table-sku": "SKU", "inventory-table-reserved": "予約済み", "inventory-table-in-stock": "在庫あり", "order-filter-dropdown-filters": "フィルター", "order-filter-dropdown-status": "ステータス", "order-filter-dropdown-payment-status": "支払いステータス", "order-filter-dropdown-fulfillment-status": "履行ステータス", "order-filter-dropdown-regions": "地域", "order-filter-dropdown-sales-channel": "販売チャンネル", "order-filter-dropdown-date": "日付", "order-table-paid": "支払い済み", "order-table-awaiting": "保留中", "order-table-requires-action": "対応が必要", "order-table-canceled": "キャンセル済み", "order-table-n-a": "不適用", "order-table-order": "注文", "order-table-date-added": "追加日", "order-table-customer": "顧客", "order-table-fulfillment": "発送", "order-table-payment-status": "支払い状況", "order-table-sales-channel": "販売チャンネル", "order-table-total": "合計", "order-table-filters-complete": "完了", "order-table-filters-incomplete": "未完了", "price-list-table-filters": "フィルター", "price-list-table-status": "ステータス", "price-list-table-type": "タイプ", "price-list-table-price-lists": "価格リスト", "price-list-table-success": "成功", "price-list-table-successfully-copied-price-list": "価格リストを正常にコピーしました", "price-list-table-error": "エラー", "price-list-table-delete-price-list": "価格リストを削除します", "price-list-table-confirm-delete": "この価格リストを削除してもよろしいですか？", "price-list-table-successfully-deleted-the-price-list": "価格リストを正常に削除しました", "price-list-table-successfully-unpublished-price-list": "価格リストを正常に非公開にしました", "price-list-table-successfully-published-price-list": "価格リストを正常に公開しました", "price-list-table-unpublish": "非公開", "price-list-table-publish": "公開", "price-list-table-delete": "削除", "price-list-table-name": "名前", "price-list-table-description": "説明", "price-list-table-groups": "グループ", "price-list-table-other-more": "+他{{other}}件", "price-overrides-apply-overrides-on-selected-variants": "選択したバリアントにオーバーライドを適用する", "price-overrides-apply-on-all-variants": "すべてのバリアントに適用する", "price-overrides-prices": "価格", "price-overrides-cancel": "キャンセル", "price-overrides-save-and-close": "保存して閉じる", "price-overrides-show-regions": "地域を表示する", "product-table-products": "製品", "product-table-copy-success": "成功", "product-table-copy-created-a-new-product": "新しい製品を作成しました", "product-table-copy-error": "エラー", "product-table-delete-product": "製品を削除する", "product-table-confirm-delete": "この製品を削除してもよろしいですか？", "product-table-edit": "編集する", "product-table-unpublish": "非公開にする", "product-table-publish": "公開する", "product-table-draft": "下書き", "product-table-published": "公開済み", "product-table-success": "成功", "product-table-successfully-unpublished-product": "製品を正常に非公開にしました", "product-table-successfully-published-product": "製品を正常に公開しました", "product-table-error": "エラー", "product-table-duplicate": "複製", "product-table-delete": "削除", "product-table-proposed": "提案済み", "product-table-published-title": "公開済み", "product-table-rejected": "拒否されました", "product-table-draft-title": "下書き", "product-table-name": "名前", "product-table-collection": "コレクション", "product-table-status": "状態", "product-table-availability": "利用可能性", "product-table-inventory": "在庫", "product-table-inventory-in-stock-count_one": "{{count}}種類のバリアントに在庫があります", "product-table-inventory-in-stock-count_other": "{{count}}種類のバリアントに在庫があります", "reservation-form-location": "場所", "reservation-form-choose-where-you-wish-to-reserve-from": "予約したい場所を選択してください。", "reservation-form-item-to-reserve": "予約するアイテム", "reservation-form-select-the-item-that-you-wish-to-reserve": "予約したいアイテムを選択してください。", "reservation-form-item": "アイテム", "reservation-form-in-stock": "在庫あり", "reservation-form-available": "利用可能", "reservation-form-reserve": "予約する", "reservation-form-remove-item": "アイテムの削除", "reservation-form-description": "説明", "reservation-form-what-type-of-reservation-is-this": "この予約はどのタイプですか？", "reservations-table-reservations": "予約", "reservations-table-edit": "編集", "reservations-table-delete": "削除", "reservations-table-confirm-delete": "この予約を削除してもよろしいですか？", "reservations-table-remove-reservation": "予約を削除", "reservations-table-reservation-has-been-removed": "予約が削除されました", "new-success": "成功", "new-successfully-created-reservation": "予約が正常に作成されました", "new-error": "エラー", "new-cancel": "キャンセル", "new-save-reservation": "予約を保存", "new-reserve-item": "商品を予約", "new-metadata": "メタデータ", "reservations-table-order-id": "注文ID", "reservations-table-description": "説明", "reservations-table-created": "作成日", "reservations-table-quantity": "数量", "search-modal-start-typing-to-search": "検索するには入力を開始してください...", "search-modal-clear-search": "検索をクリア", "search-modal-or": "または", "search-modal-to-navigate": "ナビゲートする", "search-modal-to-select-and": "選択する", "search-modal-to-search-anytime": "いつでも検索する", "templates-settings": "設定", "templates-manage-the-settings-for-your-medusa-store": "Medusaストアの設定を管理する", "transfer-orders-modal-info": "情報", "transfer-orders-modal-customer-is-already-the-owner-of-the-order": "顧客はすでに注文の所有者です", "transfer-orders-modal-success": "成功", "transfer-orders-modal-successfully-transferred-order-to-different-customer": "注文を別の顧客に正常に転送しました", "transfer-orders-modal-error": "エラー", "transfer-orders-modal-could-not-transfer-order-to-different-customer": "注文を別の顧客に転送できませんでした", "transfer-orders-modal-transfer-order": "注文を転送する", "transfer-orders-modal-order": "注文", "transfer-orders-modal-current-owner": "現在の所有者", "transfer-orders-modal-the-customer-currently-related-to-this-order": "この注文に関連付けられている現在の顧客", "transfer-orders-modal-new-owner": "新しい所有者", "transfer-orders-modal-the-customer-to-transfer-this-order-to": "この注文を転送する顧客", "transfer-orders-modal-cancel": "キャンセル", "transfer-orders-modal-confirm": "確認", "templates-edit-user": "ユーザーを編集する", "templates-remove-user": "ユーザーを削除する", "templates-resend-invitation": "招待を再送する", "templates-success": "成功", "templates-invitiation-link-has-been-resent": "招待リンクが再送されました", "templates-copy-invite-link": "招待リンクをコピー", "templates-invite-link-copied-to-clipboard": "招待リンクがクリップボードにコピーされました", "templates-remove-invitation": "招待を削除する", "templates-expired": "期限切れ", "templates-pending": "保留中", "templates-all": "すべて", "templates-member": "メンバー", "templates-admin": "管理者", "templates-no-team-permissions": "チーム権限なし", "templates-status": "ステータス", "templates-active": "アクティブ", "templates-name": "名前", "templates-email": "メールアドレス", "templates-team-permissions": "チーム権限", "templates-confirm-remove": "このユーザーを削除してもよろしいですか？", "templates-remove-user-heading": "ユーザーを削除する", "templates-user-has-been-removed": "ユーザーは削除されました", "templates-confirm-remove-invite": "この招待を削除しますか？", "templates-remove-invite": "招待を削除", "templates-invitiation-has-been-removed": "招待が削除されました", "multiselect-choose-categories": "カテゴリを選択してください", "domain-categories-multiselect-selected-with-counts_one": "{{count}}", "domain-categories-multiselect-selected-with-counts_other": "{{count}}", "details-success": "成功", "details-updated-products-in-collection": "コレクションの製品を更新しました", "details-error": "エラー", "details-back-to-collections": "コレクションに戻る", "details-edit-collection": "コレクションを編集する", "details-delete": "削除", "details-metadata": "メタデータ", "details-edit-products": "製品を編集する", "details-products-in-this-collection": "このコレクションの製品", "details-raw-collection": "生のコレクション", "details-delete-collection": "コレクションを削除する", "details-successfully-deleted-collection": "コレクションが正常に削除されました", "details-yes-delete": "はい、削除します", "details-successfully-updated-customer": "顧客の情報が正常に更新されました", "details-customer-details": "顧客の詳細", "details-general": "一般", "details-first-name": "名", "details-lebron": "レブロン", "details-last-name": "姓", "details-james": "ジェームズ", "details-email": "メール", "details-phone-number": "電話番号", "details-cancel": "キャンセル", "details-save-and-close": "保存して閉じる", "details-edit": "編集", "details-back-to-customers": "顧客に戻る", "details-first-seen": "初回アクセス", "details-phone": "電話", "details-orders": "注文", "details-user": "ユーザー", "details-orders_one": "{{count}}件の注文", "details-orders_other": "{{count}}件の注文", "details-an-overview-of-customer-orders": "注文の概要", "details-raw-customer": "生の顧客", "groups-group-updated": "グループが更新されました", "groups-group-created": "グループが作成されました", "groups-the-customer-group-has-been-updated": "顧客グループが更新されました", "groups-the-customer-group-has-been-created": "顧客グループが作成されました", "groups-edit-customer-group": "顧客グループの編集", "groups-create-a-new-customer-group": "新しい顧客グループの作成", "groups-details": "詳細", "groups-metadata": "メタデータ", "groups-cancel": "キャンセル", "groups-edit-group": "グループの編集", "groups-publish-group": "グループの公開", "groups-no-customers-in-this-group-yet": "まだこのグループに顧客はいません", "groups-customers": "顧客", "groups-edit": "編集", "groups-delete": "削除", "groups-yes-delete": "はい、削除", "groups-delete-the-group": "グループの削除", "groups-group-deleted": "グループが削除されました", "groups-confirm-delete-customer-group": "この顧客グループを削除してもよろしいですか？", "groups-back-to-customer-groups": "顧客グループに戻る", "groups-new-group": "新しいグループ", "add-condition-conditions-were-successfully-added": "条件が正常に追加されました", "add-condition-discount-conditions-updated": "割引条件が更新されました", "add-condition-use-conditions-must-be-used-within-a-conditions-provider": "useConditionsはConditionsProvider内で使用する必要があります", "collections-search": "検索...", "collections-cancel": "キャンセル", "collections-save-and-go-back": "保存して戻る", "collections-save-and-close": "保存して閉じる", "customer-groups-search": "検索...", "customer-groups-cancel": "キャンセル", "customer-groups-save-and-go-back": "保存して戻る", "customer-groups-save-and-close": "保存して閉じる", "product-types-search": "検索...", "product-types-cancel": "キャンセル", "product-types-save-and-go-back": "保存して戻る", "product-types-save-and-close": "保存して閉じる", "products-search": "検索...", "products-cancel": "キャンセル", "products-save-and-go-back": "保存して戻る", "products-save-and-close": "保存して閉じる", "tags-search": "検索...", "tags-cancel": "キャンセル", "tags-save-and-go-back": "保存して戻る", "tags-save-and-close": "保存して閉じる", "edit-condition-add-conditions": "条件を追加", "edit-condition-selected-with-count_one": "{{count}}", "edit-condition-selected-with-count_other": "{{count}}", "edit-condition-deselect": "選択解除", "edit-condition-remove": "削除", "edit-condition-add": "追加", "edit-condition-title": "割引条件で{{type}}を編集", "edit-condition-close": "閉じる", "edit-condition-success": "成功", "edit-condition-the-resources-were-successfully-added": "リソースが正常に追加されました", "edit-condition-error": "エラー", "edit-condition-failed-to-add-resources": "リソースの追加に失敗しました", "edit-condition-the-resources-were-successfully-removed": "リソースが正常に削除されました", "edit-condition-failed-to-remove-resources": "リソースの削除に失敗しました", "edit-condition-use-edit-condition-context-must-be-used-within-an-edit-condition-provider": "useEditConditionContextはEditConditionProvider内で使用する必要があります", "conditions-conditions": "条件", "conditions-add-condition-label": "条件を追加する", "conditions-this-discount-has-no-conditions": "この割引には条件がありません", "conditions-success": "成功", "conditions-condition-removed": "条件が削除されました", "conditions-error": "エラー", "conditions-edit-condition": "条件を編集する", "conditions-delete-condition": "条件を削除する", "conditions-discount-is-applicable-to-specific-products": "割引は特定の商品に適用されます", "conditions-discount-is-applicable-to-specific-collections": "割引は特定のコレクションに適用されます", "conditions-discount-is-applicable-to-specific-product-tags": "割引は特定の商品タグに適用されます", "conditions-discount-is-applicable-to-specific-product-types": "割引は特定の商品タイプに適用されます", "conditions-discount-is-applicable-to-specific-customer-groups": "割引は特定の顧客グループに適用されます", "configurations-success": "成功", "configurations-discount-updated-successfully": "割引は正常に更新されました", "configurations-error": "エラー", "configurations-edit-configurations": "設定を編集する", "configurations-cancel": "キャンセル", "configurations-save": "保存する", "configurations-configurations": "設定", "configurations-start-date": "開始日", "configurations-end-date": "終了日", "configurations-delete-configuration": "設定削除", "configurations-discount-end-date-removed": "割引終了日を削除しました", "configurations-number-of-redemptions": "割引の使用回数", "configurations-redemption-limit-removed": "割引制限を解除しました", "configurations-delete-setting": "設定削除", "configurations-discount-duration-removed": "割引期間を削除しました", "general-success": "成功", "general-discount-updated-successfully": "割引を更新しました", "general-error": "エラー", "general-edit-general-information": "一般情報を編集", "general-details": "詳細", "general-metadata": "メタデータ", "general-cancel": "キャンセル", "general-save-and-close": "保存して閉じる", "general-delete-promotion": "プロモーションを削除", "general-confirm-delete-promotion": "このプロモーションを削除してもよろしいですか？", "general-promotion-deleted-successfully": "プロモーションを削除しました", "general-discount-published-successfully": "割引を正常に公開しました", "general-discount-drafted-successfully": "割引が正常にドラフトされました", "general-delete-discount": "割引を削除する", "general-template-discount": "テンプレート割引", "general-published": "公開", "general-draft": "ドラフト", "general-discount-amount": "割引金額", "general-valid-regions": "有効地域", "general-total-redemptions": "合計償還回数", "general-free-shipping": "無料配送", "general-unknown-discount-type": "不明な割引タイプ", "details-discount-deleted": "割引が削除されました", "details-confirm-delete-discount": "この割引を削除してもよろしいですか？", "details-delete-discount": "割引を削除する", "details-back-to-discounts": "割引に戻る", "details-raw-discount": "生の割引", "discounts-add-discount": "割引を追加する", "discount-form-add-conditions": "条件を追加する", "discount-form-choose-a-condition-type": "条件のタイプを選択する", "discount-form-you-can-only-add-one-of-each-type-of-condition": "各タイプの条件は1つしか追加できません", "discount-form-you-cannot-add-any-more-conditions": "これ以上条件を追加することはできません", "discount-form-cancel": "キャンセル", "discount-form-save": "保存", "add-condition-tables-cancel": "キャンセル", "add-condition-tables-save-and-add-more": "保存して追加", "add-condition-tables-save-and-close": "保存して閉じる", "add-condition-tables-search-by-title": "タイトルで検索...", "add-condition-tables-search-groups": "グループで検索...", "add-condition-tables-search-products": "製品で検索...", "add-condition-tables-search-by-tag": "タグで検索...", "add-condition-tables-search-by-type": "タイプで検索...", "details-condition-tables-search-by-title": "タイトルで検索...", "details-condition-tables-search-groups": "グループで検索...", "details-condition-tables-cancel": "キャンセル", "details-condition-tables-save-and-add-more": "保存して追加", "details-condition-tables-save-and-close": "保存して閉じる", "details-condition-tables-search-products": "製品で検索...", "details-condition-tables-search-by-tag": "タグで検索...", "details-condition-tables-search-by-type": "タイプで検索...", "edit-condition-tables-search-by-title": "タイトルで検索...", "edit-condition-tables-title": "タイトル", "edit-condition-tables-search-groups": "グループを検索...", "edit-condition-tables-cancel": "キャンセル", "edit-condition-tables-delete-condition": "条件を削除", "edit-condition-tables-save": "保存", "edit-condition-tables-search-products": "商品を検索...", "edit-condition-tables-search-by-tag": "タグで検索...", "edit-condition-tables-search-by-type": "タイプで検索...", "shared-title": "タイトル", "shared-products": "商品", "shared-applies-to-the-selected-items": "選択されたアイテムに適用", "shared-applies-to-all-items-except-the-selected-items": "選択されたアイテムを除くすべてのアイテムに適用", "shared-members": "メンバー", "shared-status": "ステータス", "shared-variants": "バリアント", "shared-tag": "タグ", "shared-type": "タイプ", "edit-conditions-modal-title": "{{title}}を編集", "form-use-discount-form-must-be-a-child-of-discount-form-context": "useDiscountFormはDiscountFormContextの子である必要があります", "discount-form-error": "エラー", "discount-form-save-as-draft": "下書きとして保存", "discount-form-publish-discount": "割引を公開する", "discount-form-create-new-discount": "新しい割引を作成する", "discount-form-discount-type": "割引の種類", "discount-form-select-a-discount-type": "割引の種類を選択する", "discount-form-allocation": "割り当て", "discount-form-general": "一般", "discount-form-configuration": "設定", "discount-form-discount-code-application-disclaimer": "割引コードは公開ボタンを押した時から適用され、そのままにしておけば永久に適用されます。", "discount-form-conditions": "条件", "discount-form-discount-code-apply-to-all-products-if-left-untouched": "割引コードはそのままにしておけばすべての商品に適用されます。", "discount-form-add-conditions-to-your-discount": "割引に条件を追加する", "discount-form-metadata": "メタデータ", "discount-form-metadata-usage-description": "メタデータを使用して割引に追加情報を追加できます。", "condition-item-remainder-more": "+{{remainder}}を追加", "conditions-edit": "編集", "conditions-product": "商品", "conditions-collection": "コレクション", "conditions-tag": "タグ", "conditions-customer-group": "顧客グループ", "conditions-type": "タイプ", "conditions-add-condition": "条件の追加", "sections-start-date": "開始日", "sections-schedule-the-discount-to-activate-in-the-future": "割引を将来の特定の日に設定したい場合は、ここで開始日を設定することができます。そうしない場合、割引はすぐに有効になります。", "sections-select-discount-start-date": "開始時刻", "sections-start-time": "割引に有効期限はありますか？", "sections-discount-has-an-expiry-date": "割引を将来の特定の日に無効にする場合は、ここで有効期限を設定することができます。", "sections-schedule-the-discount-to-deactivate-in-the-future": "有効期限日", "sections-select-discount-end-date": "有効期限時刻", "sections-expiry-date": "特定の顧客毎に適用回数を制限しますか？", "sections-expiry-time": "適用回数の制限は各顧客ではなく、全顧客に適用されます。", "sections-limit-the-number-of-redemptions": "顧客がこの割引を利用できる回数を制限したい場合は、ここで制限を設定することができます。", "sections-limit-applies-across-all-customers-not-per-customer": "適用回数", "sections-limit-discount-number-of-uses": "有効期間はありますか？", "sections-number-of-redemptions": "割引の期間を設定します。", "sections-availability-duration": "割引タイプを選択してください", "sections-set-the-duration-of-the-discount": "合計金額", "sections-select-a-discount-type": "合計金額に適用します", "sections-total-amount": "商品ごとに適用します", "sections-apply-to-every-allowed-item": "全ての許可された商品に適用する", "sections-percentage": "割合", "sections-fixed-amount": "固定金額", "sections-discount-in-whole-numbers": "整数の割引", "sections-you-can-only-select-one-valid-region-if-you-want-to-use-the-fixed-amount-type": "固定金額のタイプを使用する場合は、有効なリージョンを1つだけ選択できます", "sections-free-shipping": "送料無料", "sections-override-delivery-amount": "配送料金を上書きする", "sections-at-least-one-region-is-required": "少なくとも1つのリージョンが必要です", "sections-choose-valid-regions": "有効なリージョンを選択してください", "sections-code": "コード", "sections-summersale-10": "SUMMERSALE10", "sections-code-is-required": "コードは必須です", "sections-amount-is-required": "金額は必須です", "sections-amount": "金額", "sections-customer-invoice-code": "お客様がチェックアウト時に入力するコードです。お客様の請求書に表示されます。", "sections-uppercase-letters-and-numbers-only": "大文字の英数字のみ可能です。", "sections-description": "説明", "sections-summer-sale-2022": "2022年のサマーセール", "sections-this-is-a-template-discount": "これはテンプレートの割引です", "sections-template-discounts-description": "テンプレートの割引を使用すると、一連の割引に適用できるルールを定義できます。各ユーザーに対してユニークなコードを生成する必要があるが、すべてのユニークなコードのルールは同じである場合に便利です。", "discount-form-product": "製品", "discount-form-only-for-specific-products": "特定の製品のみ", "discount-form-choose-products": "製品を選ぶ", "discount-form-customer-group": "顧客グループ", "discount-form-only-for-specific-customer-groups": "特定の顧客グループのみ", "discount-form-choose-groups": "グループを選ぶ", "discount-form-tag": "タグ", "discount-form-only-for-specific-tags": "特定のタグのみ", "discount-form-collection": "コレクション", "discount-form-only-for-specific-product-collections": "特定の商品コレクションのみ", "discount-form-choose-collections": "コレクションを選ぶ", "discount-form-type": "タイプ", "discount-form-only-for-specific-product-types": "特定の商品タイプのみ", "discount-form-choose-types": "タイプを選ぶ", "utils-products": "製品", "utils-groups": "グループ", "utils-tags": "タグ", "utils-collections": "コレクション", "utils-types": "タイプ", "gift-cards-created-gift-card": "作成されたギフトカード", "gift-cards-custom-gift-card-was-created-successfully": "カスタムギフトカードは正常に作成されました", "gift-cards-error": "エラー", "gift-cards-custom-gift-card": "カスタムギフトカード", "gift-cards-details": "詳細", "gift-cards-receiver": "受取人", "gift-cards-cancel": "キャンセル", "gift-cards-create-and-send": "作成して送信", "details-updated-gift-card": "更新されたギフトカード", "details-gift-card-was-successfully-updated": "ギフトカードの更新に成功しました", "details-failed-to-update-gift-card": "ギフトカードの更新に失敗しました", "details-edit-gift-card": "ギフトカードの編集", "details-details": "詳細", "details-edit-details": "詳細の編集", "details-update-balance-label": "残高を更新", "details-updated-status": "ステータスの更新", "details-successfully-updated-the-status-of-the-gift-card": "ギフトカードのステータスを正常に更新しました", "details-back-to-gift-cards": "ギフトカードに戻る", "details-original-amount": "元の金額", "details-balance": "残高", "details-region": "地域", "details-expires-on": "有効期限", "details-created": "作成日", "details-raw-gift-card": "ギフトカード", "details-balance-updated": "残高が更新されました", "details-gift-card-balance-was-updated": "ギフトカードの残高が更新されました", "details-failed-to-update-balance": "残高の更新に失敗しました", "details-update-balance": "残高を更新する", "manage-back-to-gift-cards": "ギフトカードに戻る", "gift-cards-please-enter-a-name-for-the-gift-card": "ギフトカードの名前を入力してください", "gift-cards-please-add-at-least-one-denomination": "少なくとも1つの金額を追加してください", "gift-cards-denominations": "金額", "gift-cards-success": "成功", "gift-cards-successfully-created-gift-card": "ギフトカードが正常に作成されました", "gift-cards-create-gift-card": "ギフトカードを作成する", "gift-cards-gift-card-details": "ギフトカードの詳細", "gift-cards-name": "名前", "gift-cards-the-best-gift-card": "最高のギフトカード", "gift-cards-description": "説明", "gift-cards-the-best-gift-card-of-all-time": "史上最高のギフトカード", "gift-cards-thumbnail": "サムネイル", "gift-cards-delete": "削除する", "gift-cards-size-recommended": "推奨の1200 x 1600（3：4）、10MBまで", "gift-cards-amount": "金額", "gift-cards-add-denomination": "額面を追加する", "gift-cards-create-publish": "作成＆公開", "gift-cards-successfully-updated-gift-card": "ギフトカードが正常に更新されました", "gift-cards-gift-cards": "ギフトカード", "gift-cards-manage": "自分のMedusaストアのギフトカードを管理する", "gift-cards-are-you-ready-to-sell-your-first-gift-card": "最初のギフトカードを販売する準備はできていますか？", "gift-cards-no-gift-card-has-been-added-yet": "まだギフトカードは追加されていません。", "gift-cards-history": "履歴", "gift-cards-see-the-history-of-purchased-gift-cards": "購入済みギフトカードの履歴を見る", "gift-cards-successfully-deleted-gift-card": "ギフトカードが正常に削除されました", "gift-cards-yes-delete": "はい、削除する", "gift-cards-delete-gift-card": "ギフトカードを削除する", "inventory-filters": "フィルタ", "address-form-address": "住所", "address-form-company": "会社", "address-form-address-1": "住所1", "address-form-this-field-is-required": "このフィールドは必須です", "address-form-address-2": "住所2", "address-form-postal-code": "郵便番号", "address-form-city": "市", "address-form-country": "国", "edit-sales-channels-edit-channels": "チャンネルの編集", "edit-sales-channels-add-channels": "チャンネルの追加", "general-form-location-name": "ロケーション名", "general-form-flagship-store-warehouse": "旗艦ストア、倉庫", "general-form-name-is-required": "名前は必須です", "location-card-delete-location": "ロケーションの削除", "location-card-confirm-delete": "このロケーションを削除してもよろしいですか。これにより、このロケーションに関連するすべての在庫レベルと予約が削除されます。", "location-card-success": "成功", "location-card-location-deleted-successfully": "ロケーションが正常に削除されました", "location-card-error": "エラー", "location-card-edit-details": "詳細の編集", "location-card-delete": "削除", "location-card-connected-sales-channels": "接続された販売チャンネル", "sales-channels-form-add-sales-channels": "販売チャンネルの追加", "sales-channels-form-edit-channels": "チャンネルの編集", "sales-channels-section-not-connected-to-any-sales-channels-yet": "まだどの販売チャンネルにも接続されていません", "edit-success": "成功", "edit-location-edited-successfully": "場所の編集が成功しました", "edit-error": "エラー", "edit-edit-location-details": "場所の詳細を編集する", "edit-metadata": "メタデータ", "edit-cancel": "キャンセル", "edit-save-and-close": "保存して閉じる", "new-location-added-successfully": "場所の追加が成功しました", "new-location-created": "場所は正常に作成されましたが、セールスチャネルの関連付けにエラーが発生しました", "new-cancel-location-changes": "保存されていない変更をキャンセルしてもよろしいですか", "new-yes-cancel": "はい、キャンセルします", "new-no-continue-creating": "いいえ、作成を続行します", "new-add-location": "場所を追加する", "new-add-new-location": "新しい場所を追加する", "new-general-information": "一般情報", "new-location-details": "この場所に関する詳細を指定する", "new-select-location-channel": "この場所のアイテムが購入できるセールスチャネルを指定します。", "oauth-complete-installation": "完全なインストール", "claim-type-form-refund": "返金", "claim-type-form-replace": "置き換える", "items-to-receive-form-items-to-receive": "受け取るアイテム", "items-to-receive-form-product": "商品", "items-to-receive-form-quantity": "数量", "items-to-receive-form-refundable": "返金可", "add-return-reason-reason-for-return": "返品理由", "add-return-reason-reason": "理由", "add-return-reason-choose-a-return-reason": "返品理由を選択", "add-return-reason-note": "メモ", "add-return-reason-product-was-damaged-during-shipping": "商品が配送中に破損しました", "add-return-reason-cancel": "キャンセル", "add-return-reason-save-and-go-back": "保存して戻る", "add-return-reason-select-reason-title": "理由を選択する", "add-return-reason-edit-reason": "理由を編集する", "add-return-reason-select-reason": "理由を選択する", "items-to-return-form-items-to-claim": "請求するアイテム", "items-to-return-form-items-to-return": "返品するアイテム", "items-to-return-form-product": "商品", "items-to-return-form-quantity": "数量", "items-to-return-form-refundable": "返金可", "add-additional-items-screen-go-back": "戻る", "add-additional-items-screen-add-products": "商品を追加する", "add-additional-items-screen-add-product-variants": "製品のバリアントを追加する", "add-additional-items-screen-search-products": "商品を検索する", "add-additional-items-screen-variant-price-missing": "このバリアントは、注文の地域/通貨に対する価格が設定されておらず、選択できません。", "add-additional-items-screen-stock": "在庫", "add-additional-items-screen-price": "価格", "add-additional-items-screen-price-overridden-in-price-list-applicable-to-this-order": "この注文に適用されるプライスリストで価格が上書きされました。", "items-to-send-form-items-to-send": "発送するアイテム", "items-to-send-form-add-products": "商品を追加する", "items-to-send-form-product": "商品", "items-to-send-form-quantity": "数量", "items-to-send-form-price": "価格", "items-to-send-form-price-overridden-in-price-list-applicable-to-this-order": "この注文に適用されるプライスリストで価格が上書きされました。", "refund-amount-form-cancel-editing-refund-amount": "返金金額の編集をキャンセルする", "refund-amount-form-edit-refund-amount": "返金金額を編集する", "refund-amount-form-refund-amount-cannot-be-negative": "返金金額はマイナスにすることはできません", "refund-amount-form-the-refund-amount-must-be-at-least-0": "返金金額は少なくとも0でなければなりません", "reservation-indicator-awaiting-reservation-count": "{{awaitingReservation}}個のアイテムが予約されていません", "reservation-indicator-this-item-has-been-fulfilled": "このアイテムは履行されました。", "edit-reservation-button-quantity-item-location-name": "{{quantity}}個のアイテム: ${{locationName}}", "reservation-indicator-edit-reservation": "予約の編集", "rma-summaries-claimed-items": "請求された商品", "rma-summaries-replacement-items": "交換アイテム", "rma-summaries-customer-refund-description": "顧客は請求された商品に対して全額返金されますが、交換アイテムや配送料金は差し引かれません。代わりに、返品された商品を受け取った際にカスタムな返金額を設定するか、交換を作成することも選択できます。", "rma-summaries-refund-amount": "返金額", "rma-summaries-the-customer-will-be-refunded-once-the-returned-items-are-received": "返品された商品を受け取った後に顧客に返金されます", "rma-summaries-the-customer-will-be-refunded-immediately": "顧客に即座に返金されます", "rma-summaries-receiving": "受け取り", "rma-summaries-free": "無料", "send-notification-form-return": "返品", "send-notification-form-exchange": "交換", "send-notification-form-claim": "請求", "send-notification-form-send-notifications": "通知を送信する", "send-notification-form-if-unchecked-the-customer-will-not-receive-communication": "チェックを外すと顧客はこの{{subject}}についての通知を受け取りません。", "shipping-address-form-shipping-address": "配送先住所", "shipping-address-form-ship-to-a-different-address": "別の住所に配送する", "shipping-address-form-cancel": "キャンセル", "shipping-address-form-save-and-go-back": "保存して戻る", "shipping-address-form-shipping-information": "配送情報", "shipping-form-shipping-for-return-items": "返品品の配送", "shipping-form-shipping-for-replacement-items": "交換品の発送", "shipping-form-shipping-method-is-required": "発送方法が必要です", "shipping-form-choose-shipping-method": "発送方法を選択してください", "shipping-form-shipping-method": "発送方法", "shipping-form-add-custom-price": "カスタム価格を追加する", "shipping-form-return-shipping-for-items-claimed-by-the-customer-is-complimentary": "顧客が主張した商品の返送は無料です。", "shipping-form-shipping-for-replacement-items-is-complimentary": "交換品の発送は無料です。", "components-decrease-quantity": "数量を減らす", "components-increase-quantity": "数量を増やす", "details-successfully-updated-address": "住所が正常に更新されました", "details-billing-address": "請求先住所", "details-shipping-address": "配送先住所", "details-contact": "連絡先", "details-location": "場所", "claim-are-you-sure-you-want-to-close": "本当に閉じますか？", "claim-you-have-unsaved-changes-are-you-sure-you-want-to-close": "保存されていない変更があります。本当に閉じますか？", "claim-please-select-a-reason": "理由を選択してください", "claim-a-shipping-method-for-replacement-items-is-required": "交換品の発送方法が必要です", "claim-successfully-created-claim": "クレームが正常に作成されました", "claim-created": "注文番号{{display_id}}のクレームが正常に作成されました", "claim-error-creating-claim": "請求の作成エラー", "claim-create-claim": "請求の作成", "claim-location": "場所", "claim-choose-which-location-you-want-to-return-the-items-to": "商品を返品する場所を選択してください。", "claim-select-location-to-return-to": "返品先場所を選択", "claim-cancel": "キャンセル", "claim-submit-and-close": "送信して閉じる", "create-fulfillment-error": "エラー", "create-fulfillment-please-select-a-location-to-fulfill-from": "満たす場所を選択してください", "create-fulfillment-cant-allow-this-action": "この操作は許可できません", "create-fulfillment-trying-to-fulfill-more-than-in-stock": "在庫以上を満たそうとしています", "create-fulfillment-successfully-fulfilled-order": "注文の完了", "create-fulfillment-successfully-fulfilled-swap": "交換の完了", "create-fulfillment-successfully-fulfilled-claim": "請求の完了", "create-fulfillment-success": "成功", "create-fulfillment-cancel": "キャンセル", "create-fulfillment-create-fulfillment": "フルフィルメントの作成", "create-fulfillment-create-fulfillment-title": "フルフィルメントの作成", "create-fulfillment-locations": "場所", "create-fulfillment-choose-where-you-wish-to-fulfill-from": "満たす場所を選択してください。", "create-fulfillment-items-to-fulfill": "発注するアイテム", "create-fulfillment-select-the-number-of-items-that-you-wish-to-fulfill": "発注するアイテムの数を選択してください。", "create-fulfillment-send-notifications": "通知を送信する", "create-fulfillment-when-toggled-notification-emails-will-be-sent": "オンにすると、通知メールが送信されます。", "create-fulfillment-quantity-is-not-valid": "数量が無効です", "detail-cards-allocated": "割り当て済み", "detail-cards-not-fully-allocated": "完全には割り当てられていません", "detail-cards-subtotal": "小計", "detail-cards-shipping": "出荷", "detail-cards-tax": "税金", "detail-cards-total": "合計", "detail-cards-edit-order": "注文を編集", "detail-cards-allocate": "割り当て", "detail-cards-discount": "割引:", "detail-cards-original-total": "元の合計", "details-successfully-updated-the-email-address": "メールアドレスが正常に更新されました", "details-email-address": "メールアドレス", "details-save": "保存", "details-order-id-copied": "注文IDがコピーされました", "details-email-copied": "メールがコピーされました", "details-cancel-order-heading": "注文をキャンセルする", "details-are-you-sure-you-want-to-cancel-the-order": "注文をキャンセルしてもよろしいですか？", "order-details-display-id": "注文 #{{display_id}}", "details-successfully-canceled-order": "注文が正常にキャンセルされました", "details-go-to-customer": "顧客ページに移動する", "details-transfer-ownership": "所有権を譲渡する", "details-edit-shipping-address": "配送先を編集する", "details-edit-billing-address": "請求先住所を編集する", "details-edit-email-address": "メールアドレスを編集する", "details-back-to-orders": "注文一覧に戻る", "details-cancel-order": "注文をキャンセルする", "details-payment": "支払い", "details-refunded": "返金済み", "details-total-paid": "合計支払額", "details-fulfillment": "発送", "details-create-fulfillment": "発送を作成する", "details-shipping-method": "配送方法", "details-customer": "顧客", "details-shipping": "配送先", "details-billing": "請求先", "details-raw-order": "生の注文", "mark-shipped-successfully-marked-order-as-shipped": "注文が正常に出荷されました", "mark-shipped-successfully-marked-swap-as-shipped": "交換が正常に出荷されました", "mark-shipped-successfully-marked-claim-as-shipped": "クレームが正常に出荷されました", "mark-shipped-success": "成功", "mark-shipped-error": "エラー", "mark-shipped-mark-fulfillment-shipped": "フルフィルメントの出荷がマークされました", "mark-shipped-tracking": "追跡", "mark-shipped-tracking-number-label": "追跡番号", "mark-shipped-tracking-number": "追跡番号...", "mark-shipped-add-additional-tracking-number": "+ 追加の追跡番号を追加", "mark-shipped-send-notifications": "通知を送信", "mark-shipped-cancel": "キャンセル", "mark-shipped-complete": "完了", "order-line-warning": "警告", "order-line-cannot-duplicate-an-item-without-a-variant": "バリアントなしでアイテムを複製することはできません", "order-line-error": "エラー", "order-line-failed-to-duplicate-item": "アイテムの複製に失敗しました", "order-line-success": "成功", "order-line-item-removed": "アイテムが削除されました", "order-line-failed-to-remove-item": "アイテムの削除に失敗しました", "order-line-item-added": "アイテムが追加されました", "order-line-failed-to-replace-the-item": "アイテムの置き換えに失敗しました", "order-line-replace-product-variants": "製品のバリアントを置き換える", "order-line-replace-with-other-item": "他のアイテムで置き換える", "order-line-duplicate-item": "重複したアイテム", "order-line-remove-item": "アイテムを削除する", "order-line-line-item-cannot-be-edited": "このラインアイテムは発送の一部であり、編集することはできません。ラインアイテムを編集するには、発送をキャンセルしてください。", "order-line-new": "新規", "order-line-modified": "修正済み", "receive-return-please-select-at-least-one-item-to-receive": "少なくとも1つのアイテムを選んで受け取ってください", "receive-return-successfully-received-return": "返品が正常に受け付けられました", "receive-return-received-return-for-order": "注文番号{{display_id}}の返品を受け取りました", "receive-return-failed-to-receive-return": "返品の受け取りに失敗しました", "receive-return-receive-return": "返品を受け取る", "receive-return-location": "場所", "receive-return-choose-location": "アイテムを返品する場所を選択してください。", "receive-return-select-location-to-return-to": "返品する場所を選択してください", "receive-return-no-inventory-levels-exist-for-the-items-at-the-selected-location": "選択した場所にアイテムの在庫レベルは存在しません", "receive-return-cancel": "キャンセル", "receive-return-save-and-close": "保存して閉じる", "refund-success": "成功", "refund-successfully-refunded-order": "注文が正常に返金されました", "refund-error": "エラー", "refund-create-a-refund": "返金を作成する", "refund-attention": "注意！", "refund-system-payment-disclaimer": "一つ以上の支払いがシステム支払いです。このような支払いについては、キャプチャや返金はMedusaで処理されませんのでご注意ください。", "refund-details": "詳細", "refund-cannot-refund-more-than-the-orders-net-total": "注文の純合計を超える返金はできません。", "refund-discount": "割引", "refund-reason": "理由", "refund-note": "メモ", "refund-discount-for-loyal-customer": "忠実なお客様への割引", "refund-send-notifications": "通知を送信する", "refund-cancel": "キャンセル", "refund-complete": "完了", "reservation-reservation-was-deleted": "予約が削除されました", "reservation-the-allocated-items-have-been-released": "割り当てられた商品は解放されました。", "reservation-error": "エラー", "reservation-failed-to-delete-the-reservation": "予約の削除に失敗しました。", "reservation-reservation-was-updated": "予約が更新されました", "reservation-the-reservation-change-was-saved": "予約の変更が保存されました。", "reservation-errors": "エラー", "reservation-failed-to-update-reservation": "予約の更新に失敗しました", "reservation-edit-reservation": "予約を編集", "reservation-location": "場所", "reservation-choose-which-location-you-want-to-ship-the-items-from": "アイテムを発送する場所を選択してください。", "reservation-items-to-allocate-title": "アロケートするアイテム", "reservation-select-the-number-of-items-that-you-wish-to-allocate": "アロケートするアイテムの数を選択してください。", "reservation-max-reservation-requested": " / {{maxReservation}} リクエスト中", "reservation-reserved": "予約済み", "reservation-description": "説明", "reservation-what-type-of-reservation-is-this": "この予約のタイプは何ですか？", "reservation-metadata": "メタデータ", "reservation-remove-metadata": "メタデータを削除", "reservation-add-metadata": "メタデータを追加", "reservation-delete-reservation": "予約を削除", "reservation-cancel": "キャンセル", "reservation-save-and-close": "保存して閉じる", "reservation-couldnt-allocate-items": "アイテムをアロケートできませんでした", "reservation-items-allocated": "割り当てられたアイテム", "reservation-items-have-been-allocated-successfully": "アイテムは正常に割り当てられました", "reservation-save-reservation": "予約を保存する", "reservation-loading": "読込中...", "reservation-allocate-order-items": "注文アイテムを割り当てる", "reservation-choose-where-you-wish-to-allocate-from": "割り当てる場所を選択してください", "reservation-items-to-allocate": "割り当てるアイテム", "returns-success": "成功", "returns-successfully-returned-order": "注文が正常に返品されました", "returns-error": "エラー", "returns-request-return": "返品をリクエストする", "returns-items-to-return": "返品するアイテム", "returns-choose-which-location-you-want-to-return-the-items-to": "アイテムを返品する場所を選択してください", "returns-select-location-to-return-to": "返品先の場所を選択する", "returns-selected-location-has-no-inventory-levels": "選択した場所には選択したアイテムの在庫レベルがありません。返品はリクエストできますが、選択した場所の在庫レベルが作成されるまで受け取ることはできません。", "returns-shipping": "配送", "returns-choose-retur,-shipping-method": "この返品に使用する配送方法を選択してください。", "returns-total-refund": "総払い戻し額", "returns-amount": "金額", "returns-send-notifications": "通知を送信する", "returns-notify-customer-of-created-return": "返品の作成を顧客に通知する", "returns-back": "戻る", "returns-submit": "提出する", "rma-sub-modals-search-for-additional": "追加の検索", "rma-sub-modals-general": "一般", "rma-sub-modals-first-name": "名前", "rma-sub-modals-last-name": "姓", "rma-sub-modals-phone": "電話", "rma-sub-modals-shipping-address": "配送先住所", "rma-sub-modals-address-1": "住所1", "rma-sub-modals-address-2": "住所2", "rma-sub-modals-province": "都道府県", "rma-sub-modals-postal-code": "郵便番号", "rma-sub-modals-city": "市区町村", "rma-sub-modals-country": "国", "rma-sub-modals-back": "戻る", "rma-sub-modals-add": "追加", "rma-sub-modals-name": "名前", "rma-sub-modals-status": "ステータス", "rma-sub-modals-in-stock": "在庫あり", "rma-sub-modals-products": "商品", "rma-sub-modals-search-products": "商品を検索する...", "rma-sub-modals-reason-for-return": "返品理由", "rma-sub-modals-reason": "理由", "rma-sub-modals-note": "注意事項", "swap-success": "成功", "swap-successfully-created-exchange": "交換を成功裏に作成しました", "swap-error": "エラー", "swap-register-exchange": "交換を登録する", "swap-items-to-return": "返品項目", "swap-shipping": "配送", "swap-shipping-method": "配送方法", "swap-add-a-shipping-method": "配送方法を追加する", "swap-location": "場所", "swap-choose-which-location-you-want-to-return-the-items-to": "商品を返品する場所を選択してください。", "swap-select-location-to-return-to": "返品先を選択する", "swap-items-to-send": "送る商品", "swap-add-product": "商品を追加する", "swap-return-total": "返品合計", "swap-additional-total": "追加合計", "swap-outbond-shipping": "出荷", "swap-calculated-at-checkout": "レジで計算", "swap-estimated-difference": "見積もりの差分", "swap-send-notifications": "通知を送る", "swap-if-unchecked-the-customer-will-not-receive-communication-about-this-exchange": "チェックを外すと、お客様はこの交換に関する連絡を受け取りません", "swap-complete": "完了", "templates-shipped": "出荷済み", "templates-fulfilled": "履行済み", "templates-canceled": "キャンセル済み", "templates-partially-fulfilled": "一部履行済み", "templates-fulfillment-status-requires-action": "アクションが必要です", "templates-awaiting-fulfillment": "発送待ち", "templates-partially-shipped": "一部出荷済み", "templates-cancel-fulfillment-heading": "履行をキャンセルしますか？", "templates-are-you-sure-you-want-to-cancel-the-fulfillment": "本当に履行をキャンセルしますか？", "templates-successfully-canceled-swap": "交換を正常にキャンセルしました", "templates-error": "エラー", "templates-successfully-canceled-claim": "請求を正常にキャンセルしました", "templates-successfully-canceled-fulfillment": "履行を正常にキャンセルしました", "templates-fulfillment-has-been-canceled": "履行がキャンセルされました", "templates-fulfilled-by-provider": "{{title}} {{provider}}によって履行されます", "templates-not-shipped": "未発送", "templates-tracking": "追跡", "templates-shipped-from": "出荷元", "templates-shipping-from": "出荷", "templates-mark-shipped": "出荷済みとする", "templates-cancel-fulfillment": "履行のキャンセル", "templates-completed": "完了", "templates-processing": "処理中", "templates-requires-action": "アクションが必要", "templates-capture-payment": "支払いのキャプチャ", "templates-successfully-captured-payment": "支払いが正常にキャプチャされました", "templates-refund": "返金", "templates-total-for-swaps": "スワップの合計", "templates-refunded-for-swaps": "スワップの返金", "templates-refunded-for-returns": "返品の返金", "templates-manually-refunded": "手動で返金済み", "templates-net-total": "純計", "templates-paid": "支払われた金額", "templates-awaiting-payment": "支払い待ち", "templates-payment-status-requires-action": "アクションが必要です", "draft-orders-completed": "完了しました", "draft-orders-open": "オープン", "draft-orders-mark-as-paid": "支払い済みとしてマーク", "draft-orders-success": "成功", "draft-orders-successfully-mark-as-paid": "正常に支払い済みとしてマークされました", "draft-orders-error": "エラー", "draft-orders-successfully-canceled-order": "注文が正常にキャンセルされました", "draft-orders-back-to-draft-orders": "下書き注文に戻る", "on-mark-as-paid-confirm-order-id": "注文番号 #{{display_id}}", "draft-orders-go-to-order": "注文に移動", "draft-orders-cancel-draft-order": "下書き注文をキャンセル", "draft-orders-draft-order": "下書き注文", "draft-orders-email": "メール", "draft-orders-phone": "電話", "draft-orders-amount": "金額 {{currency_code}}", "draft-orders-payment": "支払い", "draft-orders-subtotal": "小計", "draft-orders-shipping": "配送", "draft-orders-tax": "税金", "draft-orders-total-to-pay": "支払い合計", "draft-orders-payment-link": "支払いリンク:", "draft-orders-configure-payment-link-in-store-settings": "店舗設定で支払いリンクを設定する", "draft-orders-shipping-method": "配送方法", "draft-orders-data": "データ", "draft-orders-1-item": "（1個のアイテム）", "draft-orders-customer": "お客様", "draft-orders-edit-shipping-address": "配送先住所を編集", "draft-orders-edit-billing-address": "請求先住所を編集", "draft-orders-go-to-customer": "お客様に移動", "draft-orders-contact": "連絡先", "draft-orders-billing": "請求先", "draft-orders-raw-draft-order": "仮の草稿注文", "draft-orders-are-you-sure": "本当によろしいですか？", "draft-orders-remove-resource-heading": "{{resource}}を削除します", "draft-orders-remove-resource-success-text": "{{resource}}が削除されました", "draft-orders-this-will-create-an-order-mark-this-as-paid-if-you-received-the-payment": "これにより注文が作成されます。支払いを受け取った場合は、支払い済みとマークしてください。", "draft-orders-mark-paid": "支払い済みにする", "draft-orders-cancel": "キャンセル", "draft-orders-create-draft-order": "仮注文を作成する", "edit-amount-paid": "支払い済み金額", "edit-new-total": "新しい合計金額", "edit-difference-due": "差額", "edit-back": "戻る", "edit-save-and-go-back": "保存して戻る", "edit-order-edit-set-as-requested": "注文編集をリクエストしました", "edit-failed-to-request-confirmation": "確認リクエストに失敗しました", "edit-added-successfully": "追加しました", "edit-error-occurred": "エラーが発生しました", "edit-add-product-variants": "商品バリアントを追加", "edit-edit-order": "注文を編集", "edit-items": "アイテム", "edit-add-items": "アイテムを追加", "edit-filter-items": "アイテムをフィルタリング...", "edit-note": "ノート", "edit-add-a-note": "ノートを追加...", "variants-table-location": "{{location}}に", "edit-product": "商品", "edit-in-stock": "在庫あり", "edit-price": "価格", "edit-products": "商品", "edit-search-product-variants": "商品バリアントを検索...", "orders-success": "成功", "orders-successfully-initiated-export": "エクスポートが正常に開始されました", "orders-error": "エラー", "orders-export-orders": "注文をエクスポート", "components-billing-address": "請求先住所", "components-use-same-as-shipping": "配送先と同じに利用", "components-e-g-gift-wrapping": "例：ギフトラッピング", "components-title": "タイトル", "components-price": "価格", "components-quantity": "数量", "components-back": "戻る", "components-add": "追加", "components-items-for-the-order": "注文のアイテム", "components-details": "詳細", "components-price-excl-taxes": "価格（税抜き）", "components-add-custom": "カスタムを追加", "components-add-existing": "既存のものを追加", "components-add-products": "商品を追加する", "components-add-custom-item": "カスタムアイテムを追加する", "components-choose-region": "地域を選択する", "components-region": "地域", "select-shipping-to-name": "（{{name}}へ）", "components-attention": "注意！", "components-no-options-for-orders-without-shipping": "配送なしの注文オプションがありません。地域設定で「ウェブサイトに表示しない」にチェックを外して、例えば「店内受け取り」を追加してください。そして続けてください。", "components-choose-a-shipping-method": "配送方法を選択する", "components-set-custom-price": "カスタム価格を設定する", "components-custom-price": "カスタム価格", "components-customer-and-shipping-details": "顧客と配送詳細", "components-find-existing-customer": "既存の顧客を検索する", "components-email": "メール", "components-choose-existing-addresses": "既存の住所を選択する", "components-create-new": "新規作成", "components-the-discount-is-not-applicable-to-the-selected-region": "割引は選択した地域に適用されません", "components-the-discount-code-is-invalid": "割引コードが無効です", "components-add-discount": "割引を追加する", "components-summer-10": "SUMMER10", "components-discount": "割引", "select-shipping-code": "（コード：{{code}}）", "components-type": "タイプ", "components-value": "値", "components-address": "住所", "components-shipping-method": "配送方法", "components-billing-details": "請求詳細", "components-edit": "編集", "form-use-new-order-form-must-be-used-within-new-order-form-provider": "useNewOrderFormはNewOrderFormProvider内で使用する必要があります", "new-order-created": "注文が作成されました", "new-create-draft-order": "ドラフト注文を作成する", "price-list-product-filter-created-at": "作成日時", "price-list-product-filter-updated-at": "更新日時", "price-list-details-drawer-prompt-title": "確認してもよろしいですか？", "price-list-details-drawer-prompt-description": "保存されていない変更があります。本当に終了しますか？", "price-list-details-notification-succes-title": "価格リストが更新されました", "price-list-details-drawer-notification-success-message": "価格リストが正常に更新されました", "price-list-details-drawer-notification-error-title": "エラーが発生しました", "price-list-details-drawer-title": "価格リストの詳細を編集する", "price-list-details-drawer-cancel-button": "キャンセル", "price-list-details-drawer-save-button": "保存", "price-list-details-section-prompt-confirm-text": "削除", "price-list-details-section-prompt-cancel-text": "キャンセル", "price-list-details-section-prompt-title": "価格リストの削除", "price-list-details-section-prompt-description": "価格リスト \"{{name}}\" を削除してもよろしいですか？", "price-list-details-section-delete-notification-success-title": "価格リストを削除しました", "price-list-details-section-delete-notification-success-message": "価格リスト \"{{name}}\" を正常に削除しました", "price-list-details-section-delete-notification-error-title": "価格リストの削除に失敗しました", "price-list-details-section-customer-groups": "顧客グループ", "price-list-details-section-last-edited": "最終編集", "price-list-details-section-number-of-prices": "価格", "price-list-details-section-status-menu-expired": "期限切れ", "price-list-details-section-status-menu-draft": "下書き", "price-list-details-section-status-menu-scheduled": "スケジュール", "price-list-details-section-status-active": "有効", "price-list-details-section-status-menu-notification-success-title": "価格リストのステータスを正常に更新しました", "price-list-details-section-status-menu-notification-success-message": "価格リストのステータスが {{status}} に正常に更新されました", "price-list-details-section-status-menu-notification-error-title": "価格リストのステータスの更新に失敗しました", "price-list-details-section-status-menu-item-draft": "下書き", "price-list-details-section-status-menu-item-activate": "アクティベート", "price-list-details-menu-item-edit": "詳細の編集", "price-list-details-menu-item-delete": "削除", "price-list-edit-error": "価格リストの読み込み中にエラーが発生しました。ページを再度読み込んで、もう一度お試しください。問題が解決しない場合は、後でもう一度お試しください。", "price-list-new-form-prompt-title": "本当によろしいですか？", "price-list-new-form-prompt-exit-description": "変更が保存されていません。本当に終了しますか？", "price-list-new-form-prompt-back-description": "変更が保存されていません。本当に戻りますか？", "price-list-add-products-modal-no-prices-error": "少なくとも1つの商品に価格を割り当ててください。", "price-list-add-products-modal-missing-prices-title": "未完了の価格リスト", "price-list-add-products-modal-missing-prices-description": "選択したすべての商品に価格が割り当てられていません。続行しますか？", "price-list-add-products-modal-success-title": "新しい価格が追加されました", "price-list-add-products-modal-success-message": "新しい価格が価格リストに追加されました。", "price-list-add-products-modal-error-title": "エラーが発生しました", "price-list-add-products-modal-back-button-cancel": "キャンセル", "price-list-add-products-modal-back-button": "戻る", "price-list-add-products-modal-next-button-continue": "続行する", "price-list-add-products-modal-next-button-submit-and-close": "送信して閉じる", "price-list-add-products-modal-next-button-continue-save-prices": "価格を保存する", "price-list-add-products-modal-products-tab": "商品を選択する", "price-list-add-products-modal-prices-tab": "価格を編集する", "price-list-add-products-modal-error": "フォームの準備中にエラーが発生しました。ページを再度読み込んで、もう一度お試しください。問題が解決しない場合は、後でもう一度お試しください。", "price-list-edit-prices-modal-prompt-title": "保存されていない変更", "price-list-edit-prices-modal-prompt-exit-description": "変更が保存されていません。本当に終了しますか？", "price-list-edit-prices-modal-prompt-back-description": "変更内容が保存されていません。戻りますか？", "price-list-edit-prices-modal-notification-update-error": "エラーが発生しました", "price-list-edit-prices-modal-notification-remove-error-title": "エラーが発生しました", "price-list-edit-prices-modal-notification-remove-error-description": "一部の価格が正しく更新されませんでした。 もう一度試してみてください。", "price-list-edit-prices-modal-notification-update-success-title": "価格が更新されました", "price-list-edit-prices-modal-notification-update-success-description": "価格が正常に更新されました", "price-list-edit-prices-modal-next-button-save-and-close": "保存して閉じる", "price-list-edit-prices-modal-next-button-save": "価格を保存する", "price-list-edit-prices-modal-back-button-cancel": "キャンセル", "price-list-edit-prices-modal-back-button-back": "戻る", "price-list-edit-prices-modal-overview-tab": "価格を編集する", "price-list-edit-prices-modal-error-loading": "フォームの準備中にエラーが発生しました。ページを再読み込みしてもう一度試してみてください。問題が解決しない場合は、後でもう一度試してみてください。", "price-list-prices-section-prompt-title": "よろしいですか？", "price-list-prices-section-prompt-description": "これにより製品価格がリストから永久に削除されます", "price-list-prices-secton-delete-success-title": "価格が削除されました", "price-list-prices-section-delete-success-description_one": "{{count}}商品の価格を正常に削除しました", "price-list-prices-section-delete-success-description_other": "{{count}}商品の価格を正常に削除しました", "price-list-prices-section-delete-error-title": "エラーが発生しました", "price-list-prices-section-heading": "価格", "price-list-prices-section-search-placeholder": "製品を検索する", "price-list-prices-section-prices-menu-edit": "価格を編集する", "price-list-prices-section-prices-menu-add": "商品を追加する", "price-list-prices-section-table-load-error": "商品の取得中にエラーが発生しました。ページをリロードしてみるか、問題が解消しない場合は後でもう一度お試しください。", "price-list-prices-section-bar-count_one": "選択された{{count}}個", "price-list-prices-section-bar-count_other": "選択された{{count}}個", "price-list-prices-section-edit-command": "編集", "price-list-prices-section-delete-command": "削除", "price-list-prices-section-select-all-checkbox-label": "現在のページのすべての商品を選択する", "price-list-prices-section-select-checkbox-label": "行を選択する", "price-list-prices-section-table-product": "商品", "price-list-prices-section-table-thumbnail-alt": "{{title}} サムネイル", "price-list-prices-section-table-collection": "コレクション", "price-list-prices-section-table-variants": "バリアント", "price-list-details-form-type-heading": "タイプ", "price-list-details-form-type-description": "作成する価格リストのタイプを選択してください。", "price-list-details-form-type-label-sale": "セール", "price-list-details-form-type-hint-sale": "セールを作成する場合に使用してください。", "price-list-details-form-type-label-override": "オーバーライド", "price-list-details-form-type-hint-override": "価格を上書きする場合に使用してください。", "price-list-details-form-general-heading": "一般的な", "price-list-details-form-general-description": "価格表のタイトルと説明を選んでください。", "price-list-details-form-general-name-label": "名前", "price-list-details-form-general-name-placeholder": "ブラックフライデーセール", "price-list-details-form-general-description-label": "説明", "price-list-details-form-general-description-placeholder": "ブラックフライデーセールの価格...", "price-list-details-form-tax-inclusive-label": "税込価格", "price-list-details-form-tax-inclusive-hint": "このリストのすべての価格を税込みにするか選択してください。", "price-list-details-form-dates-starts-at-heading": "価格リストには開始日がありますか？", "price-list-details-form-dates-starts-at-description": "価格オーバーライドを将来有効にするためにスケジュールします。", "price-list-details-form-dates-starts-at-label": "開始日", "price-list-details-form-ends-at-heading": "価格リストに有効期限がありますか？", "price-list-details-form-ends-at-description": "価格オーバーライドを将来無効にするためにスケジュールします。", "price-list-details-form-ends-at-label": "有効期限", "price-list-details-form-customer-groups-name": "名前", "price-list-details-form-customer-groups-members": "メンバー", "price-list-details-form-customer-groups-error": "顧客グループの読み込み中にエラーが発生しました。ページを再読み込みして、もう一度試してみてください。問題が解決しない場合は、後でもう一度試してください。", "price-list-details-form-customer-groups-no-groups": "顧客グループが見つかりませんでした。", "price-list-details-form-customer-groups-heading": "顧客の可用性", "price-list-details-form-customer-groups-description": "価格オーバーライドを適用する顧客グループを指定します。", "price-list-details-form-customer-groups-content-heading": "顧客グループ", "price-list-details-form-customer-groups-search-placeholder": "検索", "price-list-prices-form-products-error": "フォームの準備中にエラーが発生しました。ページをリロードして再試行してください。問題が解決しない場合は、後でもう一度試してください。", "price-list-prices-form-heading": "価格の編集", "price-list-prices-form-variant": "バリアント", "price-list-prices-form-sku": "SKU", "price-list-prices-form-prices": "価格", "price-list-prices-form-prices-variant-count_one": "{{count}}のバリアント", "price-list-prices-form-prices-variant-count_other": "{{count}}のバリアント", "price-list-prices-form-add-prices-button": "価格の追加", "price-list-prices-form-prices-count_one": "{{count}}の価格", "price-list-prices-form-prices-count_other": "{{count}}の価格", "price-list-product-prices-form-invalid-data-title": "無効なデータ", "price-list-product-prices-form-invalid-data-body": "貼り付けたデータには数値ではない値が含まれています。", "price-list-product-prices-form-column-visibility-button": "表示", "price-list-product-prices-form-column-visibility-currencies-label": "通貨", "price-list-product-prices-form-column-visibility-regions-label": "地域", "price-list-product-prices-form-column-product-label": "製品", "price-list-product-prices-form-column-currencies-price-label": "価格{{code}}", "price-list-product-prices-form-column-regions-price-label": "価格{{name}}({{code}})", "price-list-products-form-select-all": "現在のページ上のすべての製品を選択する", "price-list-products-form-select-row": "行を選択する", "price-list-products-form-product-label": "製品", "price-list-products-form-product-thumbnail": "{{title}}のサムネイル", "price-list-products-form-collection-label": "コレクション", "price-list-products-form-sales-channels-label": "在庫", "price-list-products-form-sales-channels-value": "{{first}} + {{remaining}} 件追加", "price-list-products-form-status-label": "ステータス", "price-list-products-form-inventory-label": "在庫", "price-list-products-form-inventory-value": "{{variants}}種類の商品が{{totalStock}}個在庫にあります", "price-list-products-form-loading": "商品の読み込み中", "price-list-products-form-error": "商品の読み込み中にエラーが発生しました。ページをリロードしてもう一度試してください。問題が解決しない場合は、後でもう一度試してください。", "price-list-products-form-no-products": "製品が見つかりませんでした。", "price-list-products-form-heading": "製品を選択する", "price-list-products-form-search-placeholder": "検索", "price-list-new-form-no-prices-error": "少なくとも1つの製品に価格を設定してください。", "price-list-new-form-missing-prices-title": "不完全な価格リスト", "price-list-new-products-modal-missing-prices-description": "選択した製品すべてに価格が割り当てられていません。続行しますか？", "price-list-new-form-notification-success-title": "価格リストが作成されました", "price-list-new-form-notification-success-message": "価格リストの作成に成功しました", "price-list-new-form-notification-error-title": "エラーが発生しました", "price-list-new-form-next-button-save-and-publish": "保存して発表", "price-list-new-form-next-button-save": "価格を保存する", "price-list-new-form-next-button-continue": "続行する", "price-list-new-form-back-button-cancel": "キャンセル", "price-list-new-form-back-button-back": "戻る", "price-list-new-form-details-tab": "価格リストを作成する", "price-list-new-form-products-tab": "商品を選択する", "price-list-new-form-prices-tab": "価格を編集する", "price-list-new-form-save-as-draft": "下書きとして保存する", "price-list-new-form-error-loading-products": "フォームの準備中にエラーが発生しました。ページを再読み込みしてもう一度お試しください。問題が解決しない場合は、後で再試行してください。", "components-success": "成功", "components-successfully-updated-category-tree": "カテゴリツリーを正常に更新しました", "components-error": "エラー", "components-failed-to-update-category-tree": "カテゴリツリーの更新に失敗しました", "components-delete": "削除", "components-category-deleted": "カテゴリが削除されました", "components-category-deletion-failed": "カテゴリの削除に失敗しました", "components-category-status-is-inactive": "カテゴリのステータスは非アクティブです", "components-category-visibility-is-private": "カテゴリの表示は非公開です", "components-add-category-item-to": "カテゴリアイテムを追加する", "modals-public": "公開", "modals-private": "非公開", "modals-active": "有効", "modals-inactive": "無効", "modals-success": "成功", "modals-successfully-created-a-category": "カテゴリーが正常に作成されました", "modals-failed-to-create-a-new-category": "新しいカテゴリーの作成に失敗しました", "modals-error": "エラー", "modals-save-category": "カテゴリーを保存する", "modals-add-category-to": "{{name}}にカテゴリーを追加する", "modals-add-category": "カテゴリーを追加する", "modals-details": "詳細", "modals-name": "名前", "modals-give-this-category-a-name": "このカテゴリーに名前を付ける", "modals-handle": "ハンドル", "modals-custom-handle": "カスタムハンドル", "modals-description": "説明", "modals-give-this-category-a-description": "このカテゴリーに説明を付ける", "modals-status": "ステータス", "modals-visibility": "可視性", "modals-successfully-updated-the-category": "カテゴリーの更新に成功しました", "modals-failed-to-update-the-category": "カテゴリーの更新に失敗しました", "modals-edit-product-category": "商品カテゴリを編集する", "modals-cancel": "キャンセル", "modals-save-and-close": "保存して閉じる", "pages-no-product-categories-yet": "まだ商品カテゴリはありません、上のボタンを使用して最初のカテゴリを作成してください。", "pages-add-category": "カテゴリ追加", "pages-product-categories": "商品カテゴリ", "pages-helps-you-to-keep-your-products-organized": "商品を整理するのに役立ちます。", "batch-job-success": "成功", "batch-job-import-confirmed-for-processing-progress-info-is-available-in-the-activity-drawer": "インポートが処理確認されました。進行状況はアクティビティドロワーに表示されます。", "batch-job-error": "エラー", "batch-job-import-failed": "インポートに失敗しました。", "batch-job-failed-to-delete-the-csv-file": "CSVファイルの削除に失敗しました", "batch-job-failed-to-cancel-the-batch-job": "バッチジョブのキャンセルに失敗しました", "batch-job-products-list": "製品リスト", "batch-job-unsure-about-how-to-arrange-your-list": "リストをどのように配置するかわからない場合は、以下のテンプレートをダウンロードして正しい形式に従っているか確認してください。", "batch-job-download-template": "インポートを使用することで、商品を追加または更新することができます。既存の商品/バリアントを更新する場合は、商品/バリアントIDの列に既存のIDを設定する必要があります。値が設定されていない場合、新しいレコードが作成されます。商品のインポート前に確認が求められます。", "batch-job-imports-description": "フィルター", "products-status": "ステータス", "products-tags": "タグ", "products-spring-summer": "春、夏...", "new-sales-channels": "販売チャネル", "new-this-product-will-only-be-available-in-the-default-sales-channel-if-left-untouched": "この商品はデフォルトの販売チャネルでのみ利用可能です。", "new-change-availablity": "利用可能の変更", "add-variants-a-variant-with-these-options-already-exists": "このオプションと一致するバリアントがすでに存在しています。", "add-variants-product-options": "商品オプション", "add-variants-options-are-used-to-define-the-color-size-etc-of-the-product": "オプションは商品の色、サイズなどを定義するために使用されます。", "add-variants-option-title": "オプションタイトル", "add-variants-variations-comma-separated": "バリエーション（カンマ区切り）", "add-variants-color": "色...", "add-variants-already-exists": "すでに存在しています", "add-variants-blue-red-black": "青、赤、黒...", "add-variants-add-an-option": "オプションを追加", "add-variants-product-variants": "商品バリアント", "add-variants-you-must-add-at-least-one-product-option-before-you-can-begin-adding-product-variants": "商品バリアントを追加する前に、少なくとも1つの商品オプションを追加する必要があります。", "add-variants-variant": "バリアント", "add-variants-inventory": "在庫", "add-variants-add-a-variant": "バリアントを追加", "add-variants-create-variant": "バリアントを作成する", "add-variants-cancel": "キャンセル", "add-variants-save-and-close": "保存して閉じる", "new-variant-a-variant-with-these-options-already-exists": "これらのオプションを持つバリアントはすでに存在します。", "new-variant-are-you-sure-you-want-to-delete-this-variant": "このバリアントを削除してもよろしいですか？", "new-variant-delete-variant": "バリアントを削除する", "new-variant-edit": "編集", "new-variant-delete": "削除", "new-variant-edit-variant": "バリアントを編集する", "new-variant-cancel": "キャンセル", "new-variant-save-and-close": "保存して閉じる", "new-something-went-wrong-while-trying-to-upload-images": "画像のアップロード中に問題が発生しました。", "new-no-file-service-configured": "ファイルサービスが設定されていない可能性があります。管理者にお問い合わせください", "new-upload-thumbnail-error": "サムネイルのアップロード中に問題が発生しました。", "new-save-as-draft": "下書きとして保存", "new-publish-product": "商品を公開する", "new-general-information-title": "一般情報", "new-to-start-selling-all-you-need-is-a-name-and-a-price": "販売を開始するには、名前と価格が必要です。", "new-organize-product": "商品の整理", "new-add-variations-of-this-product": "この商品のバリエーションを追加する。", "new-offer-your-customers-different-options-for-color-format-size-shape-etc": "お客様に色、形式、サイズ、形状などさまざまなオプションを提供します。", "new-used-for-shipping-and-customs-purposes": "配送や税関の目的で使用されます。", "new-dimensions": "寸法", "new-customs": "税関", "new-used-to-represent-your-product-during-checkout-social-sharing-and-more": "チェックアウト、ソーシャル共有などで製品を表します。", "new-media": "メディア", "new-add-images-to-your-product": "製品に画像を追加します。", "overview-import-products": "輸入製品", "overview-export-products": "輸出製品", "overview-new-product": "新製品", "overview-new-collection": "新コレクション", "overview-success": "成功", "overview-successfully-created-collection": "コレクションの作成に成功しました", "overview-error": "エラー", "overview-successfully-initiated-export": "輸出を正常に開始しました", "modals-add-sales-channels": "販売チャネルを追加します", "modals-find-channels": "チャネルを探す", "modals-updated-the-api-key": "APIキーが更新されました", "modals-failed-to-update-the-api-key": "APIキーの更新に失敗しました", "modals-edit-api-key-details": "APIキーの詳細を編集します", "modals-title": "タイトル", "modals-name-your-key": "キーの名前を付ける", "modals-sales-channels-added-to-the-scope": "スコープに追加された販売チャネル", "modals-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "販売チャネルをキーのスコープに追加する際にエラーが発生しました", "modals-add-and-go-back": "追加して戻る", "modals-add-and-close": "追加して閉じる", "modals-sales-channels-removed-from-the-scope": "スコープから削除された販売チャネル", "modals-error-occurred-while-removing-sales-channels-from-the-scope-of-the-key": "販売チャネルをキーのスコープから削除する際にエラーが発生しました", "modals-edit-sales-channels": "販売チャネルの編集", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_one": "{{count}}", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_other": "{{count}}", "modals-deselect": "選択解除", "modals-remove": "削除", "modals-add-channels": "チャンネルを追加", "modals-close": "閉じる", "pages-sales-channels": "販売チャネル", "pages-connect-as-many-sales-channels-to-your-api-key-as-you-need": "必要なだけAPIキーに販売チャネルを接続してください。", "pages-add-sales-channels": "販売チャネルを追加する", "pages-edit-sales-channels": "販売チャネルの編集", "pages-success": "成功", "pages-created-a-new-api-key": "新しいAPIキーを作成しました", "pages-error": "エラー", "pages-failed-to-create-a-new-api-key": "新しいAPIキーの作成に失敗しました", "pages-sales-channels-added-to-the-scope": "販売チャネルがスコープに追加されました", "pages-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "販売チャネルのスコープに追加する際にエラーが発生しました", "pages-publish-api-key": "APIキーを公開する", "pages-create-api-key": "APIキーを作成する", "pages-create-and-manage-api-keys-right-now-this-is-only-related-to-sales-channels": "APIキーを作成および管理します。現時点では、これは販売チャネルに関係しています。", "pages-create-api-key-label": "APIキーを作成する", "pages-back-to-settings": "設定に戻る", "pages-publishable-api-keys": "公開可能なAPIキー", "pages-these-publishable-keys-will-allow-you-to-authenticate-api-requests": "これらの公開可能なキーを使用してAPIリクエストを認証できます。", "tables-name": "名前", "tables-token": "トークン", "tables-done": "完了", "tables-copy-to-clipboard": "クリップボードにコピー", "tables-created": "作成済み", "tables-status": "ステータス", "tables-revoked": "取り消された", "tables-live": "ライブ", "tables-edit-api-key-details": "API 키 세부정보 편집", "tables-edit-sales-channels": "판매 채널 편집", "tables-copy-token": "토큰 복사", "tables-revoke-token": "토큰 취소", "tables-delete-api-key": "API 키 삭제", "tables-yes-delete": "예, 삭제", "tables-api-key-deleted": "API 키가 삭제되었습니다", "tables-are-you-sure-you-want-to-delete-this-public-key": "이 공개 키를 삭제하시겠습니까?", "tables-delete-key": "키 삭제", "tables-yes-revoke": "예, 취소", "tables-api-key-revoked": "API 키가 취소되었습니다", "tables-are-you-sure-you-want-to-revoke-this-public-key": "이 공개 키를 취소하시겠습니까?", "tables-revoke-key": "키 취소", "tables-api-keys": "API 키", "tables-no-keys-yet-use-the-above-button-to-create-your-first-publishable-key": "아직 키가 없습니다. 위의 버튼을 사용하여 첫 번째 게시 가능한 키를 만드세요", "tables-title": "제목", "tables-description": "설명", "tables-no-added-sales-channels": "추가된 판매 채널 없음", "tables-sales-channels": "판매 채널", "form-title": "제목", "form-website-app-amazon-physical-store-pos-facebook-product-feed": "ウェブサイト、アプリ、Amazon、物理店舗のPOS、Facebook製品フィード...", "form-description": "説明", "form-available-products-at-our-website-app": "ウェブサイト、アプリで利用可能な商品...", "form-success": "成功", "form-the-sales-channel-is-successfully-updated": "販売チャネルが正常に更新されました", "form-error": "エラー", "form-failed-to-update-the-sales-channel": "販売チャネルの更新に失敗しました", "form-sales-channel-details": "販売チャネルの詳細", "form-general-info": "一般情報", "form-name": "名前", "form-close": "閉じる", "form-save": "保存", "pages-draft": "下書き", "pages-control-which-products-are-available-in-which-channels": "製品をどのチャネルで利用可能にするかを制御します", "pages-search-by-title-or-description": "タイトルまたは説明で検索", "pages-confirm-delete-sales-channel": "この販売チャネルを削除してもよろしいですか? あなたが行った設定は永久に失われます。", "pages-delete-channel-heading": "チャネルを削除する", "pages-edit-general-info": "一般情報の編集", "pages-add-products": "製品の追加", "pages-delete-channel": "チャネルの削除", "pages-disabled": "無効", "pages-enabled": "有効", "tables-collection": "コレクション", "tables-start-building-your-channels-setup": "チャンネルのセットアップを開始しましょう...", "tables-no-products-in-channels": "まだこのチャンネルに製品を追加していませんが、追加すればここに表示されます。", "tables-add-products": "製品を追加する", "tables-details": "詳細", "tables-remove-from-the-channel": "チャンネルから削除する", "tables-products": "製品", "sales-channels-table-placeholder-selected-with-counts_one": "{{count}}", "sales-channels-table-placeholder-selected-with-counts_other": "{{count}}", "tables-remove": "削除", "components-successfully-updated-currency": "通貨が正常に更新されました", "components-default": "デフォルト", "default-store-currency-success": "成功", "default-store-currency-successfully-updated-default-currency": "デフォルト通貨が正常に更新されました", "default-store-currency-error": "エラー", "default-store-currency-default-store-currency": "デフォルトの店舗通貨", "default-store-currency-this-is-the-currency-your-prices-are-shown-in": "価格が表示される通貨です。", "store-currencies-success": "成功", "store-currencies-successfully-updated-currencies": "通貨が正常に更新されました", "store-currencies-error": "エラー", "store-currencies-cancel": "キャンセル", "store-currencies-save-and-go-back": "保存して戻る", "store-currencies-save-and-close": "保存して閉じる", "store-currencies-add-store-currencies": "店舗通貨の追加", "store-currencies-current-store-currencies": "現在の店舗通貨", "store-currencies-close": "閉じる", "current-currencies-screen-selected-with-count_one": "{{count}}", "current-currencies-screen-selected-with-count_other": "{{count}}", "store-currencies-deselect": "選択解除", "store-currencies-remove": "削除", "store-currencies-add-currencies": "通貨の追加", "store-currencies-store-currencies": "店舗通貨", "store-currencies-all-the-currencies-available-in-your-store": "店舗で利用可能なすべての通貨", "store-currencies-edit-currencies": "通貨の編集", "currencies-an-unknown-error-occurred": "不明なエラーが発生しました", "currencies-error": "エラー", "currencies-back-to-settings": "設定に戻る", "currencies-manage-the-markets-that-you-will-operate-within": "運営するマーケットを管理する", "currencies-include-or-exclude-taxes": "この通貨で価格を定義する際に、税金を含めるか除外するかを決定してください", "currencies-tax-incl-prices": "税込価格", "settings-error": "エラー", "settings-malformed-swap-url": "不正なスワップURL", "settings-malformed-payment-url": "不正な支払いURL", "settings-malformed-invite-url": "不正な招待URL", "settings-success": "成功", "settings-successfully-updated-store": "ストアが正常に更新されました", "settings-back-to-settings": "設定に戻る", "settings-save": "保存", "settings-cancel": "キャンセル", "settings-store-details": "ストアの詳細", "settings-manage-your-business-details": "ビジネスの詳細を管理する", "settings-general": "一般", "settings-store-name": "ストア名", "settings-medusa-store": "メデューサストア", "settings-advanced-settings": "高度な設定", "settings-swap-link-template": "スワップリンクテンプレート", "settings-draft-order-link-template": "ドラフト注文リンクテンプレート", "settings-invite-link-template": "招待リンクテンプレート", "settings-manage-the-general-settings-for-your-store": "ストアの全般設定を管理する", "settings-manage-the-settings-for-your-store-apos-s-extensions": "ストアの拡張機能の設定を管理する", "edit-user-information-success": "成功", "edit-user-information-your-information-was-successfully-updated": "情報が正常に更新されました", "edit-user-information-edit-information": "情報を編集する", "edit-user-information-cancel": "キャンセル", "edit-user-information-submit-and-close": "提出して閉じる", "personal-information-back-to-settings": "設定に戻る", "personal-information-personal-information": "個人情報", "personal-information-manage-your-medusa-profile": "Medusaのプロフィールを管理する", "personal-information-language-settings-title": "言語", "personal-information-language-settings-description": "Medusa Adminの言語を調整する", "personal-information-language-settings-help-us-translate": "翻訳を手伝ってください", "personal-information-usage-insights-title": "使用状況の洞察", "usage-insights-disabled": "無効", "usage-insights-active": "有効", "usage-insights-share-usage-insights-and-help-us-improve-medusa": "使用状況の洞察を共有し、Medusaを改善するのに役立てる", "usage-insights-edit-preferences": "環境設定を編集する", "usage-insights-success": "成功", "usage-insights-your-information-was-successfully-updated": "情報が正常に更新されました", "usage-insights-error": "エラー", "usage-insights-cancel": "キャンセル", "usage-insights-submit-and-close": "送信して閉じる", "region-form-title": "タイトル", "region-form-europe": "ヨーロッパ", "region-form-currency-code-is-required": "通貨コードが必要です", "region-form-currency": "通貨", "region-form-choose-currency": "通貨を選択してください", "region-form-default-tax-rate": "デフォルトの税率", "region-form-tax-rate-is-required": "税率が必要です", "region-form-tax-rate-must-be-equal-to-or-less-than-100": "税率は100以下でなければなりません", "region-form-default-tax-code": "デフォルトの税コード", "region-form-countries": "国", "region-form-choose-countries": "国を選択してください", "region-form-tax-inclusive-prices": "税込み価格", "region-form-when-enabled-region-prices-will-be-tax-inclusive": "有効にすると、地域価格は税込みになります。", "region-form-payment-providers-are-required": "支払いプロバイダが必要です", "region-form-payment-providers": "支払いプロバイダ", "region-form-choose-payment-providers": "支払いプロバイダを選択してください...", "region-form-fulfillment-providers-are-required": "フルフィルメントプロバイダが必要です", "region-form-fulfillment-providers": "履行プロバイダ", "region-form-choose-fulfillment-providers": "履行プロバイダを選択してください...", "shipping-option-card-success": "成功", "shipping-option-card-shipping-option-updated": "配送オプションが更新されました", "shipping-option-card-error": "エラー", "shipping-option-card-edit-shipping-option": "送料オプションを編集する", "shipping-option-card-fulfillment-method": "履行方法", "shipping-option-card-cancel": "キャンセル", "shipping-option-card-save-and-close": "保存して閉じる", "shipping-option-card-shipping-option-has-been-deleted": "送料オプションが削除されました", "shipping-option-card-flat-rate": "固定料金", "shipping-option-card-calcualted": "計算された", "shipping-option-card-min-subtotal": "最小注文金額:", "shipping-option-card-max-subtotal": "最大注文金額:", "shipping-option-card-admin": "管理者", "shipping-option-card-store": "ストア", "shipping-option-card-edit": "編集する", "shipping-option-card-delete": "削除する", "shipping-option-form-visible-in-store": "ストアで表示", "shipping-option-form-enable-or-disable-the-shipping-option-visiblity-in-store": "ストアでの送料オプションの表示を有効または無効にします。", "shipping-option-form-details": "詳細", "shipping-option-form-title": "タイトル", "shipping-option-form-title-is-required": "タイトルは必須です", "shipping-option-form-price-type": "価格タイプ", "shipping-option-form-flat-rate": "定額", "shipping-option-form-calculated": "計算された", "shipping-option-form-choose-a-price-type": "価格タイプを選択してください", "shipping-option-form-price": "価格", "shipping-option-form-shipping-profile": "配送プロファイル", "shipping-option-form-choose-a-shipping-profile": "配送プロファイルを選択してください", "shipping-option-form-fulfillment-method": "フルフィルメント方法", "shipping-option-form-choose-a-fulfillment-method": "フルフィルメント方法を選択してください", "shipping-option-form-requirements": "要件", "shipping-option-form-min-subtotal-must-be-less-than-max-subtotal": "最小サブトータルは最大サブトータルよりも低くなければなりません", "shipping-option-form-min-subtotal": "最小サブトータル", "shipping-option-form-max-subtotal": "最大サブトータル", "shipping-option-form-metadata": "メタデータ", "general-section-success": "成功", "general-section-region-was-successfully-updated": "地域は正常に更新されました", "general-section-error": "エラー", "general-section-edit-region-details": "地域の詳細を編集", "general-section-details": "詳細", "general-section-providers": "プロバイダー", "general-section-metadata": "メタデータ", "general-section-cancel": "キャンセル", "general-section-save-and-close": "保存して閉じる", "edit-something-went-wrong": "何か問題が発生しました...", "edit-no-region-found": "そのIDの地域が見つかりません。メニューを使用して左側の地域を選択してください。", "return-shipping-options-success": "成功", "return-shipping-options-shipping-option-created": "出荷オプションが作成されました", "return-shipping-options-error": "エラー", "return-shipping-options-add-return-shipping-option": "返品送料オプションを追加", "return-shipping-options-cancel": "キャンセル", "return-shipping-options-save-and-close": "保存して閉じる", "return-shipping-options-return-shipping-options": "返品送料オプション", "return-shipping-options-add-option": "オプションを追加する", "return-shipping-options-enter-specifics-about-available-regional-return-shipment-methods": "利用可能な地域の返品出荷方法についての詳細を入力してください。", "shipping-options-success": "成功", "shipping-options-shipping-option-created": "出荷オプションが作成されました", "shipping-options-error": "エラー", "shipping-options-add-shipping-option": "配送オプションを追加する", "shipping-options-cancel": "キャンセル", "shipping-options-save-and-close": "保存して閉じる", "shipping-options-shipping-options": "配送オプション", "shipping-options-add-option": "オプションを追加する", "shipping-options-enter-specifics-about-available-regional-shipment-methods": "利用可能な地域の配送方法についての詳細を入力してください。", "new-region-created": "地域が作成されました", "new-create-region": "地域を作成する", "new-details": "詳細", "new-add-the-region-details": "地域の詳細を追加する", "new-providers": "プロバイダーズ", "new-add-which-fulfillment-and-payment-providers-should-be-available-in-this-region": "この地域で利用可能なフルフィルメントおよび支払いプロバイダーを追加します。", "region-overview-regions": "地域", "region-overview-manage-the-markets-that-you-will-operate-within": "運営する市場を管理します。", "region-overview-not-configured": "設定されていません", "region-overview-fulfillment-providers": "フルフィルメントプロバイダー：", "return-reasons-notification-success": "成功", "return-reasons-created-a-new-return-reason": "新しい返品理由を作成しました", "return-reasons-success": "成功", "return-reasons-error": "エラー", "return-reasons-cannot-create-a-return-reason-with-an-existing-value": "既存の値で返品理由を作成することはできません", "return-reasons-add-reason": "理由を追加する", "return-reasons-value-is-required": "値が必要です", "return-reasons-value": "値", "return-reasons-label-is-required": "ラベルが必要です", "return-reasons-label": "ラベル", "return-reasons-description": "説明", "return-reasons-customer-received-the-wrong-size": "お客様が間違ったサイズを受け取った", "return-reasons-cancel": "キャンセル", "return-reasons-create": "作成する", "return-reasons-success-title": "成功", "return-reasons-successfully-updated-return-reason": "返品理由が正常に更新されました", "return-reasons-duplicate-reason": "重複した理由", "return-reasons-delete-reason": "理由を削除する", "return-reasons-save": "保存する", "return-reasons-details": "詳細", "return-reasons-delete-return-reason": "返品理由を削除しますか？", "return-reasons-are-you-sure-you-want-to-delete-this-return-reason": "設定に戻る", "return-reasons-back-to-settings": "返品理由", "return-reasons-add-reason-label": "理由を追加する", "return-reasons-manage-reasons-for-returned-items": "返品アイテムの理由を管理する", "taxes-details": "詳細", "taxes-new-tax-rate": "新しい税率", "taxes-tax-calculation-settings": "税の計算設定", "taxes-success": "成功", "taxes-successfully-updated-tax-rate": "税率が正常に更新されました。", "taxes-error": "エラー", "taxes-overrides": "オーバーライド", "taxes-product-rules": "製品のルール", "taxes-product-rules-description_one": "{{count}}個の商品に適用", "taxes-product-rules-description_other": "{{count}}個の商品に適用", "taxes-product-type-rules": "製品のタイプのルール", "taxes-product-type-rules-description_one": "{{count}}個の製品タイプに適用", "taxes-product-type-rules-description_other": "{{count}}個の製品タイプに適用", "taxes-shipping-option-rules": "配送オプションのルール", "taxes-applies-to-shipping-option-with-count_one": "{{count}}個の配送オプションに適用", "taxes-applies-to-shipping-option-with-count_other": "{{count}}個の配送オプションに適用", "taxes-add-overrides": "オーバーライドを追加", "taxes-cancel": "キャンセル", "taxes-save": "保存", "taxes-name": "名前", "taxes-default": "デフォルト", "taxes-rate-name": "レート名", "taxes-tax-rate": "税率", "taxes-tax-code": "税区分", "taxes-edit-tax-rate": "税率の編集", "taxes-back-to-settings": "設定に戻る", "taxes-regions": "地域", "taxes-select-the-region-you-wish-to-manage-taxes-for": "税金を管理する地域を選択してください", "taxes-go-to-region-settings": "地域設定に移動する", "taxes-successfully-created-tax-rate": "税率を作成しました", "taxes-add-tax-rate": "税率を追加する", "taxes-applies-to-product-type-with-count_one": "{{count}}個の商品タイプに適用", "taxes-applies-to-product-type-with-count_other": "{{count}}個の商品タイプに適用", "taxes-create": "作成", "taxes-select-products": "商品を選択する", "taxes-select-product-types-label": "商品タイプを選択する", "taxes-product-types": "商品タイプ", "taxes-system-tax-provider": "システム税金プロバイダー", "taxes-region-tax-settings-were-successfully-updated": "地域税の設定が正常に更新されました。", "taxes-tax-provider": "税プロバイダ", "taxes-calculate-taxes-automatically": "税を自動的に計算しますか？", "taxes-automatically-apply-tax-calculations-to-carts": "Medusaがこの地域のカートに税計算を自動的に適用します。チェックを外すと、レジで手動で税を計算する必要があります。3rdパーティの税プロバイダを使用している場合は、多くのリクエストを行わないために手動税計算が推奨されます。", "taxes-apply-tax-to-gift-cards": "ギフトカードに税を適用しますか？", "taxes-apply-taxes-to-gift-cards": "チェックを入れると、購入時にギフトカードに税が適用されます。一部の国では、税金規則により、ギフトカードに税金が購入時に適用される必要があります。", "taxes-search-products": "商品を検索する..", "taxes-select-shipping-option": "配送オプションを選択", "taxes-shipping-options": "配送オプション", "taxes-delete-tax-rate-heading": "税率を削除する", "taxes-confirm-delete": "この税率を削除してもよろしいですか？", "taxes-tax-rate-was-deleted": "税率が削除されました。", "taxes-edit": "編集", "taxes-delete-tax-rate": "税率を削除する", "taxes-delete-rule": "ルールを削除する", "taxes-type": "タイプ", "taxes-products": "商品", "taxes-select-individual-products": "個々の商品を選択", "taxes-select-product-types": "商品の種類を選択", "taxes-select-shipping-options": "配送オプションを選択", "taxes-back": "戻る", "taxes-add": "追加する", "taxes-code": "コード", "users-invite-users": "ユーザーを招待する", "users-back-to-settings": "設定に戻る", "users-the-team": "チーム", "users-manage-users-of-your-medusa-store": "Medusaストアのユーザーを管理する", "users-count_one": "{{count}}", "users-count_other": "{{count}}"}