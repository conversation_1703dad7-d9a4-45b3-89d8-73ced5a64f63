{"back-button-go-back": "返回", "filter-menu-trigger": "查看", "filter-menu-clear-button": "清除", "filter-menu-select-item-default-placeholder": "选择过滤器", "filter-menu-select-item-clear-button": "清除所选选项", "filter-menu-select-item-selected": "已选择", "filter-menu-date-item-before": "之前", "filter-menu-date-item-after": "之后", "filter-menu-date-item-between": "之间", "sales-channels-display-available-count": "在销售渠道中，有<2>{{availableChannelsCount}}</2>个可用，共<6>{{totalChannelsCount}}</6>个销售渠道", "activity-drawer-activity": "活动", "activity-drawer-no-notifications-title": "这里很安静...", "activity-drawer-no-notifications-description": "您目前没有任何通知，但一旦有通知，它们将显示在这里。", "activity-drawer-error-title": "哦，不好...", "activity-drawer-error-description": "在尝试获取您的通知时出了一些问题 - 我们会继续尝试！", "activity-drawer-processing": "处理中...", "analytics-config-form-title": "匿名化我的使用数据", "analytics-config-form-description": "您可以选择匿名化您的使用数据。如果选择此选项，我们将不会收集您的个人信息，如姓名和电子邮件地址。", "analytics-config-form-opt-out": "选择不分享我的使用数据", "analytics-config-form-opt-out-later": "您随时可以选择不分享您的使用数据。", "analytics-preferences-success": "成功", "analytics-preferences-your-preferences-were-successfully-updated": "您的偏好已成功更新", "analytics-preferences-error": "错误", "analytics-preferences-help-us-get-better": "帮助我们变得更好", "analytics-preferences-disclaimer": "为了创建最引人入胜的电子商务体验，我们希望了解您如何使用 Medusa 的见解。用户见解让我们能够构建更好、更引人入胜、更易用的产品。我们仅收集用于产品改进的数据。阅读我们在", "analytics-preferences-documentation": "文档", "analytics-preferences-please-enter-a-valid-email": "请输入有效的电子邮件", "analytics-preferences-continue": "继续", "currency-input-currency": "货币", "currency-input-amount-is-not-valid": "金额无效", "organisms-success": "成功", "organisms-delete-successful": "删除成功", "organisms-are-you-sure-you-want-to-delete": "您确定要删除吗？", "organisms-no-cancel": "不，取消", "organisms-yes-remove": "是的，删除", "details-collapsible-hide-additional-details": "隐藏附加详情", "details-collapsible-show-additional-details": "显示附加详情", "edit-user-modal-success": "成功", "edit-user-modal-user-was-updated": "用户已更新", "edit-user-modal-error": "错误", "edit-user-modal-edit-user": "编辑用户", "edit-user-modal-first-name-label": "名字", "edit-user-modal-first-name-placeholder": "名字...", "edit-user-modal-last-name-label": "姓氏", "edit-user-modal-last-name-placeholder": "姓氏...", "edit-user-modal-email": "电子邮件", "edit-user-modal-cancel": "取消", "edit-user-modal-save": "保存", "error-boundary-back-to-dashboard": "返回仪表板", "error-boundary-an-unknown-error-occured": "发生了未知错误", "error-boundary-bad-request": "错误的请求", "error-boundary-you-are-not-logged-in": "您未登录", "error-boundary-you-do-not-have-permission-perform-this-action": "您没有执行此操作的权限", "error-boundary-page-was-not-found": "未找到页面", "error-boundary-an-unknown-server-error-occured": "发生未知服务器错误", "error-boundary-503": "服务器当前不可用", "error-boundary-500": "发生错误，原因未指定，这很可能是由于我们端的技术问题。请尝试刷新页面。如果问题仍然存在，请联系您的管理员。", "error-boundary-400": "请求格式不正确，请修复您的请求并重试。", "error-boundary-401": "您未登录，请登录以继续。", "error-boundary-403": "您没有执行此操作的权限，如果您认为这是一个错误，请联系您的管理员。", "error-boundary-404": "您请求的页面未找到，请检查URL并重试。", "error-boundary-500-2": "服务器无法处理您的请求，这很可能是由于我们端的技术问题。请重试。如果问题仍然存在，请联系您的管理员。", "error-boundary-503-2": "服务器暂时不可用，无法处理您的请求。请稍后重试。如果问题仍然存在，请联系您的管理员。", "export-modal-title": "初始化数据导出", "export-modal-cancel": "取消", "export-modal-export": "导出", "file-upload-modal-upload-a-new-photo": "上传新照片", "gift-card-banner-edit": "编辑", "gift-card-banner-unpublish": "取消发布", "gift-card-banner-publish": "发布", "gift-card-banner-delete": "删除", "gift-card-banner-published": "已发布", "gift-card-banner-unpublished": "未发布", "gift-card-denominations-section-denomination-added": "添加面额", "gift-card-denominations-section-a-new-denomination-was-successfully-added": "新面额已成功添加", "gift-card-denominations-section-a-denomination-with-that-default-value-already-exists": "具有该默认值的面额已存在", "gift-card-denominations-section-error": "错误", "gift-card-denominations-section-add-denomination": "添加面额", "gift-card-denominations-section-cancel": "取消", "gift-card-denominations-section-save-and-close": "保存并关闭", "gift-card-denominations-section-denomination-updated": "更新面额", "gift-card-denominations-section-a-new-denomination-was-successfully-updated": "新面额已成功更新", "gift-card-denominations-section-edit-denomination": "编辑面额", "gift-card-denominations-section-denominations": "面额", "gift-card-denominations-section-denomination": "面额", "gift-card-denominations-section-in-other-currencies": "在其他货币中", "gift-card-denominations-section-and-more_one": "，还有{{count}}个", "gift-card-denominations-section-and-more_other": "，还有{{count}}个", "gift-card-denominations-section-delete-denomination": "删除面额", "gift-card-denominations-section-confirm-delete": "您确定要删除此面额吗？", "gift-card-denominations-section-denomination-deleted": "已删除面额", "gift-card-denominations-section-denomination-was-successfully-deleted": "已成功删除面额", "gift-card-denominations-section-edit": "编辑", "gift-card-denominations-section-delete": "删除", "help-dialog-how-can-we-help": "我们可以如何帮助您？", "help-dialog-we-usually-respond-in-a-few-hours": "我们通常在几小时内回复", "help-dialog-subject": "主题", "help-dialog-what-is-it-about": "关于什么？...", "help-dialog-write-a-message": "撰写消息...", "help-dialog-feel-free-to-join-our-community-of": "欢迎加入我们的社区，其中包括", "help-dialog-merchants-and-e-commerce-developers": "商家和电子商务开发者", "help-dialog-send-a-message": "发送消息", "invite-modal-success": "成功", "invite-modal-invitation-sent-to": "已发送邀请至 {{user}}", "invite-modal-error": "错误", "invite-modal-member": "成员", "invite-modal-admin": "管理员", "invite-modal-developer": "开发者", "invite-modal-invite-users": "邀请用户", "invite-modal-email": "电子邮件", "invite-modal-role": "角色", "invite-modal-select-role": "选择角色", "invite-modal-cancel": "取消", "invite-modal-invite": "邀请", "login-card-no-match": "这些凭据与我们的记录不匹配。", "login-card-log-in-to": "登录 Uponco", "login-card-email": "电子邮件", "login-card-password": "密码", "login-card-forgot-your-password": "忘记密码？", "metadata-add-metadata": "添加元数据", "product-attributes-section-edit-attributes": "编辑属性", "product-attributes-section-dimensions": "尺寸", "product-attributes-section-configure-to-calculate-the-most-accurate-shipping-rates": "配置以计算最准确的运费", "product-attributes-section-customs": "海关", "product-attributes-section-cancel": "取消", "product-attributes-section-save": "保存", "product-attributes-section-title": "属性", "product-attributes-section-height": "高度", "product-attributes-section-width": "宽度", "product-attributes-section-length": "长度", "product-attributes-section-weight": "重量", "product-attributes-section-mid-code": "MID 码", "product-attributes-section-hs-code": "HS 码", "product-attributes-section-country-of-origin": "原产国", "product-general-section-success": "成功", "product-general-section-successfully-updated-sales-channels": "成功更新销售渠道", "product-general-section-error": "错误", "product-general-section-failed-to-update-sales-channels": "无法更新销售渠道", "product-general-section-edit-general-information": "编辑一般信息", "product-general-section-gift-card": "礼品卡", "product-general-section-product": "产品", "product-general-section-metadata": "元数据", "product-general-section-cancel": "取消", "product-general-section-save": "保存", "product-general-section-delete": "删除", "product-general-section-edit-sales-channels": "编辑销售渠道", "product-general-section-published": "已发布", "product-general-section-draft": "草稿", "product-general-section-details": "详情", "product-general-section-subtitle": "副标题", "product-general-section-handle": "标识", "product-general-section-type": "类型", "product-general-section-collection": "收藏", "product-general-section-category": "类别", "product-general-section-discountable": "可打折", "product-general-section-true": "是", "product-general-section-false": "否", "product-general-section-count_one": "{{count}}", "product-general-section-count_other": "{{count}}", "product-general-section-sales-channels": "销售渠道", "product-media-section-edit-media": "编辑媒体", "product-media-section-upload-images-error": "尝试上传图像时出了问题。", "product-media-section-file-service-not-configured": "您可能没有配置文件服务。请联系您的管理员", "product-media-section-error": "错误", "product-media-section-media": "媒体", "product-media-section-add-images-to-your-product": "为您的产品添加图像。", "product-media-section-cancel": "取消", "product-media-section-save-and-close": "保存并关闭", "product-raw-section-raw-gift-card": "原始礼品卡", "product-raw-section-raw-product": "原始产品", "product-thumbnail-section-success": "成功", "product-thumbnail-section-successfully-deleted-thumbnail": "成功删除缩略图", "product-thumbnail-section-error": "错误", "product-thumbnail-section-edit": "编辑", "product-thumbnail-section-upload": "上传", "product-thumbnail-section-upload-thumbnail-error": "尝试上传缩略图时出了问题。", "product-thumbnail-section-you-might-not-have-a-file-service-configured-please-contact-your-administrator": "您可能没有配置文件服务。请联系您的管理员", "product-thumbnail-section-upload-thumbnail": "上传缩略图", "product-thumbnail-section-thumbnail": "缩略图", "product-thumbnail-section-used-to-represent-your-product-during-checkout-social-sharing-and-more": "用于在结账、社交分享等过程中代表您的产品。", "product-thumbnail-section-cancel": "取消", "product-thumbnail-section-save-and-close": "保存并关闭", "product-variant-tree-count_one": "{{count}}", "product-variant-tree-count_other": "{{count}}", "product-variant-tree-add-prices": "添加价格", "product-variants-section-add-variant": "添加变体", "product-variants-section-cancel": "取消", "product-variants-section-save-and-close": "保存并关闭", "product-variants-section-edit-stock-inventory": "编辑库存", "product-variants-section-edit-variant": "编辑变体", "edit-variants-modal-cancel": "取消", "edit-variants-modal-save-and-go-back": "保存并返回", "edit-variants-modal-save-and-close": "保存并关闭", "edit-variants-modal-edit-variant": "编辑变体", "edit-variants-modal-update-success": "变体已成功更新", "edit-variants-modal-edit-variants": "编辑变体", "edit-variants-modal-product-variants": "产品变体", "edit-variants-modal-variant": "变体", "edit-variants-modal-inventory": "库存", "product-variants-section-edit-prices": "编辑价格", "product-variants-section-edit-variants": "编辑变体", "product-variants-section-edit-options": "编辑选项", "product-variants-section-product-variants": "产品变体", "product-variants-section-error": "错误", "product-variants-section-failed-to-update-product-options": "无法更新产品选项", "product-variants-section-success": "成功", "product-variants-section-successfully-updated-product-options": "成功更新产品选项", "product-variants-section-product-options": "产品选项", "product-variants-section-option-title": "选项标题", "product-variants-section-option-title-is-required": "选项标题是必需的", "product-variants-section-add-an-option": "添加一个选项", "product-variants-section-inventory": "库存", "product-variants-section-title": "标题", "product-variants-section-sku": "SKU", "product-variants-section-ean": "EAN", "product-variants-section-manage-inventory": "管理库存", "product-variants-section-duplicate-variant": "复制变体", "product-variants-section-delete-variant-label": "删除变体", "product-variants-section-yes-delete": "是的，删除", "product-variants-section-delete-variant-heading": "删除变体", "product-variants-section-confirm-delete": "您确定要删除此变体吗？", "product-variants-section-note-deleting-the-variant-will-also-remove-inventory-items-and-levels": "注意：删除变体还将删除库存项目和级别", "reset-token-card-error": "错误", "reset-token-card-reset-your-password": "重置您的密码", "reset-token-card-password-reset-description": "在下面输入您的电子邮件地址，我们将<1></1>向您发送有关如何重置<3></3>密码的说明。", "reset-token-card-email": "电子邮件", "reset-token-card-this-is-not-a-valid-email": "这不是有效的电子邮件", "reset-token-card-send-reset-instructions": "发送重置说明", "reset-token-card-successfully-sent-you-an-email": "成功向您发送了一封电子邮件", "reset-token-card-go-back-to-sign-in": "返回登录", "rma-return-product-table-product-details": "产品详情", "rma-return-product-table-quantity": "数量", "rma-select-product-table-product-details": "产品详情", "rma-select-product-table-quantity": "数量", "rma-select-product-table-refundable": "可退款", "rma-select-product-table-images-witch-count_one": "{{count}}", "rma-select-product-table-images-witch-count_other": "{{count}}", "rma-select-product-table-select-reason": "选择原因", "sidebar-store": "商店", "sidebar-orders": "订单", "sidebar-products": "产品", "sidebar-categories": "类别", "sidebar-customers": "顾客", "sidebar-inventory": "库存", "sidebar-discounts": "折扣", "sidebar-gift-cards": "礼品卡", "sidebar-pricing": "定价", "sidebar-settings": "设置", "table-container-soothed-offset_one": "{{soothedOffset}} - {{pageSize}} 共 {{count}} {{title}}", "table-container-soothed-offset_other": "{{soothedOffset}} - {{pageSize}} 共 {{count}} {{title}}", "table-container-current-page": "{{currentPage}} 共 {{soothedPageCount}}", "timeline-request-return": "请求退货", "timeline-register-exchange": "注册换货", "timeline-register-claim": "注册索赔", "timeline-success": "成功", "timeline-added-note": "添加注释", "timeline-error": "错误", "timeline-timeline": "时间线", "upload-modal-new": "新", "upload-modal-updates": "更新", "upload-modal-drop-your-file-here-or": "将您的文件拖到这里，或", "upload-modal-click-to-browse": "点击浏览。", "upload-modal-only-csv-files-are-supported": "仅支持 .csv 文件。", "upload-modal-import-file-title": "导入 {{fileTitle}}", "upload-modal-cancel": "取消", "upload-modal-import-list": "导入列表", "add-products-modal-add-products": "添加产品", "add-products-modal-search-by-name-or-description": "按名称或描述搜索...", "add-products-modal-cancel": "取消", "add-products-modal-save": "保存", "add-products-modal-product-details": "产品详情", "add-products-modal-status": "状态", "add-products-modal-variants": "变体", "templates-general": "通用", "templates-first-name": "名字", "templates-last-name": "姓氏", "templates-company": "公司", "templates-phone": "电话", "templates-billing-address": "账单地址", "templates-shipping-address": "送货地址", "templates-address": "地址", "templates-address-1": "地址1", "templates-address-2": "地址2", "templates-postal-code": "邮政编码", "templates-city": "城市", "templates-province": "省份", "templates-country": "国家", "templates-metadata": "元数据", "collection-modal-success": "成功", "collection-modal-successfully-updated-collection": "成功更新收藏", "collection-modal-error": "错误", "collection-modal-successfully-created-collection": "成功创建收藏", "collection-modal-edit-collection": "编辑收藏", "collection-modal-add-collection": "添加收藏", "collection-modal-description": "要创建收藏，您只需要标题和标识。", "collection-modal-details": "详情", "collection-modal-title-label": "标题", "collection-modal-title-placeholder": "太阳镜", "collection-modal-handle-label": "标识", "collection-modal-handle-placeholder": "太阳镜", "collection-modal-slug-description": "用于集合的URL Slug。如果留空，将自动生成。", "collection-modal-metadata": "元数据", "collection-modal-cancel": "取消", "collection-modal-save-collection": "保存集合", "collection-modal-publish-collection": "发布集合", "collection-product-table-add-products": "添加产品", "collection-product-table-products": "产品", "collection-product-table-search-products": "搜索产品", "collection-product-table-cancel": "取消", "collection-product-table-save": "保存", "collection-product-table-sort-by": "排序方式", "collection-product-table-all": "全部", "collection-product-table-newest": "最新", "collection-product-table-oldest": "最旧", "collection-product-table-title": "标题", "collection-product-table-decide-status-published": "已发布", "collection-product-table-draft": "草稿", "collection-product-table-proposed": "建议", "collection-product-table-rejected": "已拒绝", "collection-product-table-remove-product-from-collection": "从集合中移除产品", "collection-product-table-product-removed-from-collection": "产品已从集合中移除", "collections-table-delete-collection": "删除集合", "collections-table-confirm-delete": "您确定要删除此集合吗？", "collections-table-edit": "编辑", "collections-table-delete": "删除", "collections-table-title": "标题", "collections-table-handle": "标识", "collections-table-created-at": "创建于", "collections-table-updated-at": "更新于", "collections-table-products": "产品", "customer-group-table-details": "详情", "customer-group-table-delete": "删除", "customer-group-table-success": "成功", "customer-group-table-group-deleted": "组已删除", "customer-group-table-error": "错误", "customer-group-table-failed-to-delete-the-group": "无法删除组", "customer-group-table-customer-groups": "客户组", "customer-group-table-delete-from-the-group": "从组中删除", "customer-group-table-customer-groups-title": "客户组", "customer-group-table-groups": "组", "customer-group-table-all": "全部", "customer-group-table-edit-customers": "编辑客户", "customer-group-table-customers": "客户", "customer-group-table-cancel": "取消", "customer-group-table-save": "保存", "customer-orders-table-orders": "订单", "customer-orders-table-transfer-order": "转移订单", "customer-orders-table-paid": "已支付", "customer-orders-table-awaiting": "等待中", "customer-orders-table-requires-action": "需要操作", "customer-orders-table-n-a": "N/A", "customer-orders-table-fulfilled": "已完成", "customer-orders-table-shipped": "已发货", "customer-orders-table-not-fulfilled": "未完成", "customer-orders-table-partially-fulfilled": "部分完成", "customer-orders-table-partially-shipped": "部分发货", "customer-orders-table-order": "订单", "customer-orders-table-remainder-more": "+ {{remainder}} 更多", "customer-orders-table-date": "日期", "customer-orders-table-fulfillment": "履行", "customer-orders-table-status": "状态", "customer-orders-table-total": "总计", "customer-table-customers": "客户", "customer-table-edit": "编辑", "customer-table-details": "详情", "customer-table-date-added": "添加日期", "customer-table-name": "姓名", "customer-table-email": "电子邮件", "customer-table-orders": "订单", "discount-filter-dropdown-filters": "过滤器", "discount-table-discounts": "折扣", "discount-table-search-by-code-or-description": "按代码或描述搜索...", "discount-table-success": "成功", "discount-table-successfully-copied-discount": "成功复制折扣", "discount-table-error": "错误", "discount-table-scheduled": "计划中", "discount-table-expired": "已过期", "discount-table-active": "活动", "discount-table-disabled": "已禁用", "discount-table-free-shipping": "免费运费", "discount-table-code": "代码", "discount-table-description": "描述", "discount-table-amount": "金额", "discount-table-status": "状态", "discount-table-redemptions": "兑换", "discount-table-delete-discount": "删除折扣", "discount-table-confirm-delete": "您确定要删除此折扣吗？", "discount-table-publish": "发布", "discount-table-unpublish": "取消发布", "discount-table-successfully-published-discount": "成功发布折扣", "discount-table-successfully-unpublished-discount": "成功取消发布折扣", "discount-table-duplicate": "复制", "discount-table-delete": "删除", "draft-order-table-draft-orders": "草稿订单", "draft-order-table-completed": "已完成", "draft-order-table-open": "打开", "draft-order-table-draft": "草稿", "draft-order-table-order": "订单", "draft-order-table-date-added": "添加日期", "draft-order-table-customer": "客户", "draft-order-table-status": "状态", "gift-card-filter-dropdown-is-in-the-last": "在最后", "gift-card-filter-dropdown-is-older-than": "早于", "gift-card-filter-dropdown-is-after": "晚于", "gift-card-filter-dropdown-is-before": "早于", "gift-card-filter-dropdown-is-equal-to": "等于", "gift-card-filter-dropdown-filters": "过滤器", "gift-card-filter-dropdown-status": "状态", "gift-card-filter-dropdown-payment-status": "支付状态", "gift-card-filter-dropdown-fulfillment-status": "履行状态", "gift-card-filter-dropdown-date": "日期", "gift-card-table-gift-cards": "礼品卡", "gift-card-table-code": "代码", "gift-card-table-order": "订单", "gift-card-table-original-amount": "原始金额", "gift-card-table-balance": "余额", "gift-card-table-region-has-been-deleted": "地区已被删除", "gift-card-table-none": "无", "gift-card-table-created": "创建于", "image-table-file-name": "文件名", "image-table-thumbnail": "缩略图", "image-table-select-thumbnail-image-for-product": "选择要用作此产品缩略图的图像", "inventory-table-inventory-items": "库存项目", "inventory-table-actions-adjust-availability": "调整可用性", "inventory-table-view-product": "查看产品", "inventory-table-success": "成功", "inventory-table-inventory-item-updated-successfully": "库存项目已成功更新", "inventory-table-adjust-availability": "调整可用性", "inventory-table-cancel": "取消", "inventory-table-save-and-close": "保存并关闭", "inventory-table-item": "项目", "inventory-table-variant": "变体", "inventory-table-sku": "SKU", "inventory-table-reserved": "已预留", "inventory-table-in-stock": "有库存", "order-filter-dropdown-filters": "过滤器", "order-filter-dropdown-status": "状态", "order-filter-dropdown-payment-status": "支付状态", "order-filter-dropdown-fulfillment-status": "履行状态", "order-filter-dropdown-regions": "地区", "order-filter-dropdown-sales-channel": "销售渠道", "order-filter-dropdown-date": "日期", "order-table-paid": "已支付", "order-table-awaiting": "等待中", "order-table-requires-action": "需要操作", "order-table-canceled": "已取消", "order-table-n-a": "N/A", "order-table-order": "订单", "order-table-date-added": "添加日期", "order-table-customer": "客户", "order-table-fulfillment": "履行", "order-table-payment-status": "支付状态", "order-table-sales-channel": "销售渠道", "order-table-total": "总计", "order-table-filters-complete": "已完成", "order-table-filters-incomplete": "未完成", "price-list-table-filters": "过滤器", "price-list-table-status": "状态", "price-list-table-type": "类型", "price-list-table-price-lists": "价格表", "price-list-table-success": "成功", "price-list-table-successfully-copied-price-list": "成功复制价格表", "price-list-table-error": "错误", "price-list-table-delete-price-list": "删除价格表", "price-list-table-confirm-delete": "您确定要删除此价格表吗？", "price-list-table-successfully-deleted-the-price-list": "成功删除了价格表", "price-list-table-successfully-unpublished-price-list": "成功取消发布价格表", "price-list-table-successfully-published-price-list": "成功发布价格表", "price-list-table-unpublish": "取消发布", "price-list-table-publish": "发布", "price-list-table-delete": "删除", "price-list-table-name": "名称", "price-list-table-description": "描述", "price-list-table-groups": "组", "price-list-table-other-more": "+ {{other}} 更多", "price-overrides-apply-overrides-on-selected-variants": "对选定变体应用覆盖", "price-overrides-apply-on-all-variants": "应用于所有变体", "price-overrides-prices": "价格", "price-overrides-cancel": "取消", "price-overrides-save-and-close": "保存并关闭", "price-overrides-show-regions": "显示地区", "product-table-products": "产品", "product-table-copy-success": "成功", "product-table-copy-created-a-new-product": "创建了新产品", "product-table-copy-error": "错误", "product-table-delete-product": "删除产品", "product-table-confirm-delete": "您确定要删除此产品吗？", "product-table-edit": "编辑", "product-table-unpublish": "取消发布", "product-table-publish": "发布", "product-table-draft": "草稿", "product-table-published": "已发布", "product-table-success": "成功", "product-table-successfully-unpublished-product": "成功取消发布产品", "product-table-successfully-published-product": "成功发布产品", "product-table-error": "错误", "product-table-duplicate": "复制", "product-table-delete": "删除", "product-table-proposed": "建议", "product-table-published-title": "已发布", "product-table-rejected": "已拒绝", "product-table-draft-title": "草稿", "product-table-name": "名称", "product-table-collection": "集合", "product-table-status": "状态", "product-table-availability": "可用性", "product-table-inventory": "库存", "product-table-inventory-in-stock-count_one": "{{count}} 个变体有库存", "product-table-inventory-in-stock-count_other": "{{count}} 个变体有库存", "reservation-form-location": "位置", "reservation-form-choose-where-you-wish-to-reserve-from": "选择您希望从哪里预订。", "reservation-form-item-to-reserve": "要预订的项目", "reservation-form-select-the-item-that-you-wish-to-reserve": "选择您要预订的项目。", "reservation-form-item": "项目", "reservation-form-in-stock": "有库存", "reservation-form-available": "可用", "reservation-form-reserve": "预订", "reservation-form-remove-item": "移除项目", "reservation-form-description": "描述", "reservation-form-what-type-of-reservation-is-this": "这是什么类型的预订？", "reservations-table-reservations": "预订", "reservations-table-edit": "编辑", "reservations-table-delete": "删除", "reservations-table-confirm-delete": "您确定要移除此预订吗？", "reservations-table-remove-reservation": "移除预订", "reservations-table-reservation-has-been-removed": "预订已被移除", "new-success": "成功", "new-successfully-created-reservation": "预订成功创建", "new-error": "错误", "new-cancel": "取消", "new-save-reservation": "保存预订", "new-reserve-item": "预订项目", "new-metadata": "元数据", "reservations-table-order-id": "订单ID", "reservations-table-description": "描述", "reservations-table-created": "创建日期", "reservations-table-quantity": "数量", "search-modal-start-typing-to-search": "开始输入以搜索...", "search-modal-clear-search": "清除搜索", "search-modal-or": "或", "search-modal-to-navigate": "导航", "search-modal-to-select-and": "选择，并", "search-modal-to-search-anytime": "随时搜索", "templates-settings": "设置", "templates-manage-the-settings-for-your-store": "管理您商店的设置", "transfer-orders-modal-info": "信息", "transfer-orders-modal-customer-is-already-the-owner-of-the-order": "客户已经是订单的所有者", "transfer-orders-modal-success": "成功", "transfer-orders-modal-successfully-transferred-order-to-different-customer": "成功将订单转移给不同的客户", "transfer-orders-modal-error": "错误", "transfer-orders-modal-could-not-transfer-order-to-different-customer": "无法将订单转移给不同的客户", "transfer-orders-modal-transfer-order": "转移订单", "transfer-orders-modal-order": "订单", "transfer-orders-modal-current-owner": "当前所有者", "transfer-orders-modal-the-customer-currently-related-to-this-order": "当前与此订单相关的客户", "transfer-orders-modal-new-owner": "新所有者", "transfer-orders-modal-the-customer-to-transfer-this-order-to": "要将此订单转移给的客户", "transfer-orders-modal-cancel": "取消", "transfer-orders-modal-confirm": "确认", "templates-edit-user": "编辑用户", "templates-remove-user": "删除用户", "templates-resend-invitation": "重新发送邀请", "templates-success": "成功", "templates-invitiation-link-has-been-resent": "邀请链接已重新发送", "templates-copy-invite-link": "复制邀请链接", "templates-invite-link-copied-to-clipboard": "邀请链接已复制到剪贴板", "templates-remove-invitation": "删除邀请", "templates-expired": "已过期", "templates-pending": "待处理", "templates-all": "全部", "templates-member": "成员", "templates-admin": "管理员", "templates-no-team-permissions": "无团队权限", "templates-status": "状态", "templates-active": "活动", "templates-name": "名称", "templates-email": "电子邮件", "templates-team-permissions": "团队权限", "templates-confirm-remove": "您确定要删除此用户吗？", "templates-remove-user-heading": "删除用户", "templates-user-has-been-removed": "用户已被移除", "templates-confirm-remove-invite": "您确定要删除此邀请吗？", "templates-remove-invite": "删除邀请", "templates-invitiation-has-been-removed": "邀请已被删除", "multiselect-choose-categories": "选择类别", "domain-categories-multiselect-selected-with-counts_one": "{{count}}", "domain-categories-multiselect-selected-with-counts_other": "{{count}}", "details-success": "成功", "details-updated-products-in-collection": "更新集合中的产品", "details-error": "错误", "details-back-to-collections": "返回到集合", "details-edit-collection": "编辑集合", "details-delete": "删除", "details-metadata": "元数据", "details-edit-products": "编辑产品", "details-products-in-this-collection": "此集合中的产品", "details-raw-collection": "原始集合", "details-delete-collection": "删除集合", "details-successfully-deleted-collection": "成功删除集合", "details-yes-delete": "是，删除", "details-successfully-updated-customer": "成功更新客户", "details-customer-details": "客户详情", "details-general": "常规", "details-first-name": "名字", "details-lebron": "勒布朗", "details-last-name": "姓氏", "details-james": "詹姆斯", "details-email": "电子邮件", "details-phone-number": "电话号码", "details-cancel": "取消", "details-save-and-close": "保存并关闭", "details-edit": "编辑", "details-back-to-customers": "返回到客户", "details-first-seen": "首次见面", "details-phone": "电话", "details-orders": "订单", "details-user": "用户", "details-orders_one": "订单 {{count}}", "details-orders_other": "订单 {{count}}", "details-an-overview-of-customer-orders": "客户订单概览", "details-raw-customer": "原始客户", "groups-group-updated": "组已更新", "groups-group-created": "组已创建", "groups-the-customer-group-has-been-updated": "客户组已更新", "groups-the-customer-group-has-been-created": "客户组已创建", "groups-edit-customer-group": "编辑客户组", "groups-create-a-new-customer-group": "创建新的客户组", "groups-details": "详情", "groups-metadata": "元数据", "groups-cancel": "取消", "groups-edit-group": "编辑组", "groups-publish-group": "发布组", "groups-no-customers-in-this-group-yet": "此组中尚无客户", "groups-customers": "客户", "groups-edit": "编辑", "groups-delete": "删除", "groups-yes-delete": "是，删除", "groups-delete-the-group": "删除组", "groups-group-deleted": "组已删除", "groups-confirm-delete-customer-group": "您确定要删除此客户组吗？", "groups-back-to-customer-groups": "返回到客户组", "groups-new-group": "新组", "add-condition-conditions-were-successfully-added": "条件已成功添加", "add-condition-discount-conditions-updated": "折扣条件已更新", "add-condition-use-conditions-must-be-used-within-a-conditions-provider": "useConditions必须在ConditionsProvider内使用", "collections-search": "搜索...", "collections-cancel": "取消", "collections-save-and-go-back": "保存并返回", "collections-save-and-close": "保存并关闭", "customer-groups-search": "搜索...", "customer-groups-cancel": "取消", "customer-groups-save-and-go-back": "保存并返回", "customer-groups-save-and-close": "保存并关闭", "product-types-search": "搜索...", "product-types-cancel": "取消", "product-types-save-and-go-back": "保存并返回", "product-types-save-and-close": "保存并关闭", "products-search": "搜索...", "products-cancel": "取消", "products-save-and-go-back": "保存并返回", "products-save-and-close": "保存并关闭", "tags-search": "搜索...", "tags-cancel": "取消", "tags-save-and-go-back": "保存并返回", "tags-save-and-close": "保存并关闭", "edit-condition-add-conditions": "添加条件", "edit-condition-selected-with-count_one": "{{count}}", "edit-condition-selected-with-count_other": "{{count}}", "edit-condition-deselect": "取消选择", "edit-condition-remove": "移除", "edit-condition-add": "添加", "edit-condition-title": "编辑折扣条件中的{{type}}", "edit-condition-close": "关闭", "edit-condition-success": "成功", "edit-condition-the-resources-were-successfully-added": "资源已成功添加", "edit-condition-error": "错误", "edit-condition-failed-to-add-resources": "添加资源失败", "edit-condition-the-resources-were-successfully-removed": "资源已成功移除", "edit-condition-failed-to-remove-resources": "移除资源失败", "edit-condition-use-edit-condition-context-must-be-used-within-an-edit-condition-provider": "useEditConditionContext必须在EditConditionProvider内使用", "conditions-conditions": "条件", "conditions-add-condition-label": "添加条件", "conditions-this-discount-has-no-conditions": "此折扣没有条件", "conditions-success": "成功", "conditions-condition-removed": "条件已移除", "conditions-error": "错误", "conditions-edit-condition": "编辑条件", "conditions-delete-condition": "删除条件", "conditions-discount-is-applicable-to-specific-products": "折扣适用于特定产品", "conditions-discount-is-applicable-to-specific-collections": "折扣适用于特定集合", "conditions-discount-is-applicable-to-specific-product-tags": "折扣适用于特定产品标签", "conditions-discount-is-applicable-to-specific-product-types": "折扣适用于特定产品类型", "conditions-discount-is-applicable-to-specific-customer-groups": "折扣适用于特定客户组", "configurations-success": "成功", "configurations-discount-updated-successfully": "折扣已成功更新", "configurations-error": "错误", "configurations-edit-configurations": "编辑配置", "configurations-cancel": "取消", "configurations-save": "保存", "configurations-configurations": "配置", "configurations-start-date": "开始日期", "configurations-end-date": "结束日期", "configurations-delete-configuration": "删除配置", "configurations-discount-end-date-removed": "折扣结束日期已移除", "configurations-number-of-redemptions": "兑换次数", "configurations-redemption-limit-removed": "兑换限制已移除", "configurations-delete-setting": "删除设置", "configurations-discount-duration-removed": "折扣持续时间已移除", "general-success": "成功", "general-discount-updated-successfully": "折扣更新成功", "general-error": "错误", "general-edit-general-information": "编辑常规信息", "general-details": "详细信息", "general-metadata": "元数据", "general-cancel": "取消", "general-save-and-close": "保存并关闭", "general-delete-promotion": "删除促销", "general-confirm-delete-promotion": "您确定要删除此促销吗？", "general-promotion-deleted-successfully": "促销删除成功", "general-discount-published-successfully": "折扣成功发布", "general-discount-drafted-successfully": "折扣成功起草", "general-delete-discount": "删除折扣", "general-template-discount": "模板折扣", "general-published": "已发布", "general-draft": "起草", "general-discount-amount": "折扣金额", "general-valid-regions": "有效地区", "general-total-redemptions": "总兑换次数", "general-free-shipping": "免费送货", "general-unknown-discount-type": "未知折扣类型", "details-discount-deleted": "折扣已删除", "details-confirm-delete-discount": "您确定要删除此折扣吗？", "details-delete-discount": "删除折扣", "details-back-to-discounts": "返回到折扣", "details-raw-discount": "原始折扣", "discounts-add-discount": "添加折扣", "discount-form-add-conditions": "添加条件", "discount-form-choose-a-condition-type": "选择条件类型", "discount-form-you-can-only-add-one-of-each-type-of-condition": "您只能添加每种类型的条件一次", "discount-form-you-cannot-add-any-more-conditions": "您不能再添加条件", "discount-form-cancel": "取消", "discount-form-save": "保存", "add-condition-tables-cancel": "取消", "add-condition-tables-save-and-add-more": "保存并添加更多", "add-condition-tables-save-and-close": "保存并关闭", "add-condition-tables-search-by-title": "按标题搜索...", "add-condition-tables-search-groups": "搜索组...", "add-condition-tables-search-products": "搜索产品...", "add-condition-tables-search-by-tag": "按标签搜索...", "add-condition-tables-search-by-type": "按类型搜索...", "details-condition-tables-search-by-title": "按标题搜索...", "details-condition-tables-search-groups": "搜索组...", "details-condition-tables-cancel": "取消", "details-condition-tables-save-and-add-more": "保存并添加更多", "details-condition-tables-save-and-close": "保存并关闭", "details-condition-tables-search-products": "搜索产品...", "details-condition-tables-search-by-tag": "按标签搜索...", "details-condition-tables-search-by-type": "按类型搜索...", "edit-condition-tables-search-by-title": "按标题搜索...", "edit-condition-tables-title": "标题", "edit-condition-tables-search-groups": "搜索组...", "edit-condition-tables-cancel": "取消", "edit-condition-tables-delete-condition": "删除条件", "edit-condition-tables-save": "保存", "edit-condition-tables-search-products": "搜索产品...", "edit-condition-tables-search-by-tag": "按标签搜索...", "edit-condition-tables-search-by-type": "按类型搜索...", "shared-title": "标题", "shared-products": "产品", "shared-applies-to-the-selected-items": "适用于所选项目。", "shared-applies-to-all-items-except-the-selected-items": "适用于除所选项目之外的所有项目。", "shared-members": "会员", "shared-status": "状态", "shared-variants": "变体", "shared-tag": "标签", "shared-type": "类型", "edit-conditions-modal-title": "编辑{{title}}", "form-use-discount-form-must-be-a-child-of-discount-form-context": "useDiscountForm必须是DiscountFormContext的子级", "discount-form-error": "错误", "discount-form-save-as-draft": "保存为草稿", "discount-form-publish-discount": "发布折扣", "discount-form-create-new-discount": "创建新折扣", "discount-form-discount-type": "折扣类型", "discount-form-select-a-discount-type": "选择折扣类型", "discount-form-allocation": "分配", "discount-form-general": "常规", "discount-form-configuration": "配置", "discount-form-discount-code-application-disclaimer": "折扣代码从您点击发布按钮开始，如果不作更改将一直有效。", "discount-form-conditions": "条件", "discount-form-discount-code-apply-to-all-products-if-left-untouched": "如果不作更改，折扣代码将适用于所有产品。", "discount-form-add-conditions-to-your-discount": "为您的折扣添加条件", "discount-form-metadata": "元数据", "discount-form-metadata-usage-description": "元数据允许您向折扣添加附加信息。", "condition-item-remainder-more": "+{{remainder}} 更多", "conditions-edit": "编辑", "conditions-product": "产品", "conditions-collection": "集合", "conditions-tag": "标签", "conditions-customer-group": "客户组", "conditions-type": "类型", "conditions-add-condition": "添加条件", "sections-start-date": "开始日期", "sections-schedule-the-discount-to-activate-in-the-future": "安排折扣在将来生效。", "sections-select-discount-start-date": "如果您希望安排折扣在将来生效，您可以在此设置开始日期，否则折扣将立即生效。", "sections-start-time": "开始时间", "sections-discount-has-an-expiry-date": "折扣有到期日期吗？", "sections-schedule-the-discount-to-deactivate-in-the-future": "安排折扣在将来停用。", "sections-select-discount-end-date": "如果您希望安排折扣在将来停用，您可以在此设置到期日期。", "sections-expiry-date": "到期日期", "sections-expiry-time": "到期时间", "sections-limit-the-number-of-redemptions": "限制兑换次数？", "sections-limit-applies-across-all-customers-not-per-customer": "限制适用于所有客户，而不是每个客户。", "sections-limit-discount-number-of-uses": "如果您希望限制客户可以兑换此折扣的次数，您可以在此设置限制。", "sections-number-of-redemptions": "兑换次数", "sections-availability-duration": "有效期？", "sections-set-the-duration-of-the-discount": "设置折扣的持续时间。", "sections-select-a-discount-type": "选择折扣类型", "sections-total-amount": "总金额", "sections-apply-to-the-total-amount": "适用于总金额", "sections-item-specific": "特定项目", "sections-apply-to-every-allowed-item": "适用于每个允许的项目", "sections-percentage": "百分比", "sections-fixed-amount": "固定金额", "sections-discount-in-whole-numbers": "整数折扣", "sections-you-can-only-select-one-valid-region-if-you-want-to-use-the-fixed-amount-type": "如果要使用固定金额类型，您只能选择一个有效地区", "sections-free-shipping": "免费送货", "sections-override-delivery-amount": "覆盖交付金额", "sections-at-least-one-region-is-required": "至少需要一个地区", "sections-choose-valid-regions": "选择有效地区", "sections-code": "代码", "sections-summersale-10": "SUMMERSALE10", "sections-code-is-required": "代码是必需的", "sections-amount-is-required": "金额是必需的", "sections-amount": "金额", "sections-customer-invoice-code": "客户在结账时输入的代码。这将显示在客户的发票上。", "sections-uppercase-letters-and-numbers-only": "仅允许大写字母和数字。", "sections-description": "描述", "sections-summer-sale-2022": "2022年夏季特卖", "sections-this-is-a-template-discount": "这是一个模板折扣", "sections-template-discounts-description": "模板折扣允许您定义一组规则，可以在一组折扣中使用。这在需要为每个用户生成唯一代码的活动中非常有用，但所有唯一代码的规则应相同。", "discount-form-product": "产品", "discount-form-only-for-specific-products": "仅适用于特定产品", "discount-form-choose-products": "选择产品", "discount-form-customer-group": "客户组", "discount-form-only-for-specific-customer-groups": "仅适用于特定客户组", "discount-form-choose-groups": "选择组", "discount-form-tag": "标签", "discount-form-only-for-specific-tags": "仅适用于特定标签", "discount-form-collection": "集合", "discount-form-only-for-specific-product-collections": "仅适用于特定产品集合", "discount-form-choose-collections": "选择集合", "discount-form-type": "类型", "discount-form-only-for-specific-product-types": "仅适用于特定产品类型", "discount-form-choose-types": "选择类型", "utils-products": "产品", "utils-groups": "组", "utils-tags": "标签", "utils-collections": "集合", "utils-types": "类型", "gift-cards-created-gift-card": "创建礼品卡", "gift-cards-custom-gift-card-was-created-successfully": "自定义礼品卡创建成功", "gift-cards-error": "错误", "gift-cards-custom-gift-card": "自定义礼品卡", "gift-cards-details": "详细信息", "gift-cards-receiver": "接收者", "gift-cards-cancel": "取消", "gift-cards-create-and-send": "创建并发送", "details-updated-gift-card": "更新礼品卡", "details-gift-card-was-successfully-updated": "礼品卡更新成功", "details-failed-to-update-gift-card": "更新礼品卡失败", "details-edit-gift-card": "编辑礼品卡", "details-details": "详细信息", "details-edit-details": "编辑详细信息", "details-update-balance-label": "更新余额", "details-updated-status": "更新状态", "details-successfully-updated-the-status-of-the-gift-card": "成功更新礼品卡状态", "details-back-to-gift-cards": "返回到礼品卡", "details-original-amount": "原始金额", "details-balance": "余额", "details-region": "地区", "details-expires-on": "到期日", "details-created": "创建日期", "details-raw-gift-card": "原始礼品卡", "details-balance-updated": "余额已更新", "details-gift-card-balance-was-updated": "礼品卡余额已更新", "details-failed-to-update-balance": "更新余额失败", "details-update-balance": "更新余额", "manage-back-to-gift-cards": "返回到礼品卡", "gift-cards-please-enter-a-name-for-the-gift-card": "请输入礼品卡的名称", "gift-cards-please-add-at-least-one-denomination": "请至少添加一个面额", "gift-cards-denominations": "面额", "gift-cards-success": "成功", "gift-cards-successfully-created-gift-card": "成功创建礼品卡", "gift-cards-create-gift-card": "创建礼品卡", "gift-cards-gift-card-details": "礼品卡详情", "gift-cards-name": "名称", "gift-cards-the-best-gift-card": "最佳礼品卡", "gift-cards-description": "描述", "gift-cards-the-best-gift-card-of-all-time": "有史以来最好的礼品卡", "gift-cards-thumbnail": "缩略图", "gift-cards-delete": "删除", "gift-cards-size-recommended": "推荐尺寸为1200 x 1600（3:4），每个最大10MB", "gift-cards-amount": "金额", "gift-cards-add-denomination": "添加面额", "gift-cards-create-publish": "创建并发布", "gift-cards-successfully-updated-gift-card": "成功更新礼品卡", "gift-cards-gift-cards": "礼品卡", "gift-cards-manage": "管理您商店的礼品卡", "gift-cards-are-you-ready-to-sell-your-first-gift-card": "您准备好销售第一张礼品卡了吗？", "gift-cards-no-gift-card-has-been-added-yet": "尚未添加礼品卡。", "gift-cards-history": "历史记录", "gift-cards-see-the-history-of-purchased-gift-cards": "查看已购买礼品卡的历史记录", "gift-cards-successfully-deleted-gift-card": "成功删除礼品卡", "gift-cards-yes-delete": "是，删除", "gift-cards-delete-gift-card": "删除礼品卡", "inventory-filters": "过滤器", "address-form-address": "地址", "address-form-company": "公司", "address-form-address-1": "地址1", "address-form-this-field-is-required": "此字段为必填项", "address-form-address-2": "地址2", "address-form-postal-code": "邮政编码", "address-form-city": "城市", "address-form-country": "国家", "edit-sales-channels-edit-channels": "编辑通道", "edit-sales-channels-add-channels": "添加通道", "general-form-location-name": "位置名称", "general-form-flagship-store-warehouse": "旗舰店，仓库", "general-form-name-is-required": "名称是必需的", "location-card-delete-location": "删除位置", "location-card-confirm-delete": "您确定要删除此位置吗？这也将删除与此位置关联的所有库存级别和预订。", "location-card-success": "成功", "location-card-location-deleted-successfully": "位置删除成功", "location-card-error": "错误", "location-card-edit-details": "编辑详细信息", "location-card-delete": "删除", "location-card-connected-sales-channels": "已连接销售通道", "sales-channels-form-add-sales-channels": "添加销售通道", "sales-channels-form-edit-channels": "编辑通道", "sales-channels-section-not-connected-to-any-sales-channels-yet": "尚未连接到任何销售通道", "edit-success": "成功", "edit-location-edited-successfully": "位置编辑成功", "edit-error": "错误", "edit-edit-location-details": "编辑位置详情", "edit-metadata": "元数据", "edit-cancel": "取消", "edit-save-and-close": "保存并关闭", "new-location-added-successfully": "位置添加成功", "new-location-created": "位置创建成功，但关联销售通道时出现错误", "new-cancel-location-changes": "您确定要取消未保存的更改吗？", "new-yes-cancel": "是，取消", "new-no-continue-creating": "不，继续创建", "new-add-location": "添加位置", "new-add-new-location": "添加新位置", "new-general-information": "常规信息", "new-location-details": "指定此位置的详细信息", "new-select-location-channel": "指定此位置的项目可通过哪些销售通道购买。", "oauth-complete-installation": "完成安装", "claim-type-form-refund": "退款", "claim-type-form-replace": "替换", "items-to-receive-form-items-to-receive": "待收货物品", "items-to-receive-form-product": "产品", "items-to-receive-form-quantity": "数量", "items-to-receive-form-refundable": "可退款", "add-return-reason-reason-for-return": "退货原因", "add-return-reason-reason": "原因", "add-return-reason-choose-a-return-reason": "选择退货原因", "add-return-reason-note": "备注", "add-return-reason-product-was-damaged-during-shipping": "产品在运输过程中受损", "add-return-reason-cancel": "取消", "add-return-reason-save-and-go-back": "保存并返回", "add-return-reason-select-reason-title": "选择原因", "add-return-reason-edit-reason": "编辑原因", "add-return-reason-select-reason": "选择原因", "items-to-return-form-items-to-claim": "要索赔的物品", "items-to-return-form-items-to-return": "要退还的物品", "items-to-return-form-product": "产品", "items-to-return-form-quantity": "数量", "items-to-return-form-refundable": "可退款", "add-additional-items-screen-go-back": "返回", "add-additional-items-screen-add-products": "添加产品", "add-additional-items-screen-add-product-variants": "添加产品变体", "add-additional-items-screen-search-products": "搜索产品", "add-additional-items-screen-variant-price-missing": "此变体在订单的区域/货币中没有价格，无法选择。", "add-additional-items-screen-stock": "库存", "add-additional-items-screen-price": "价格", "add-additional-items-screen-price-overridden-in-price-list-applicable-to-this-order": "该价格在适用于此订单的价格表中被覆盖。", "items-to-send-form-items-to-send": "待发送商品", "items-to-send-form-add-products": "添加产品", "items-to-send-form-product": "产品", "items-to-send-form-quantity": "数量", "items-to-send-form-price": "价格", "items-to-send-form-price-overridden-in-price-list-applicable-to-this-order": "该价格在适用于此订单的价格表中被覆盖。", "refund-amount-form-cancel-editing-refund-amount": "取消编辑退款金额", "refund-amount-form-edit-refund-amount": "编辑退款金额", "refund-amount-form-refund-amount-cannot-be-negative": "退款金额不能为负数", "refund-amount-form-the-refund-amount-must-be-at-least-0": "退款金额必须至少为0", "reservation-indicator-awaiting-reservation-count": "{{awaitingReservation}} 个商品未预留", "reservation-indicator-this-item-has-been-fulfilled": "此商品已完成。", "edit-reservation-button-quantity-item-location-name": "{{quantity}} 个商品：$ {{locationName}}", "reservation-indicator-edit-reservation": "编辑预留", "rma-summaries-claimed-items": "索赔商品", "rma-summaries-replacement-items": "替换商品", "rma-summaries-customer-refund-description": "客户将对索赔商品获得全额退款，因为替换商品和运费不会被扣除。或者，您可以选择在收到退回商品或创建替换商品时设置自定义退款金额。", "rma-summaries-refund-amount": "退款金额", "rma-summaries-the-customer-will-be-refunded-once-the-returned-items-are-received": "一旦收到退回商品，客户将获得退款", "rma-summaries-the-customer-will-be-refunded-immediately": "客户将立即获得退款", "rma-summaries-receiving": "收货中", "rma-summaries-free": "免费", "send-notification-form-return": "退货", "send-notification-form-exchange": "交换", "send-notification-form-claim": "索赔", "send-notification-form-send-notifications": "发送通知", "send-notification-form-if-unchecked-the-customer-will-not-receive-communication": "如果取消选中，客户将不会收到关于此{{subject}}的通信。", "shipping-address-form-shipping-address": "收货地址", "shipping-address-form-ship-to-a-different-address": "寄到其他地址", "shipping-address-form-cancel": "取消", "shipping-address-form-save-and-go-back": "保存并返回", "shipping-address-form-shipping-information": "运输信息", "shipping-form-shipping-for-return-items": "退货物品的运输", "shipping-form-shipping-for-replacement-items": "替换商品的运输", "shipping-form-shipping-method-is-required": "需要运输方式", "shipping-form-choose-shipping-method": "选择运输方式", "shipping-form-shipping-method": "运输方式", "shipping-form-add-custom-price": "添加自定义价格", "shipping-form-return-shipping-for-items-claimed-by-the-customer-is-complimentary": "客户索赔的商品的退货运费是免费的。", "shipping-form-shipping-for-replacement-items-is-complimentary": "替换商品的运费是免费的。", "components-decrease-quantity": "减少数量", "components-increase-quantity": "增加数量", "details-successfully-updated-address": "地址更新成功", "details-billing-address": "账单地址", "details-shipping-address": "送货地址", "details-contact": "联系方式", "details-location": "地点", "claim-are-you-sure-you-want-to-close": "您确定要关闭吗？", "claim-you-have-unsaved-changes-are-you-sure-you-want-to-close": "您有未保存的更改，您确定要关闭吗？", "claim-please-select-a-reason": "请选择一个原因", "claim-a-shipping-method-for-replacement-items-is-required": "需要替换商品的运输方式", "claim-successfully-created-claim": "索赔创建成功", "claim-created": "订单 #{{display_id}} 的索赔成功创建", "claim-error-creating-claim": "创建索赔时出错", "claim-create-claim": "创建索赔", "claim-location": "地点", "claim-choose-which-location-you-want-to-return-the-items-to": "选择要退回商品的地点。", "claim-select-location-to-return-to": "选择要退还的位置", "claim-cancel": "取消", "claim-submit-and-close": "提交并关闭", "create-fulfillment-error": "错误", "create-fulfillment-please-select-a-location-to-fulfill-from": "请选择要履行的位置", "create-fulfillment-cant-allow-this-action": "无法执行此操作", "create-fulfillment-trying-to-fulfill-more-than-in-stock": "试图履行超过库存的数量", "create-fulfillment-successfully-fulfilled-order": "成功履行订单", "create-fulfillment-successfully-fulfilled-swap": "成功履行置换", "create-fulfillment-successfully-fulfilled-claim": "成功履行索赔", "create-fulfillment-success": "成功", "create-fulfillment-cancel": "取消", "create-fulfillment-create-fulfillment": "创建履行", "create-fulfillment-create-fulfillment-title": "创建履行", "create-fulfillment-locations": "位置", "create-fulfillment-choose-where-you-wish-to-fulfill-from": "选择希望从哪里履行。", "create-fulfillment-items-to-fulfill": "要履行的物品", "create-fulfillment-select-the-number-of-items-that-you-wish-to-fulfill": "选择您希望履行的物品数量。", "create-fulfillment-send-notifications": "发送通知", "create-fulfillment-when-toggled-notification-emails-will-be-sent": "切换时，将发送通知电子邮件。", "create-fulfillment-quantity-is-not-valid": "数量无效", "detail-cards-allocated": "已分配", "detail-cards-not-fully-allocated": "未完全分配", "detail-cards-subtotal": "小计", "detail-cards-shipping": "运费", "detail-cards-tax": "税费", "detail-cards-total": "总计", "detail-cards-edit-order": "编辑订单", "detail-cards-allocate": "分配", "detail-cards-discount": "折扣：", "detail-cards-original-total": "原始总计", "details-successfully-updated-the-email-address": "成功更新电子邮件地址", "details-email-address": "电子邮件地址", "details-save": "保存", "details-order-id-copied": "订单ID已复制", "details-email-copied": "电子邮件已复制", "details-cancel-order-heading": "取消订单", "details-are-you-sure-you-want-to-cancel-the-order": "您确定要取消订单吗？", "order-details-display-id": "订单 #{{display_id}}", "details-successfully-canceled-order": "成功取消订单", "details-go-to-customer": "转至客户", "details-transfer-ownership": "转让所有权", "details-edit-shipping-address": "编辑送货地址", "details-edit-billing-address": "编辑账单地址", "details-edit-email-address": "编辑电子邮件地址", "details-back-to-orders": "返回订单", "details-cancel-order": "取消订单", "details-payment": "支付", "details-refunded": "已退款", "details-total-paid": "总支付额", "details-fulfillment": "履行", "details-create-fulfillment": "创建履行", "details-shipping-method": "运输方式", "details-customer": "客户", "details-shipping": "运输", "details-billing": "结算", "details-raw-order": "原始订单", "mark-shipped-successfully-marked-order-as-shipped": "成功标记订单为已发货", "mark-shipped-successfully-marked-swap-as-shipped": "成功标记置换为已发货", "mark-shipped-successfully-marked-claim-as-shipped": "成功标记索赔为已发货", "mark-shipped-success": "成功", "mark-shipped-error": "错误", "mark-shipped-mark-fulfillment-shipped": "标记履行为已发货", "mark-shipped-tracking": "追踪", "mark-shipped-tracking-number-label": "追踪号", "mark-shipped-tracking-number": "追踪号...", "mark-shipped-add-additional-tracking-number": "+ 添加额外的追踪号", "mark-shipped-send-notifications": "发送通知", "mark-shipped-cancel": "取消", "mark-shipped-complete": "完成", "order-line-warning": "警告", "order-line-cannot-duplicate-an-item-without-a-variant": "无法复制没有变体的物品", "order-line-error": "错误", "order-line-failed-to-duplicate-item": "复制物品失败", "order-line-success": "成功", "order-line-item-removed": "物品已移除", "order-line-failed-to-remove-item": "移除物品失败", "order-line-item-added": "物品已添加", "order-line-failed-to-replace-the-item": "替换物品失败", "order-line-replace-product-variants": "替换产品变体", "order-line-replace-with-other-item": "用其他物品替换", "order-line-duplicate-item": "复制物品", "order-line-remove-item": "移除物品", "order-line-line-item-cannot-be-edited": "此行项目是履行的一部分，无法编辑。取消履行以编辑行项目。", "order-line-new": "新建", "order-line-modified": "已修改", "receive-return-please-select-at-least-one-item-to-receive": "请至少选择一件物品进行接收", "receive-return-successfully-received-return": "成功接收退货", "receive-return-received-return-for-order": "已收到订单 #{{display_id}} 的退货", "receive-return-failed-to-receive-return": "无法接收退货", "receive-return-receive-return": "接收退货", "receive-return-location": "位置", "receive-return-choose-location": "选择您希望将物品退回的位置。", "receive-return-select-location-to-return-to": "选择要退回的位置", "receive-return-no-inventory-levels-exist-for-the-items-at-the-selected-location": "在所选位置不存在物品的库存水平", "receive-return-cancel": "取消", "receive-return-save-and-close": "保存并关闭", "refund-success": "成功", "refund-successfully-refunded-order": "成功退款订单", "refund-error": "错误", "refund-create-a-refund": "创建退款", "refund-attention": "注意！", "refund-system-payment-disclaimer": "您的一个或多个支付是系统支付。请注意，默认情况下不处理此类支付的捕获和退款。", "refund-details": "详情", "refund-cannot-refund-more-than-the-orders-net-total": "不能退还超过订单净总额的金额。", "refund-discount": "折扣", "refund-reason": "原因", "refund-note": "备注", "refund-discount-for-loyal-customer": "忠实客户折扣", "refund-send-notifications": "发送通知", "refund-cancel": "取消", "refund-complete": "完成", "reservation-reservation-was-deleted": "预订已删除", "reservation-the-allocated-items-have-been-released": "已释放分配的物品。", "reservation-error": "错误", "reservation-failed-to-delete-the-reservation": "删除预订失败", "reservation-reservation-was-updated": "预订已更新", "reservation-the-reservation-change-was-saved": "预订更改已保存。", "reservation-errors": "错误", "reservation-failed-to-update-reservation": "更新预订失败", "reservation-edit-reservation": "编辑预订", "reservation-location": "位置", "reservation-choose-which-location-you-want-to-ship-the-items-from": "选择要发货物品的位置。", "reservation-items-to-allocate-title": "要分配的物品", "reservation-select-the-number-of-items-that-you-wish-to-allocate": "选择要分配的物品数量。", "reservation-max-reservation-requested": " / 已请求 {{maxReservation}}", "reservation-reserved": "已预订", "reservation-description": "描述", "reservation-what-type-of-reservation-is-this": "这是什么类型的预订？", "reservation-metadata": "元数据", "reservation-remove-metadata": "删除元数据", "reservation-add-metadata": "添加元数据", "reservation-delete-reservation": "删除预订", "reservation-cancel": "取消", "reservation-save-and-close": "保存并关闭", "reservation-couldnt-allocate-items": "无法分配物品", "reservation-items-allocated": "已分配的物品", "reservation-items-have-been-allocated-successfully": "物品已成功分配", "reservation-save-reservation": "保存预订", "reservation-loading": "加载中...", "reservation-allocate-order-items": "分配订单物品", "reservation-choose-where-you-wish-to-allocate-from": "选择要分配的位置", "reservation-items-to-allocate": "要分配的物品", "returns-success": "成功", "returns-successfully-returned-order": "成功退还订单", "returns-error": "错误", "returns-request-return": "请求退货", "returns-items-to-return": "要退回的物品", "returns-choose-which-location-you-want-to-return-the-items-to": "选择要将物品退回的位置。", "returns-select-location-to-return-to": "选择要退回的位置", "returns-selected-location-has-no-inventory-levels": "所选位置没有所选物品的库存水平。可以请求退货，但在为所选位置创建库存水平之前无法接收退货。", "returns-shipping": "运费", "returns-choose-retur,-shipping-method": "选择用于此退货的运费方式。", "returns-total-refund": "总退款", "returns-amount": "金额", "returns-send-notifications": "发送通知", "returns-notify-customer-of-created-return": "通知客户已创建退货", "returns-back": "返回", "returns-submit": "提交", "rma-sub-modals-search-for-additional": "搜索额外的", "rma-sub-modals-general": "通用", "rma-sub-modals-first-name": "名字", "rma-sub-modals-last-name": "姓氏", "rma-sub-modals-phone": "电话", "rma-sub-modals-shipping-address": "送货地址", "rma-sub-modals-address-1": "地址1", "rma-sub-modals-address-2": "地址2", "rma-sub-modals-province": "省份", "rma-sub-modals-postal-code": "邮政编码", "rma-sub-modals-city": "城市", "rma-sub-modals-country": "国家", "rma-sub-modals-back": "返回", "rma-sub-modals-add": "添加", "rma-sub-modals-name": "名称", "rma-sub-modals-status": "状态", "rma-sub-modals-in-stock": "有库存", "rma-sub-modals-products": "产品", "rma-sub-modals-search-products": "搜索产品...", "rma-sub-modals-reason-for-return": "退货原因", "rma-sub-modals-reason": "原因", "rma-sub-modals-note": "备注", "swap-success": "成功", "swap-successfully-created-exchange": "成功创建置换", "swap-error": "错误", "swap-register-exchange": "注册置换", "swap-items-to-return": "要退回的物品", "swap-shipping": "运费", "swap-shipping-method": "运费方式", "swap-add-a-shipping-method": "添加运费方式", "swap-location": "位置", "swap-choose-which-location-you-want-to-return-the-items-to": "选择要将物品退回的位置。", "swap-select-location-to-return-to": "选择要退回的位置", "swap-items-to-send": "要发送的物品", "swap-add-product": "添加产品", "swap-return-total": "退货总额", "swap-additional-total": "附加总额", "swap-outbond-shipping": "出库运费", "swap-calculated-at-checkout": "在结账时计算", "swap-estimated-difference": "估计的差额", "swap-send-notifications": "发送通知", "swap-if-unchecked-the-customer-will-not-receive-communication-about-this-exchange": "如果未选中，客户将不会收到有关此置换的通信", "swap-complete": "完成", "templates-shipped": "已发货", "templates-fulfilled": "已履行", "templates-canceled": "已取消", "templates-partially-fulfilled": "部分履行", "templates-fulfillment-status-requires-action": "需要操作的履行状态", "templates-awaiting-fulfillment": "等待履行", "templates-partially-shipped": "部分已发货", "templates-cancel-fulfillment-heading": "取消履行？", "templates-are-you-sure-you-want-to-cancel-the-fulfillment": "您确定要取消履行吗？", "templates-successfully-canceled-swap": "成功取消置换", "templates-error": "错误", "templates-successfully-canceled-claim": "成功取消索赔", "templates-successfully-canceled-fulfillment": "成功取消履行", "templates-fulfillment-has-been-canceled": "履行已取消", "templates-fulfilled-by-provider": "{{title}} 由{{provider}}履行", "templates-not-shipped": "未发货", "templates-tracking": "追踪", "templates-shipped-from": "发货自", "templates-shipping-from": "从何处发货", "templates-mark-shipped": "标记已发货", "templates-cancel-fulfillment": "取消履行", "templates-completed": "已完成", "templates-processing": "处理中", "templates-requires-action": "需要操作", "templates-capture-payment": "捕获支付", "templates-successfully-captured-payment": "成功捕获支付", "templates-refund": "退款", "templates-total-for-swaps": "置换总计", "templates-refunded-for-swaps": "置换退款", "templates-refunded-for-returns": "退货退款", "templates-manually-refunded": "手动退款", "templates-net-total": "净总额", "templates-paid": "已支付", "templates-awaiting-payment": "等待支付", "templates-payment-status-requires-action": "需要操作的支付状态", "draft-orders-completed": "已完成", "draft-orders-open": "打开", "draft-orders-mark-as-paid": "标记为已支付", "draft-orders-success": "成功", "draft-orders-successfully-mark-as-paid": "成功标记为已支付", "draft-orders-error": "错误", "draft-orders-successfully-canceled-order": "成功取消订单", "draft-orders-back-to-draft-orders": "返回到草稿订单", "on-mark-as-paid-confirm-order-id": "订单 #{{display_id}}", "draft-orders-go-to-order": "前往订单", "draft-orders-cancel-draft-order": "取消草稿订单", "draft-orders-draft-order": "草稿订单", "draft-orders-email": "电子邮件", "draft-orders-phone": "电话", "draft-orders-amount": "金额 {{currency_code}}", "draft-orders-payment": "支付", "draft-orders-subtotal": "小计", "draft-orders-shipping": "运费", "draft-orders-tax": "税费", "draft-orders-total-to-pay": "总支付金额", "draft-orders-payment-link": "支付链接:", "draft-orders-configure-payment-link-in-store-settings": "在商店设置中配置支付链接", "draft-orders-shipping-method": "运费方式", "draft-orders-data": "数据", "draft-orders-1-item": "(1 件商品)", "draft-orders-customer": "客户", "draft-orders-edit-shipping-address": "编辑送货地址", "draft-orders-edit-billing-address": "编辑账单地址", "draft-orders-go-to-customer": "前往客户", "draft-orders-contact": "联系", "draft-orders-billing": "账单", "draft-orders-raw-draft-order": "原始草稿订单", "draft-orders-are-you-sure": "您确定吗？", "draft-orders-remove-resource-heading": "移除 {{resource}}", "draft-orders-remove-resource-success-text": "{{resource}} 已被移除", "draft-orders-this-will-create-an-order-mark-this-as-paid-if-you-received-the-payment": "这将创建一个订单。如果您已收到付款，请标记为已支付。", "draft-orders-mark-paid": "标记为已支付", "draft-orders-cancel": "取消", "draft-orders-create-draft-order": "创建草稿订单", "edit-amount-paid": "支付金额", "edit-new-total": "新总额", "edit-difference-due": "差额", "edit-back": "返回", "edit-save-and-go-back": "保存并返回", "edit-order-edit-set-as-requested": "订单编辑已设为已请求", "edit-failed-to-request-confirmation": "请求确认失败", "edit-added-successfully": "成功添加", "edit-error-occurred": "发生错误", "edit-add-product-variants": "添加产品变体", "edit-edit-order": "编辑订单", "edit-items": "商品", "edit-add-items": "添加商品", "edit-filter-items": "筛选商品...", "edit-note": "备注", "edit-add-a-note": "添加备注...", "variants-table-location": " 在 {{location}}", "edit-product": "产品", "edit-in-stock": "有库存", "edit-price": "价格", "edit-products": "产品", "edit-search-product-variants": "搜索产品变体...", "orders-success": "成功", "orders-successfully-initiated-export": "成功启动导出", "orders-error": "错误", "orders-export-orders": "导出订单", "components-billing-address": "账单地址", "components-use-same-as-shipping": "使用与送货相同的地址", "components-e-g-gift-wrapping": "例如礼品包装", "components-title": "标题", "components-price": "价格", "components-quantity": "数量", "components-back": "返回", "components-add": "添加", "components-items-for-the-order": "订单商品", "components-details": "详情", "components-price-excl-taxes": "价格（不含税）", "components-add-custom": "添加自定义", "components-add-existing": "添加已有的", "components-add-products": "添加产品", "components-add-custom-item": "添加自定义商品", "components-choose-region": "选择地区", "components-region": "地区", "select-shipping-to-name": "(寄往 {{name}})", "components-attention": "注意！", "components-no-options-for-orders-without-shipping": "对于没有配送的订单，您没有任何选项。请添加一个（例如“门店履行”），在地区设置中取消选中“在网站上显示”并继续。", "components-choose-a-shipping-method": "选择一种运费方式", "components-set-custom-price": "设置自定义价格", "components-custom-price": "自定义价格", "components-customer-and-shipping-details": "客户和送货详情", "components-find-existing-customer": "查找现有客户", "components-email": "电子邮件", "components-choose-existing-addresses": "选择现有地址", "components-create-new": "创建新的", "components-the-discount-is-not-applicable-to-the-selected-region": "折扣不适用于所选地区", "components-the-discount-code-is-invalid": "折扣码无效", "components-add-discount": "添加折扣", "components-summer-10": "SUMMER10", "components-discount": "折扣", "select-shipping-code": "(代码：{{code}})", "components-type": "类型", "components-value": "值", "components-address": "地址", "components-shipping-method": "运费方式", "components-billing-details": "账单详情", "components-edit": "编辑", "form-use-new-order-form-must-be-used-within-new-order-form-provider": "useNewOrderForm 必须在 NewOrderFormProvider 内使用", "new-order-created": "订单已创建", "new-create-draft-order": "创建草稿订单", "price-list-product-filter-created-at": "创建于", "price-list-product-filter-updated-at": "更新于", "price-list-details-drawer-prompt-title": "您确定吗？", "price-list-details-drawer-prompt-description": "您有未保存的更改，确定要退出吗？", "price-list-details-notification-succes-title": "价格表已更新", "price-list-details-drawer-notification-success-message": "成功更新价格表", "price-list-details-drawer-notification-error-title": "发生错误", "price-list-details-drawer-title": "编辑价格表详情", "price-list-details-drawer-cancel-button": "取消", "price-list-details-drawer-save-button": "保存", "price-list-details-section-prompt-confirm-text": "删除", "price-list-details-section-prompt-cancel-text": "取消", "price-list-details-section-prompt-title": "删除价格表", "price-list-details-section-prompt-description": "您确定要删除价格表 \"{{name}}\" 吗？", "price-list-details-section-delete-notification-success-title": "成功删除价格表", "price-list-details-section-delete-notification-success-message": "价格表 \"{{name}}\" 已成功删除", "price-list-details-section-delete-notification-error-title": "删除价格表失败", "price-list-details-section-customer-groups": "客户组", "price-list-details-section-last-edited": "最后编辑", "price-list-details-section-number-of-prices": "价格", "price-list-details-section-status-menu-expired": "已过期", "price-list-details-section-status-menu-draft": "草稿", "price-list-details-section-status-menu-scheduled": "已计划", "price-list-details-section-status-active": "激活", "price-list-details-section-status-menu-notification-success-title": "成功更新价格表状态", "price-list-details-section-status-menu-notification-success-message": "价格表状态已成功更新为 {{status}}", "price-list-details-section-status-menu-notification-error-title": "更新价格表状态失败", "price-list-details-section-status-menu-item-draft": "草稿", "price-list-details-section-status-menu-item-activate": "激活", "price-list-details-menu-item-edit": "编辑详情", "price-list-details-menu-item-delete": "删除", "price-list-edit-error": "加载价格表时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "price-list-new-form-prompt-title": "您确定吗？", "price-list-new-form-prompt-exit-description": "您有未保存的更改，您确定要退出吗？", "price-list-new-form-prompt-back-description": "您有未保存的更改，您确定要返回吗？", "price-list-add-products-modal-no-prices-error": "请至少为一种产品分配价格。", "price-list-add-products-modal-missing-prices-title": "价格表不完整", "price-list-add-products-modal-missing-prices-description": "未为您选择的所有产品分配价格。您要继续吗？", "price-list-add-products-modal-success-title": "添加新价格", "price-list-add-products-modal-success-message": "新价格已添加到价格表。", "price-list-add-products-modal-error-title": "发生错误", "price-list-add-products-modal-back-button-cancel": "取消", "price-list-add-products-modal-back-button": "返回", "price-list-add-products-modal-next-button-continue": "继续", "price-list-add-products-modal-next-button-submit-and-close": "提交并关闭", "price-list-add-products-modal-next-button-continue-save-prices": "保存价格", "price-list-add-products-modal-products-tab": "选择产品", "price-list-add-products-modal-prices-tab": "编辑价格", "price-list-add-products-modal-error": "准备表单时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "price-list-edit-prices-modal-prompt-title": "未保存的更改", "price-list-edit-prices-modal-prompt-exit-description": "您有未保存的更改，您确定要退出吗？", "price-list-edit-prices-modal-prompt-back-description": "您有未保存的更改，您确定要返回吗？", "price-list-edit-prices-modal-notification-update-error": "发生错误", "price-list-edit-prices-modal-notification-remove-error-title": "发生错误", "price-list-edit-prices-modal-notification-remove-error-description": "某些价格未正确更新。请重试。", "price-list-edit-prices-modal-notification-update-success-title": "价格已更新", "price-list-edit-prices-modal-notification-update-success-description": "价格已成功更新", "price-list-edit-prices-modal-next-button-save-and-close": "保存并关闭", "price-list-edit-prices-modal-next-button-save": "保存价格", "price-list-edit-prices-modal-back-button-cancel": "取消", "price-list-edit-prices-modal-back-button-back": "返回", "price-list-edit-prices-modal-overview-tab": "编辑价格", "price-list-edit-prices-modal-error-loading": "准备表单时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "price-list-prices-section-prompt-title": "您确定吗？", "price-list-prices-section-prompt-description": "这将永久删除列表中的产品价格", "price-list-prices-secton-delete-success-title": "删除价格", "price-list-prices-section-delete-success-description_one": "已成功删除 {{count}} 种产品的价格", "price-list-prices-section-delete-success-description_other": "已成功删除 {{count}} 种产品的价格", "price-list-prices-section-delete-error-title": "发生错误", "price-list-prices-section-heading": "价格", "price-list-prices-section-search-placeholder": "搜索产品", "price-list-prices-section-prices-menu-edit": "编辑价格", "price-list-prices-section-prices-menu-add": "添加产品", "price-list-prices-section-table-load-error": "获取产品时发生错误。尝试重新加载页面，或者如果问题仍然存在，请稍后再试。", "price-list-prices-section-bar-count_one": "已选择 {{count}} 项", "price-list-prices-section-bar-count_other": "已选择 {{count}} 项", "price-list-prices-section-edit-command": "编辑", "price-list-prices-section-delete-command": "删除", "price-list-prices-section-select-all-checkbox-label": "选择当前页面上的所有产品", "price-list-prices-section-select-checkbox-label": "选择行", "price-list-prices-section-table-product": "产品", "price-list-prices-section-table-thumbnail-alt": "{{title}} 缩略图", "price-list-prices-section-table-collection": "系列", "price-list-prices-section-table-variants": "变体", "price-list-details-form-type-heading": "类型", "price-list-details-form-type-description": "选择要创建的价格表类型。", "price-list-details-form-type-label-sale": "特价", "price-list-details-form-type-hint-sale": "如果您正在创建特价，请使用此选项。", "price-list-details-form-type-label-override": "覆盖", "price-list-details-form-type-hint-override": "如果您要覆盖价格，请使用此选项。", "price-list-details-form-general-heading": "常规", "price-list-details-form-general-description": "为价格表选择标题和描述。", "price-list-details-form-general-name-label": "名称", "price-list-details-form-general-name-placeholder": "黑色星期五特卖", "price-list-details-form-general-description-label": "描述", "price-list-details-form-general-description-placeholder": "黑色星期五特卖的价格...", "price-list-details-form-tax-inclusive-label": "含税价格", "price-list-details-form-tax-inclusive-hint": "选择使此价格表中的所有价格都包含税。", "price-list-details-form-dates-starts-at-heading": "价格表是否有开始日期？", "price-list-details-form-dates-starts-at-description": "安排将来激活价格覆盖。", "price-list-details-form-dates-starts-at-label": "开始日期", "price-list-details-form-ends-at-heading": "价格表是否有到期日期？", "price-list-details-form-ends-at-description": "安排将来停用价格覆盖。", "price-list-details-form-ends-at-label": "到期日期", "price-list-details-form-customer-groups-name": "名称", "price-list-details-form-customer-groups-members": "成员", "price-list-details-form-customer-groups-error": "加载客户组时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "price-list-details-form-customer-groups-no-groups": "找不到客户组。", "price-list-details-form-customer-groups-heading": "客户可用性", "price-list-details-form-customer-groups-description": "指定价格覆盖应适用于哪些客户组。", "price-list-details-form-customer-groups-content-heading": "客户组", "price-list-details-form-customer-groups-search-placeholder": "搜索", "price-list-prices-form-products-error": "准备表单时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "price-list-prices-form-heading": "编辑价格", "price-list-prices-form-variant": "变体", "price-list-prices-form-sku": "SKU", "price-list-prices-form-prices": "价格", "price-list-prices-form-prices-variant-count_one": "{{count}} 个变体", "price-list-prices-form-prices-variant-count_other": "{{count}} 个变体", "price-list-prices-form-add-prices-button": "添加价格", "price-list-prices-form-prices-count_one": "{{count}} 个价格", "price-list-prices-form-prices-count_other": "{{count}} 个价格", "price-list-product-prices-form-invalid-data-title": "无效的数据", "price-list-product-prices-form-invalid-data-body": "您粘贴的数据包含非数字的值。", "price-list-product-prices-form-column-visibility-button": "查看", "price-list-product-prices-form-column-visibility-currencies-label": "货币", "price-list-product-prices-form-column-visibility-regions-label": "地区", "price-list-product-prices-form-column-product-label": "产品", "price-list-product-prices-form-column-currencies-price-label": "{{code}} 价格", "price-list-product-prices-form-column-regions-price-label": "{{name}} ({{code}}) 价格", "price-list-products-form-select-all": "选择当前页面上的所有产品", "price-list-products-form-select-row": "选择行", "price-list-products-form-product-label": "产品", "price-list-products-form-product-thumbnail": "{{title}} 缩略图", "price-list-products-form-collection-label": "系列", "price-list-products-form-sales-channels-label": "可用性", "price-list-products-form-sales-channels-value": "{{first}} + {{remaining}} 更多", "price-list-products-form-status-label": "状态", "price-list-products-form-inventory-label": "库存", "price-list-products-form-inventory-value": "库存 {{variants}} 个变体中有 {{totalStock}} 个", "price-list-products-form-loading": "正在加载产品", "price-list-products-form-error": "加载产品时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "price-list-products-form-no-products": "找不到产品。", "price-list-products-form-heading": "选择产品", "price-list-products-form-search-placeholder": "搜索", "price-list-new-form-no-prices-error": "请至少为一种产品设置价格。", "price-list-new-form-missing-prices-title": "不完整的价格表", "price-list-new-products-modal-missing-prices-description": "未为您选择的所有产品分配价格。您要继续吗？", "price-list-new-form-notification-success-title": "已创建价格表", "price-list-new-form-notification-success-message": "成功创建价格表", "price-list-new-form-notification-error-title": "发生错误", "price-list-new-form-next-button-save-and-publish": "保存并发布", "price-list-new-form-next-button-save": "保存价格", "price-list-new-form-next-button-continue": "继续", "price-list-new-form-back-button-cancel": "取消", "price-list-new-form-back-button-back": "返回", "price-list-new-form-details-tab": "创建价格表", "price-list-new-form-products-tab": "选择产品", "price-list-new-form-prices-tab": "编辑价格", "price-list-new-form-save-as-draft": "保存为草稿", "price-list-new-form-error-loading-products": "准备表单时发生错误。重新加载页面并重试。如果问题仍然存在，请稍后再试。", "components-success": "成功", "components-successfully-updated-category-tree": "成功更新类别树", "components-error": "错误", "components-failed-to-update-category-tree": "无法更新类别树", "components-delete": "删除", "components-category-deleted": "类别已删除", "components-category-deletion-failed": "类别删除失败", "components-category-status-is-inactive": "类别状态为非活动", "components-category-visibility-is-private": "类别可见性为私有", "components-add-category-item-to": "将类别项添加到", "modals-public": "公开", "modals-private": "私有", "modals-active": "活动", "modals-inactive": "非活动", "modals-success": "成功", "modals-successfully-created-a-category": "成功创建类别", "modals-failed-to-create-a-new-category": "无法创建新类别", "modals-error": "错误", "modals-save-category": "保存类别", "modals-add-category-to": "将类别添加到 {{name}}", "modals-add-category": "添加类别", "modals-details": "详情", "modals-name": "名称", "modals-give-this-category-a-name": "为此类别命名", "modals-handle": "句柄", "modals-custom-handle": "自定义句柄", "modals-description": "描述", "modals-give-this-category-a-description": "为此类别添加描述", "modals-status": "状态", "modals-visibility": "可见性", "modals-successfully-updated-the-category": "成功更新类别", "modals-failed-to-update-the-category": "无法更新类别", "modals-edit-product-category": "编辑产品类别", "modals-cancel": "取消", "modals-save-and-close": "保存并关闭", "pages-no-product-categories-yet": "尚无产品类别，使用上面的按钮创建您的第一个类别。", "pages-add-category": "添加类别", "pages-product-categories": "产品类别", "pages-helps-you-to-keep-your-products-organized": "帮助您组织产品。", "batch-job-success": "成功", "batch-job-import-confirmed-for-processing-progress-info-is-available-in-the-activity-drawer": "确认导入进行中。进度信息可在活动抽屉中查看。", "batch-job-error": "错误", "batch-job-import-failed": "导入失败。", "batch-job-failed-to-delete-the-csv-file": "无法删除 CSV 文件", "batch-job-failed-to-cancel-the-batch-job": "无法取消批处理作业", "batch-job-products-list": "产品列表", "batch-job-unsure-about-how-to-arrange-your-list": "不确定如何排列列表？", "batch-job-download-template": "下载下面的模板以确保您遵循正确的格式。", "batch-job-imports-description": "通过导入，您可以添加或更新产品。要更新现有产品/变体，必须在“产品/变体 ID”列中设置现有 ID。如果值未设置，将创建新记录。在导入产品之前，系统将向您确认。", "products-filters": "过滤器", "products-status": "状态", "products-tags": "标签", "products-spring-summer": "春季，夏季...", "new-sales-channels": "销售渠道", "new-this-product-will-only-be-available-in-the-default-sales-channel-if-left-untouched": "如果不做更改，此产品将仅在默认销售渠道中提供。", "new-change-availablity": "更改可用性", "add-variants-a-variant-with-these-options-already-exists": "具有这些选项的变体已存在。", "add-variants-product-options": "产品选项", "add-variants-options-are-used-to-define-the-color-size-etc-of-the-product": "选项用于定义产品的颜色、尺寸等。", "add-variants-option-title": "选项标题", "add-variants-variations-comma-separated": "变化（逗号分隔）", "add-variants-color": "颜色...", "add-variants-already-exists": "已存在", "add-variants-blue-red-black": "蓝色，红色，黑色...", "add-variants-add-an-option": "添加选项", "add-variants-product-variants": "产品变体", "add-variants-you-must-add-at-least-one-product-option-before-you-can-begin-adding-product-variants": "在开始添加产品变体之前，您必须添加至少一个产品选项。", "add-variants-variant": "变体", "add-variants-inventory": "库存", "add-variants-add-a-variant": "添加变体", "add-variants-create-variant": "创建变体", "add-variants-cancel": "取消", "add-variants-save-and-close": "保存并关闭", "new-variant-a-variant-with-these-options-already-exists": "具有这些选项的变体已存在。", "new-variant-are-you-sure-you-want-to-delete-this-variant": "您确定要删除此变体吗？", "new-variant-delete-variant": "删除变体", "new-variant-edit": "编辑", "new-variant-delete": "删除", "new-variant-edit-variant": "编辑变体", "new-variant-cancel": "取消", "new-variant-save-and-close": "保存并关闭", "new-something-went-wrong-while-trying-to-upload-images": "尝试上传图像时出现问题。", "new-no-file-service-configured": "您可能没有配置文件服务。请联系您的管理员。", "new-upload-thumbnail-error": "尝试上传缩略图时出现问题。", "new-save-as-draft": "保存为草稿", "new-publish-product": "发布产品", "new-general-information-title": "常规信息", "new-to-start-selling-all-you-need-is-a-name-and-a-price": "要开始销售，您只需要名称和价格。", "new-organize-product": "组织产品", "new-add-variations-of-this-product": "添加此产品的变体。", "new-offer-your-customers-different-options-for-color-format-size-shape-etc": "为您的客户提供颜色、格式、尺寸、形状等不同的选择。", "new-used-for-shipping-and-customs-purposes": "用于运输和海关目的。", "new-dimensions": "尺寸", "new-customs": "海关", "new-used-to-represent-your-product-during-checkout-social-sharing-and-more": "用于在结账、社交共享等过程中代表您的产品。", "new-media": "媒体", "new-add-images-to-your-product": "为您的产品添加图像。", "overview-import-products": "导入产品", "overview-export-products": "导出产品", "overview-new-product": "新产品", "overview-new-collection": "新系列", "overview-success": "成功", "overview-successfully-created-collection": "成功创建系列", "overview-error": "错误", "overview-successfully-initiated-export": "成功启动导出", "modals-add-sales-channels": "添加销售渠道", "modals-find-channels": "查找渠道", "modals-updated-the-api-key": "更新了 API 密钥", "modals-failed-to-update-the-api-key": "无法更新 API 密钥", "modals-edit-api-key-details": "编辑 API 密钥详情", "modals-title": "标题", "modals-name-your-key": "为您的密钥命名", "modals-sales-channels-added-to-the-scope": "销售渠道已添加到范围内", "modals-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "在将销售渠道添加到密钥范围时发生错误", "modals-add-and-go-back": "添加并返回", "modals-add-and-close": "添加并关闭", "modals-sales-channels-removed-from-the-scope": "销售渠道已从范围中移除", "modals-error-occurred-while-removing-sales-channels-from-the-scope-of-the-key": "在从密钥范围中移除销售渠道时发生错误", "modals-edit-sales-channels": "编辑销售渠道", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_one": "{{count}}", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_other": "{{count}}", "modals-deselect": "取消选择", "modals-remove": "移除", "modals-add-channels": "添加渠道", "modals-close": "关闭", "pages-sales-channels": "销售渠道", "pages-connect-as-many-sales-channels-to-your-api-key-as-you-need": "将尽可能多的销售渠道连接到您的 API 密钥。", "pages-add-sales-channels": "添加销售渠道", "pages-edit-sales-channels": "编辑销售渠道", "pages-success": "成功", "pages-created-a-new-api-key": "创建了新的 API 密钥", "pages-error": "错误", "pages-failed-to-create-a-new-api-key": "无法创建新的 API 密钥", "pages-sales-channels-added-to-the-scope": "销售渠道已添加到范围内", "pages-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "在将销售渠道添加到密钥范围时发生错误", "pages-publish-api-key": "发布 API 密钥", "pages-create-api-key": "创建 API 密钥", "pages-create-and-manage-api-keys-right-now-this-is-only-related-to-sales-channels": "创建和管理 API 密钥。目前仅与销售渠道相关。", "pages-create-api-key-label": "创建 API 密钥", "pages-back-to-settings": "返回到设置", "pages-publishable-api-keys": "可发布的 API 密钥", "pages-these-publishable-keys-will-allow-you-to-authenticate-api-requests": "这些可发布的密钥将允许您验证 API 请求。", "tables-name": "名称", "tables-token": "令牌", "tables-done": "完成", "tables-copy-to-clipboard": "复制到剪贴板", "tables-created": "创建", "tables-status": "状态", "tables-revoked": "已撤销", "tables-live": "已上线", "tables-edit-api-key-details": "编辑 API 密钥详细信息", "tables-edit-sales-channels": "编辑销售渠道", "tables-copy-token": "复制令牌", "tables-revoke-token": "撤销令牌", "tables-delete-api-key": "删除 API 密钥", "tables-yes-delete": "是的，删除", "tables-api-key-deleted": "API 密钥已删除", "tables-are-you-sure-you-want-to-delete-this-public-key": "您确定要删除此公钥吗？", "tables-delete-key": "删除密钥", "tables-yes-revoke": "是的，撤销", "tables-api-key-revoked": "API 密钥已撤销", "tables-are-you-sure-you-want-to-revoke-this-public-key": "您确定要撤销此公钥吗？", "tables-revoke-key": "撤销密钥", "tables-api-keys": "API 密钥", "tables-no-keys-yet-use-the-above-button-to-create-your-first-publishable-key": "尚无密钥，请使用上面的按钮创建您的第一个可发布密钥", "tables-title": "标题", "tables-description": "描述", "tables-no-added-sales-channels": "尚未添加销售渠道", "tables-sales-channels": "销售渠道", "form-title": "标题", "form-website-app-amazon-physical-store-pos-facebook-product-feed": "网站、应用、亚马逊、实体店 POS、Facebook 产品提要...", "form-description": "描述", "form-available-products-at-our-website-app": "我们网站、应用上的可用产品...", "form-success": "成功", "form-the-sales-channel-is-successfully-updated": "销售渠道已成功更新", "form-error": "错误", "form-failed-to-update-the-sales-channel": "无法更新销售渠道", "form-sales-channel-details": "销售渠道详情", "form-general-info": "常规信息", "form-name": "名称", "form-close": "关闭", "form-save": "保存", "pages-draft": "草稿", "pages-control-which-products-are-available-in-which-channels": "控制产品在哪些渠道中可用", "pages-search-by-title-or-description": "按标题或描述搜索", "pages-confirm-delete-sales-channel": "您确定要删除此销售渠道吗？您所做的设置将永久消失。", "pages-delete-channel-heading": "删除渠道", "pages-edit-general-info": "编辑常规信息", "pages-add-products": "添加产品", "pages-delete-channel": "删除渠道", "pages-disabled": "已禁用", "pages-enabled": "已启用", "tables-collection": "系列", "tables-start-building-your-channels-setup": "开始构建您的渠道设置...", "tables-no-products-in-channels": "您尚未向此渠道添加任何产品，但一旦添加，它们将显示在此处。", "tables-add-products": "添加产品", "tables-details": "详情", "tables-remove-from-the-channel": "从渠道中移除", "tables-products": "产品", "sales-channels-table-placeholder-selected-with-counts_one": "{{count}}", "sales-channels-table-placeholder-selected-with-counts_other": "{{count}}", "tables-remove": "移除", "components-successfully-updated-currency": "货币已成功更新", "components-default": "默认", "default-store-currency-success": "成功", "default-store-currency-successfully-updated-default-currency": "默认货币已成功更新", "default-store-currency-error": "错误", "default-store-currency-default-store-currency": "默认商店货币", "default-store-currency-this-is-the-currency-your-prices-are-shown-in": "这是显示价格的货币。", "store-currencies-success": "成功", "store-currencies-successfully-updated-currencies": "成功更新货币", "store-currencies-error": "错误", "store-currencies-cancel": "取消", "store-currencies-save-and-go-back": "保存并返回", "store-currencies-save-and-close": "保存并关闭", "store-currencies-add-store-currencies": "添加商店货币", "store-currencies-current-store-currencies": "当前商店货币", "store-currencies-close": "关闭", "current-currencies-screen-selected-with-count_one": "{{count}}", "current-currencies-screen-selected-with-count_other": "{{count}}", "store-currencies-deselect": "取消选择", "store-currencies-remove": "移除", "store-currencies-add-currencies": "添加货币", "store-currencies-store-currencies": "商店货币", "store-currencies-all-the-currencies-available-in-your-store": "您商店中所有可用的货币。", "store-currencies-edit-currencies": "编辑货币", "currencies-an-unknown-error-occurred": "发生未知错误", "currencies-error": "错误", "currencies-back-to-settings": "返回设置", "currencies-manage-the-markets-that-you-will-operate-within": "管理您将在其中运营的市场。", "currencies-include-or-exclude-taxes": "决定在定义此货币的价格时是否包括或排除税费", "currencies-tax-incl-prices": "含税价格", "settings-error": "错误", "settings-malformed-swap-url": "格式错误的交换 URL", "settings-malformed-payment-url": "格式错误的付款 URL", "settings-malformed-invite-url": "格式错误的邀请 URL", "settings-success": "成功", "settings-successfully-updated-store": "成功更新商店", "settings-back-to-settings": "返回设置", "settings-save": "保存", "settings-cancel": "取消", "settings-store-details": "商店详情", "settings-manage-your-business-details": "管理您的业务详情", "settings-general": "常规", "settings-store-name": "商店名称", "settings-store": "Acme 商店", "settings-advanced-settings": "高级设置", "settings-swap-link-template": "交换链接模板", "settings-draft-order-link-template": "草稿订单链接模板", "settings-invite-link-template": "邀请链接模板", "settings-manage-the-general-settings-for-your-store": "管理您商店的常规设置", "settings-manage-the-settings-for-your-store-apos-s-extensions": "管理您商店扩展的设置", "edit-user-information-success": "成功", "edit-user-information-your-information-was-successfully-updated": "您的信息已成功更新", "edit-user-information-edit-information": "编辑信息", "edit-user-information-cancel": "取消", "edit-user-information-submit-and-close": "提交并关闭", "personal-information-back-to-settings": "返回设置", "personal-information-personal-information": "个人信息", "personal-information-manage-your-profile": "管理您的个人资料", "personal-information-language-settings-title": "语言", "personal-information-language-settings-description": "调整管理员的语言", "personal-information-language-settings-help-us-translate": "帮助我们翻译", "personal-information-usage-insights-title": "使用见解", "usage-insights-disabled": "已禁用", "usage-insights-active": "已启用", "usage-insights-share-usage-insights-and-help-us-improve": "分享使用见解，帮助我们改进", "usage-insights-edit-preferences": "编辑首选项", "usage-insights-success": "成功", "usage-insights-your-information-was-successfully-updated": "您的信息已成功更新", "usage-insights-error": "错误", "usage-insights-cancel": "取消", "usage-insights-submit-and-close": "提交并关闭", "region-form-title": "标题", "region-form-europe": "欧洲", "region-form-currency-code-is-required": "货币代码是必需的", "region-form-currency": "货币", "region-form-choose-currency": "选择货币", "region-form-default-tax-rate": "默认税率", "region-form-tax-rate-is-required": "税率是必需的", "region-form-tax-rate-must-be-equal-to-or-less-than-100": "税率必须等于或小于 100", "region-form-default-tax-code": "默认税收代码", "region-form-countries": "国家", "region-form-choose-countries": "选择国家", "region-form-tax-inclusive-prices": "含税价格", "region-form-when-enabled-region-prices-will-be-tax-inclusive": "启用区域价格时将包含税费。", "region-form-payment-providers-are-required": "需要付款提供商", "region-form-payment-providers": "付款提供商", "region-form-choose-payment-providers": "选择付款提供商...", "region-form-fulfillment-providers-are-required": "需要履行提供商", "region-form-fulfillment-providers": "履行提供商", "region-form-choose-fulfillment-providers": "选择履行提供商...", "shipping-option-card-success": "成功", "shipping-option-card-shipping-option-updated": "已更新运输选项", "shipping-option-card-error": "错误", "shipping-option-card-edit-shipping-option": "编辑运输选项", "shipping-option-card-fulfillment-method": "履行方法", "shipping-option-card-cancel": "取消", "shipping-option-card-save-and-close": "保存并关闭", "shipping-option-card-shipping-option-has-been-deleted": "运输选项已被删除", "shipping-option-card-flat-rate": "固定费率", "shipping-option-card-calcualted": "计算", "shipping-option-card-min-subtotal": "最低小计:", "shipping-option-card-max-subtotal": "最高小计:", "shipping-option-card-admin": "管理员", "shipping-option-card-store": "商店", "shipping-option-card-edit": "编辑", "shipping-option-card-delete": "删除", "shipping-option-form-visible-in-store": "在商店中可见", "shipping-option-form-enable-or-disable-the-shipping-option-visiblity-in-store": "在商店中启用或禁用运输选项的可见性。", "shipping-option-form-details": "详情", "shipping-option-form-title": "标题", "shipping-option-form-title-is-required": "标题是必需的", "shipping-option-form-price-type": "价格类型", "shipping-option-form-flat-rate": "固定费率", "shipping-option-form-calculated": "计算", "shipping-option-form-choose-a-price-type": "选择价格类型", "shipping-option-form-price": "价格", "shipping-option-form-shipping-profile": "运输配置文件", "shipping-option-form-choose-a-shipping-profile": "选择运输配置文件", "shipping-option-form-fulfillment-method": "履行方法", "shipping-option-form-choose-a-fulfillment-method": "选择履行方法", "shipping-option-form-requirements": "要求", "shipping-option-form-min-subtotal-must-be-less-than-max-subtotal": "最低小计必须小于最高小计", "shipping-option-form-min-subtotal": "最低小计", "shipping-option-form-max-subtotal": "最高小计", "shipping-option-form-metadata": "元数据", "general-section-success": "成功", "general-section-region-was-successfully-updated": "区域已成功更新", "general-section-error": "错误", "general-section-edit-region-details": "编辑区域详情", "general-section-details": "详情", "general-section-providers": "提供商", "general-section-metadata": "元数据", "general-section-cancel": "取消", "general-section-save-and-close": "保存并关闭", "edit-something-went-wrong": "出了些问题...", "edit-no-region-found": "我们找不到具有该ID的区域，请使用左侧的菜单选择区域。", "return-shipping-options-success": "成功", "return-shipping-options-shipping-option-created": "已创建运输选项", "return-shipping-options-error": "错误", "return-shipping-options-add-return-shipping-option": "添加退货运输选项", "return-shipping-options-cancel": "取消", "return-shipping-options-save-and-close": "保存并关闭", "return-shipping-options-return-shipping-options": "退货运输选项", "return-shipping-options-add-option": "添加选项", "return-shipping-options-enter-specifics-about-available-regional-return-shipment-methods": "输入有关可用区域退货运输方法的详细信息。", "shipping-options-success": "成功", "shipping-options-shipping-option-created": "已创建运输选项", "shipping-options-error": "错误", "shipping-options-add-shipping-option": "添加运输选项", "shipping-options-cancel": "取消", "shipping-options-save-and-close": "保存并关闭", "shipping-options-shipping-options": "运输选项", "shipping-options-add-option": "添加选项", "shipping-options-enter-specifics-about-available-regional-shipment-methods": "输入有关可用区域运输方法的详细信息。", "new-region-created": "已创建区域", "new-create-region": "创建区域", "new-details": "详情", "new-add-the-region-details": "添加区域详情。", "new-providers": "提供商", "new-add-which-fulfillment-and-payment-providers-should-be-available-in-this-region": "添加在该区域中应可用的履行和付款提供商。", "region-overview-regions": "区域", "region-overview-manage-the-markets-that-you-will-operate-within": "管理您将在其中运营的市场", "region-overview-not-configured": "未配置", "region-overview-fulfillment-providers": "履行提供商:", "return-reasons-notification-success": "成功", "return-reasons-created-a-new-return-reason": "创建了一个新的退货原因", "return-reasons-success": "成功", "return-reasons-error": "错误", "return-reasons-cannot-create-a-return-reason-with-an-existing-value": "无法使用现有值创建退货原因", "return-reasons-add-reason": "添加原因", "return-reasons-value-is-required": "需要值", "return-reasons-value": "值", "return-reasons-label-is-required": "需要标签", "return-reasons-label": "标签", "return-reasons-description": "描述", "return-reasons-customer-received-the-wrong-size": "顾客收到了错误的尺寸", "return-reasons-cancel": "取消", "return-reasons-create": "创建", "return-reasons-success-title": "成功", "return-reasons-successfully-updated-return-reason": "成功更新退货原因", "return-reasons-duplicate-reason": "重复的原因", "return-reasons-delete-reason": "删除原因", "return-reasons-save": "保存", "return-reasons-details": "详情", "return-reasons-delete-return-reason": "删除退货原因", "return-reasons-are-you-sure-you-want-to-delete-this-return-reason": "您确定要删除此退货原因吗？", "return-reasons-back-to-settings": "返回设置", "return-reasons-return-reasons": "退货原因", "return-reasons-add-reason-label": "添加原因", "return-reasons-manage-reasons-for-returned-items": "管理退货商品的原因", "taxes-details": "详情", "taxes-new-tax-rate": "新税率", "taxes-tax-calculation-settings": "税收计算设置", "taxes-success": "成功", "taxes-successfully-updated-tax-rate": "成功更新税率。", "taxes-error": "错误", "taxes-overrides": "覆盖", "taxes-product-rules": "产品规则", "taxes-product-rules-description_one": "适用于{{count}}个产品", "taxes-product-rules-description_other": "适用于{{count}}个产品", "taxes-product-type-rules": "产品类型规则", "taxes-product-type-rules-description_one": "适用于{{count}}个产品类型", "taxes-product-type-rules-description_other": "适用于{{count}}个产品类型", "taxes-shipping-option-rules": "运输选项规则", "taxes-applies-to-shipping-option-with-count_one": "适用于{{count}}个运输选项", "taxes-applies-to-shipping-option-with-count_other": "适用于{{count}}个运输选项", "taxes-add-overrides": "添加覆盖", "taxes-cancel": "取消", "taxes-save": "保存", "taxes-name": "名称", "taxes-default": "默认", "taxes-rate-name": "费率名称", "taxes-tax-rate": "税率", "taxes-tax-code": "税收代码", "taxes-edit-tax-rate": "编辑税率", "taxes-back-to-settings": "返回设置", "taxes-regions": "区域", "taxes-select-the-region-you-wish-to-manage-taxes-for": "选择要管理税收的区域", "taxes-go-to-region-settings": "转到区域设置", "taxes-successfully-created-tax-rate": "成功创建税率。", "taxes-add-tax-rate": "添加税率", "taxes-applies-to-product-type-with-count_one": "适用于{{count}}个产品类型", "taxes-applies-to-product-type-with-count_other": "适用于{{count}}个产品类型", "taxes-create": "创建", "taxes-select-products": "选择产品", "taxes-select-product-types-label": "选择产品类型", "taxes-product-types": "产品类型", "taxes-system-tax-provider": "系统税务提供商", "taxes-region-tax-settings-were-successfully-updated": "成功更新了区域税收设置。", "taxes-tax-provider": "税务提供商", "taxes-calculate-taxes-automatically": "是否自动计算税金？", "taxes-automatically-apply-tax-calculations-to-carts": "勾选后，系统将自动将税金计算应用于此区域的购物车。如果未勾选，您将需要在结账时手动计算税金。如果使用第三方税务提供商，建议手动计算税金以避免执行过多请求", "taxes-apply-tax-to-gift-cards": "将税金应用于礼品卡？", "taxes-apply-taxes-to-gift-cards": "勾选后，在结账时将向礼品卡应用税金。在某些国家/地区，税收法规要求在购买时向礼品卡应用税金。", "taxes-search-products": "搜索产品", "taxes-select-shipping-option": "选择运输选项", "taxes-shipping-options": "运输选项", "taxes-delete-tax-rate-heading": "删除税率", "taxes-confirm-delete": "您确定要删除此税率吗？", "taxes-tax-rate-was-deleted": "税率已删除", "taxes-edit": "编辑", "taxes-delete-tax-rate": "删除税率", "taxes-delete-rule": "删除规则", "taxes-type": "类型", "taxes-products": "产品", "taxes-select-individual-products": "选择个别产品", "taxes-select-product-types": "选择产品类型", "taxes-select-shipping-options": "选择运输选项", "taxes-back": "返回", "taxes-add": "添加", "taxes-code": "代码", "users-invite-users": "邀请用户", "users-back-to-settings": "返回设置", "users-the-team": "团队", "users-manage-users-of-your-store": "管理您商店的用户", "users-count_one": "{{count}}", "users-count_other": "{{count}}"}