{"back-button-go-back": "Go back", "filter-menu-trigger": "View", "filter-menu-clear-button": "Clear", "filter-menu-select-item-default-placeholder": "Select filter", "filter-menu-select-item-clear-button": "Clear the selected options", "filter-menu-select-item-selected": "Selected", "filter-menu-date-item-before": "Before", "filter-menu-date-item-after": "After", "filter-menu-date-item-between": "Between", "sales-channels-display-available-count": "Available in <2>{{availableChannelsCount}}</2> out of <6>{{totalChannelsCount}}</6> Sales Channels", "activity-drawer-activity": "Activity", "activity-drawer-no-notifications-title": "It's quiet in here...", "activity-drawer-no-notifications-description": "You don't have any notifications at the moment, but once you do they will live here.", "activity-drawer-error-title": "Oh no...", "activity-drawer-error-description": "Something went wrong while trying to fetch your notifications - We will keep trying!", "activity-drawer-processing": "Processing...", "analytics-config-form-title": "Anonymize my usage data", "analytics-config-form-description": "You can choose to anonymize your usage data. If this option is selected, we will not collect your personal information, such as your name and email address.", "analytics-config-form-opt-out": "Opt out of sharing my usage data", "analytics-config-form-opt-out-later": "You can always opt out of sharing your usage data at any time.", "analytics-preferences-success": "Success", "analytics-preferences-your-preferences-were-successfully-updated": "Your preferences were successfully updated", "analytics-preferences-error": "Error", "analytics-preferences-help-us-get-better": "Help us get better", "analytics-preferences-disclaimer": "To create the most compelling e-commerce experience we would like to gain insights in how you use Medusa. User insights allow us to build a better, more engaging, and more usable products. We only collect data for product improvements. Read what data we gather in our", "analytics-preferences-documentation": "documentation", "analytics-preferences-please-enter-a-valid-email": "Please enter a valid email", "analytics-preferences-continue": "Continue", "currency-input-currency": "<PERSON><PERSON><PERSON><PERSON>", "currency-input-amount-is-not-valid": "Amount is not valid", "organisms-success": "Success", "organisms-delete-successful": "Delete successful", "organisms-are-you-sure-you-want-to-delete": "Are you sure you want to delete?", "organisms-no-cancel": "No, cancel", "organisms-yes-remove": "Yes, remove", "details-collapsible-hide-additional-details": "Hide additional details", "details-collapsible-show-additional-details": "Show additional details", "edit-user-modal-success": "Success", "edit-user-modal-user-was-updated": "User was updated", "edit-user-modal-error": "Error", "edit-user-modal-edit-user": "Edit User", "edit-user-modal-first-name-label": "First Name", "edit-user-modal-first-name-placeholder": "First name...", "edit-user-modal-last-name-label": "Last Name", "edit-user-modal-last-name-placeholder": "Last name...", "edit-user-modal-email": "Email", "edit-user-modal-cancel": "Cancel", "edit-user-modal-save": "Save", "error-boundary-back-to-dashboard": "Back to dashboard", "error-boundary-an-unknown-error-occured": "An unknown error occured", "error-boundary-bad-request": "Bad request", "error-boundary-you-are-not-logged-in": "You are not logged in", "error-boundary-you-do-not-have-permission-perform-this-action": "You do not have permission to perform this action", "error-boundary-page-was-not-found": "Page was not found", "error-boundary-an-unknown-server-error-occured": "An unknown server error occured", "error-boundary-503": "Server is currently unavailable", "error-boundary-500": "An error occurred with unspecified causes, this is most likely due to a techinical issue on our end. Please try refreshing the page. If the issue keeps happening, contact your administrator.", "error-boundary-400": "The request was malformed, fix your request and please try again.", "error-boundary-401": "You are not logged in, please log in to proceed.", "error-boundary-403": "You do not have permission to perform this action, if you think this is a mistake, contact your administrator.", "error-boundary-404": "The page you have requested was not found, please check the URL and try again.", "error-boundary-500-2": "The server was not able to handle your request, this is mostly likely due to a techinical issue on our end. Please try again. If the issue keeps happening, contact your administrator.", "error-boundary-503-2": "The server is temporarily unavailable, and your request could not be processed. Please try again later. If the issue keeps happening, contact your administrator.", "export-modal-title": "Initialize an export of your data", "export-modal-cancel": "Cancel", "export-modal-export": "Export", "file-upload-modal-upload-a-new-photo": "Upload a new photo", "gift-card-banner-edit": "Edit", "gift-card-banner-unpublish": "Unpublish", "gift-card-banner-publish": "Publish", "gift-card-banner-delete": "Delete", "gift-card-banner-published": "Published", "gift-card-banner-unpublished": "Unpublished", "gift-card-denominations-section-denomination-added": "Denomination added", "gift-card-denominations-section-a-new-denomination-was-successfully-added": "A new denomination was successfully added", "gift-card-denominations-section-a-denomination-with-that-default-value-already-exists": "A denomination with that default value already exists", "gift-card-denominations-section-error": "Error", "gift-card-denominations-section-add-denomination": "Add Denomination", "gift-card-denominations-section-cancel": "Cancel", "gift-card-denominations-section-save-and-close": "Save and close", "gift-card-denominations-section-denomination-updated": "Denomination updated", "gift-card-denominations-section-a-new-denomination-was-successfully-updated": "A new denomination was successfully updated", "gift-card-denominations-section-edit-denomination": "Edit Denomination", "gift-card-denominations-section-denominations": "Denominations", "gift-card-denominations-section-denomination": "Denomination", "gift-card-denominations-section-in-other-currencies": "In other currencies", "gift-card-denominations-section-and-more_one": ", and {{count}} more", "gift-card-denominations-section-and-more_other": ", and {{count}} more", "gift-card-denominations-section-delete-denomination": "Delete denomination", "gift-card-denominations-section-confirm-delete": "Are you sure you want to delete this denomination?", "gift-card-denominations-section-denomination-deleted": "Denomination deleted", "gift-card-denominations-section-denomination-was-successfully-deleted": "Denomination was successfully deleted", "gift-card-denominations-section-edit": "Edit", "gift-card-denominations-section-delete": "Delete", "help-dialog-how-can-we-help": "How can we help?", "help-dialog-we-usually-respond-in-a-few-hours": "We usually respond in a few hours", "help-dialog-subject": "Subject", "help-dialog-what-is-it-about": "What is it about?...", "help-dialog-write-a-message": "Write a message...", "help-dialog-feel-free-to-join-our-community-of": "Feel free to join our community of", "help-dialog-merchants-and-e-commerce-developers": "merchants and e-commerce developers", "help-dialog-send-a-message": "Send a message", "invite-modal-success": "Success", "invite-modal-invitation-sent-to": "Invitation sent to {{user}}", "invite-modal-error": "Error", "invite-modal-member": "Member", "invite-modal-admin": "Admin", "invite-modal-developer": "Developer", "invite-modal-invite-users": "Invite Users", "invite-modal-email": "Email", "invite-modal-role": "Role", "invite-modal-select-role": "Select role", "invite-modal-cancel": "Cancel", "invite-modal-invite": "Invite", "login-card-no-match": "These credentials do not match our records.", "login-card-log-in-to-medusa": "Log in to Medusa", "login-card-email": "Email", "login-card-password": "Password", "login-card-forgot-your-password": "Forgot your password?", "metadata-add-metadata": "Add Metada<PERSON>", "product-attributes-section-edit-attributes": "Edit Attributes", "product-attributes-section-dimensions": "Dimensions", "product-attributes-section-configure-to-calculate-the-most-accurate-shipping-rates": "Configure to calculate the most accurate shipping rates", "product-attributes-section-customs": "Customs", "product-attributes-section-cancel": "Cancel", "product-attributes-section-save": "Save", "product-attributes-section-title": "Attributes", "product-attributes-section-height": "Height", "product-attributes-section-width": "<PERSON><PERSON><PERSON>", "product-attributes-section-length": "Length", "product-attributes-section-weight": "Weight", "product-attributes-section-mid-code": "MID Code", "product-attributes-section-hs-code": "HS Code", "product-attributes-section-country-of-origin": "Country of origin", "product-general-section-success": "Success", "product-general-section-successfully-updated-sales-channels": "Successfully updated sales channels", "product-general-section-error": "Error", "product-general-section-failed-to-update-sales-channels": "Failed to update sales channels", "product-general-section-edit-general-information": "Edit General Information", "product-general-section-gift-card": "Gift Card", "product-general-section-product": "Product", "product-general-section-metadata": "<PERSON><PERSON><PERSON>", "product-general-section-cancel": "Cancel", "product-general-section-save": "Save", "product-general-section-delete": "Delete", "product-general-section-edit-sales-channels": "Edit Sales Channels", "product-general-section-published": "Published", "product-general-section-draft": "Draft", "product-general-section-details": "Details", "product-general-section-subtitle": "Subtitle", "product-general-section-handle": "<PERSON><PERSON>", "product-general-section-type": "Type", "product-general-section-collection": "Collection", "product-general-section-category": "Category", "product-general-section-discountable": "Discountable", "product-general-section-true": "True", "product-general-section-false": "False", "product-general-section-count_one": "{{count}}", "product-general-section-count_other": "{{count}}", "product-general-section-sales-channels": "Sales channels", "product-media-section-edit-media": "Edit Media", "product-media-section-upload-images-error": "Something went wrong while trying to upload images.", "product-media-section-file-service-not-configured": "You might not have a file service configured. Please contact your administrator", "product-media-section-error": "Error", "product-media-section-media": "Media", "product-media-section-add-images-to-your-product": "Add images to your product.", "product-media-section-cancel": "Cancel", "product-media-section-save-and-close": "Save and close", "product-raw-section-raw-gift-card": "Raw Gift Card", "product-raw-section-raw-product": "Raw Product", "product-thumbnail-section-success": "Success", "product-thumbnail-section-successfully-deleted-thumbnail": "Successfully deleted thumbnail", "product-thumbnail-section-error": "Error", "product-thumbnail-section-edit": "Edit", "product-thumbnail-section-upload": "Upload", "product-thumbnail-section-upload-thumbnail-error": "Something went wrong while trying to upload the thumbnail.", "product-thumbnail-section-you-might-not-have-a-file-service-configured-please-contact-your-administrator": "You might not have a file service configured. Please contact your administrator", "product-thumbnail-section-upload-thumbnail": "Upload Thumbnail", "product-thumbnail-section-thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "product-thumbnail-section-used-to-represent-your-product-during-checkout-social-sharing-and-more": "Used to represent your product during checkout, social sharing and more.", "product-thumbnail-section-cancel": "Cancel", "product-thumbnail-section-save-and-close": "Save and close", "product-variant-tree-count_one": "{{count}}", "product-variant-tree-count_other": "{{count}}", "product-variant-tree-add-prices": "Add prices", "product-variants-section-add-variant": "<PERSON><PERSON>", "product-variants-section-cancel": "Cancel", "product-variants-section-save-and-close": "Save and close", "product-variants-section-edit-stock-inventory": "Edit stock & inventory", "product-variants-section-edit-variant": "<PERSON>", "edit-variants-modal-cancel": "Cancel", "edit-variants-modal-save-and-go-back": "Save and go back", "edit-variants-modal-save-and-close": "Save and close", "edit-variants-modal-edit-variant": "<PERSON>", "edit-variants-modal-update-success": "Variants were successfully updated", "edit-variants-modal-edit-variants": "<PERSON>", "edit-variants-modal-product-variants": "Product variants", "edit-variants-modal-variant": "<PERSON><PERSON><PERSON>", "edit-variants-modal-inventory": "Inventory", "product-variants-section-edit-prices": "Edit Prices", "product-variants-section-edit-variants": "<PERSON>", "product-variants-section-edit-options": "Edit Options", "product-variants-section-product-variants": "Product variants", "product-variants-section-error": "Error", "product-variants-section-failed-to-update-product-options": "Failed to update product options", "product-variants-section-success": "Success", "product-variants-section-successfully-updated-product-options": "Successfully updated product options", "product-variants-section-product-options": "Product options", "product-variants-section-option-title": "Option title", "product-variants-section-option-title-is-required": "Option title is required", "product-variants-section-add-an-option": "Add an option", "product-variants-section-inventory": "Inventory", "product-variants-section-title": "Title", "product-variants-section-sku": "SKU", "product-variants-section-ean": "EAN", "product-variants-section-manage-inventory": "Manage inventory", "product-variants-section-duplicate-variant": "Duplicate <PERSON>", "product-variants-section-delete-variant-label": "Delete Variant", "product-variants-section-yes-delete": "Yes, delete", "product-variants-section-delete-variant-heading": "Delete variant", "product-variants-section-confirm-delete": "Are you sure you want to delete this variant? ", "product-variants-section-note-deleting-the-variant-will-also-remove-inventory-items-and-levels": " Note: Deleting the variant will also remove inventory items and levels", "reset-token-card-error": "Error", "reset-token-card-reset-your-password": "Reset your password", "reset-token-card-password-reset-description": "Enter your email address below, and we'll<1></1>send you instructions on how to reset<3></3>your password.", "reset-token-card-email": "Email", "reset-token-card-this-is-not-a-valid-email": "This is not a valid email", "reset-token-card-send-reset-instructions": "Send reset instructions", "reset-token-card-successfully-sent-you-an-email": "Successfully sent you an email", "reset-token-card-go-back-to-sign-in": "Go back to sign in", "rma-return-product-table-product-details": "Product Details", "rma-return-product-table-quantity": "Quantity", "rma-select-product-table-product-details": "Product Details", "rma-select-product-table-quantity": "Quantity", "rma-select-product-table-refundable": "Refundable", "rma-select-product-table-images-witch-count_one": "{{count}}", "rma-select-product-table-images-witch-count_other": "{{count}}", "rma-select-product-table-select-reason": "Select Reason", "sidebar-store": "Store", "sidebar-orders": "Orders", "sidebar-products": "Products", "sidebar-categories": "Categories", "sidebar-customers": "Customers", "sidebar-inventory": "Inventory", "sidebar-discounts": "Discounts", "sidebar-gift-cards": "Gift Cards", "sidebar-pricing": "Pricing", "sidebar-settings": "Settings", "table-container-soothed-offset_one": "{{soothedOffset}} - {{pageSize}} of {{count}} {{title}}", "table-container-soothed-offset_other": "{{soothedOffset}} - {{pageSize}} of {{count}} {{title}}", "table-container-current-page": "{{currentPage}} of {{soothedPageCount}}", "timeline-request-return": "Request Return", "timeline-register-exchange": "Register Exchange", "timeline-register-claim": "Register Claim", "timeline-success": "Success", "timeline-added-note": "Added note", "timeline-error": "Error", "timeline-timeline": "Timeline", "upload-modal-new": "new", "upload-modal-updates": "updates", "upload-modal-drop-your-file-here-or": "Drop your file here, or", "upload-modal-click-to-browse": "click to browse.", "upload-modal-only-csv-files-are-supported": "Only .csv files are supported.", "upload-modal-import-file-title": "Import {{fileTitle}}", "upload-modal-cancel": "Cancel", "upload-modal-import-list": "Import List", "add-products-modal-add-products": "Add Products", "add-products-modal-search-by-name-or-description": "Search by name or description...", "add-products-modal-cancel": "Cancel", "add-products-modal-save": "Save", "add-products-modal-product-details": "Product Details", "add-products-modal-status": "Status", "add-products-modal-variants": "Variants", "templates-general": "General", "templates-first-name": "First Name", "templates-last-name": "Last Name", "templates-company": "Company", "templates-phone": "Phone", "templates-billing-address": "Billing Address", "templates-shipping-address": "Shipping Address", "templates-address": "Address", "templates-address-1": "Address 1", "templates-address-2": "Address 2", "templates-postal-code": "Postal code", "templates-city": "City", "templates-province": "Province", "templates-country": "Country", "templates-metadata": "<PERSON><PERSON><PERSON>", "collection-modal-success": "Success", "collection-modal-successfully-updated-collection": "Successfully updated collection", "collection-modal-error": "Error", "collection-modal-successfully-created-collection": "Successfully created collection", "collection-modal-edit-collection": "Edit Collection", "collection-modal-add-collection": "Add Collection", "collection-modal-description": "To create a collection, all you need is a title and a handle.", "collection-modal-details": "Details", "collection-modal-title-label": "Title", "collection-modal-title-placeholder": "Sunglasses", "collection-modal-handle-label": "<PERSON><PERSON>", "collection-modal-handle-placeholder": "sunglasses", "collection-modal-slug-description": "URL Slug for the collection. Will be auto generated if left blank.", "collection-modal-metadata": "<PERSON><PERSON><PERSON>", "collection-modal-cancel": "Cancel", "collection-modal-save-collection": "Save collection", "collection-modal-publish-collection": "Publish collection", "collection-product-table-add-products": "Add Products", "collection-product-table-products": "Products", "collection-product-table-search-products": "Search Products", "collection-product-table-cancel": "Cancel", "collection-product-table-save": "Save", "collection-product-table-sort-by": "Sort by", "collection-product-table-all": "All", "collection-product-table-newest": "Newest", "collection-product-table-oldest": "Oldest", "collection-product-table-title": "Title", "collection-product-table-decide-status-published": "Published", "collection-product-table-draft": "Draft", "collection-product-table-proposed": "Proposed", "collection-product-table-rejected": "Rejected", "collection-product-table-remove-product-from-collection": "Remove product from collection", "collection-product-table-product-removed-from-collection": "Product removed from collection", "collections-table-delete-collection": "Delete Collection", "collections-table-confirm-delete": "Are you sure you want to delete this collection?", "collections-table-edit": "Edit", "collections-table-delete": "Delete", "collections-table-title": "Title", "collections-table-handle": "<PERSON><PERSON>", "collections-table-created-at": "Created At", "collections-table-updated-at": "Updated At", "collections-table-products": "Products", "customer-group-table-details": "Details", "customer-group-table-delete": "Delete", "customer-group-table-success": "Success", "customer-group-table-group-deleted": "Group deleted", "customer-group-table-error": "Error", "customer-group-table-failed-to-delete-the-group": "Failed to delete the group", "customer-group-table-customer-groups": "Customer groups", "customer-group-table-delete-from-the-group": "Delete from the group", "customer-group-table-customer-groups-title": "Customer Groups", "customer-group-table-groups": "Groups", "customer-group-table-all": "All", "customer-group-table-edit-customers": "Edit Customers", "customer-group-table-customers": "Customers", "customer-group-table-cancel": "Cancel", "customer-group-table-save": "Save", "customer-orders-table-orders": "Orders", "customer-orders-table-transfer-order": "Transfer order", "customer-orders-table-paid": "Paid", "customer-orders-table-awaiting": "Awaiting", "customer-orders-table-requires-action": "Requires action", "customer-orders-table-n-a": "N/A", "customer-orders-table-fulfilled": "Fulfilled", "customer-orders-table-shipped": "Shipped", "customer-orders-table-not-fulfilled": "Not fulfilled", "customer-orders-table-partially-fulfilled": "Partially fulfilled", "customer-orders-table-partially-shipped": "Partially shipped", "customer-orders-table-order": "Order", "customer-orders-table-remainder-more": "+ {{remainder}} more", "customer-orders-table-date": "Date", "customer-orders-table-fulfillment": "Fulfillment", "customer-orders-table-status": "Status", "customer-orders-table-total": "Total", "customer-table-customers": "Customers", "customer-table-edit": "Edit", "customer-table-details": "Details", "customer-table-date-added": "Date added", "customer-table-name": "Name", "customer-table-email": "Email", "customer-table-orders": "Orders", "discount-filter-dropdown-filters": "Filters", "discount-table-discounts": "Discounts", "discount-table-search-by-code-or-description": "Search by code or description...", "discount-table-success": "Success", "discount-table-successfully-copied-discount": "Successfully copied discount", "discount-table-error": "Error", "discount-table-scheduled": "Scheduled", "discount-table-expired": "Expired", "discount-table-active": "Active", "discount-table-disabled": "Disabled", "discount-table-free-shipping": "Free Shipping", "discount-table-code": "Code", "discount-table-description": "Description", "discount-table-amount": "Amount", "discount-table-status": "Status", "discount-table-redemptions": "Redemptions", "discount-table-delete-discount": "Delete Discount", "discount-table-confirm-delete": "Are you sure you want to delete this Discount?", "discount-table-publish": "Publish", "discount-table-unpublish": "Unpublish", "discount-table-successfully-published-discount": "Successfully published discount", "discount-table-successfully-unpublished-discount": "Successfully unpublished discount", "discount-table-duplicate": "Duplicate", "discount-table-delete": "Delete", "draft-order-table-draft-orders": "Draft Orders", "draft-order-table-completed": "Completed", "draft-order-table-open": "Open", "draft-order-table-draft": "Draft", "draft-order-table-order": "Order", "draft-order-table-date-added": "Date added", "draft-order-table-customer": "Customer", "draft-order-table-status": "Status", "gift-card-filter-dropdown-is-in-the-last": "is in the last", "gift-card-filter-dropdown-is-older-than": "is older than", "gift-card-filter-dropdown-is-after": "is after", "gift-card-filter-dropdown-is-before": "is before", "gift-card-filter-dropdown-is-equal-to": "is equal to", "gift-card-filter-dropdown-filters": "Filters", "gift-card-filter-dropdown-status": "Status", "gift-card-filter-dropdown-payment-status": "Payment Status", "gift-card-filter-dropdown-fulfillment-status": "Fulfillment Status", "gift-card-filter-dropdown-date": "Date", "gift-card-table-gift-cards": "Gift cards", "gift-card-table-code": "Code", "gift-card-table-order": "Order", "gift-card-table-original-amount": "Original Amount", "gift-card-table-balance": "Balance", "gift-card-table-region-has-been-deleted": "Region has been deleted", "gift-card-table-none": "None", "gift-card-table-created": "Created", "image-table-file-name": "File name", "image-table-thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "image-table-select-thumbnail-image-for-product": "Select which image you want to use as the thumbnail for this product", "inventory-table-inventory-items": "Inventory Items", "inventory-table-actions-adjust-availability": "Adjust Availability", "inventory-table-view-product": "View Product", "inventory-table-success": "Success", "inventory-table-inventory-item-updated-successfully": "Inventory item updated successfully", "inventory-table-adjust-availability": "Adjust availability", "inventory-table-cancel": "Cancel", "inventory-table-save-and-close": "Save and close", "inventory-table-item": "<PERSON><PERSON>", "inventory-table-variant": "<PERSON><PERSON><PERSON>", "inventory-table-sku": "SKU", "inventory-table-reserved": "Reserved", "inventory-table-in-stock": "In stock", "order-filter-dropdown-filters": "Filters", "order-filter-dropdown-status": "Status", "order-filter-dropdown-payment-status": "Payment Status", "order-filter-dropdown-fulfillment-status": "Fulfillment Status", "order-filter-dropdown-regions": "Regions", "order-filter-dropdown-sales-channel": "Sales Channel", "order-filter-dropdown-date": "Date", "order-table-paid": "Paid", "order-table-awaiting": "Awaiting", "order-table-requires-action": "Requires action", "order-table-canceled": "Canceled", "order-table-n-a": "N/A", "order-table-order": "Order", "order-table-date-added": "Date added", "order-table-customer": "Customer", "order-table-fulfillment": "Fulfillment", "order-table-payment-status": "Payment status", "order-table-sales-channel": "Sales Channel", "order-table-total": "Total", "order-table-filters-complete": "Complete", "order-table-filters-incomplete": "Incomplete", "price-list-table-filters": "Filters", "price-list-table-status": "Status", "price-list-table-type": "Type", "price-list-table-price-lists": "Price Lists", "price-list-table-success": "Success", "price-list-table-successfully-copied-price-list": "Successfully copied price list", "price-list-table-error": "Error", "price-list-table-delete-price-list": "Delete Price List", "price-list-table-confirm-delete": "Are you sure you want to delete this price list?", "price-list-table-successfully-deleted-the-price-list": "Successfully deleted the price list", "price-list-table-successfully-unpublished-price-list": "Successfully unpublished price list", "price-list-table-successfully-published-price-list": "Successfully published price list", "price-list-table-unpublish": "Unpublish", "price-list-table-publish": "Publish", "price-list-table-delete": "Delete", "price-list-table-name": "Name", "price-list-table-description": "Description", "price-list-table-groups": "Groups", "price-list-table-other-more": "+ {{other}} more", "price-overrides-apply-overrides-on-selected-variants": "Apply overrides on selected variants", "price-overrides-apply-on-all-variants": "Apply on all variants", "price-overrides-prices": "Prices", "price-overrides-cancel": "Cancel", "price-overrides-save-and-close": "Save and close", "price-overrides-show-regions": "Show regions", "product-table-products": "Products", "product-table-copy-success": "Success", "product-table-copy-created-a-new-product": "Created a new product", "product-table-copy-error": "Error", "product-table-delete-product": "Delete Product", "product-table-confirm-delete": "Are you sure you want to delete this product?", "product-table-edit": "Edit", "product-table-unpublish": "Unpublish", "product-table-publish": "Publish", "product-table-draft": "draft", "product-table-published": "published", "product-table-success": "Success", "product-table-successfully-unpublished-product": "Successfully unpublished product", "product-table-successfully-published-product": "Successfully published product", "product-table-error": "Error", "product-table-duplicate": "Duplicate", "product-table-delete": "Delete", "product-table-proposed": "Proposed", "product-table-published-title": "Published", "product-table-rejected": "Rejected", "product-table-draft-title": "Draft", "product-table-name": "Name", "product-table-collection": "Collection", "product-table-status": "Status", "product-table-availability": "Availability", "product-table-inventory": "Inventory", "product-table-inventory-in-stock-count_one": " in stock for {{count}} variant(s)", "product-table-inventory-in-stock-count_other": " in stock for {{count}} variant(s)", "reservation-form-location": "Location", "reservation-form-choose-where-you-wish-to-reserve-from": "Choose where you wish to reserve from.", "reservation-form-item-to-reserve": "Item to reserve", "reservation-form-select-the-item-that-you-wish-to-reserve": "Select the item that you wish to reserve.", "reservation-form-item": "<PERSON><PERSON>", "reservation-form-in-stock": "In stock", "reservation-form-available": "Available", "reservation-form-reserve": "Reserve", "reservation-form-remove-item": "Remove item", "reservation-form-description": "Description", "reservation-form-what-type-of-reservation-is-this": "What type of reservation is this?", "reservations-table-reservations": "Reservations", "reservations-table-edit": "Edit", "reservations-table-delete": "Delete", "reservations-table-confirm-delete": "Are you sure you want to remove this reservation?", "reservations-table-remove-reservation": "Remove reservation", "reservations-table-reservation-has-been-removed": "Reservation has been removed", "new-success": "Success", "new-successfully-created-reservation": "Successfully created reservation", "new-error": "Error", "new-cancel": "Cancel", "new-save-reservation": "Save reservation", "new-reserve-item": "Reserve Item", "new-metadata": "<PERSON><PERSON><PERSON>", "reservations-table-order-id": "Order ID", "reservations-table-description": "Description", "reservations-table-created": "Created", "reservations-table-quantity": "Quantity", "search-modal-start-typing-to-search": "Start typing to search...", "search-modal-clear-search": "Clear search", "search-modal-or": "or", "search-modal-to-navigate": "to navigate", "search-modal-to-select-and": "to select, and", "search-modal-to-search-anytime": "to search anytime", "templates-settings": "Settings", "templates-manage-the-settings-for-your-medusa-store": "Manage the settings for your Medusa store", "transfer-orders-modal-info": "Info", "transfer-orders-modal-customer-is-already-the-owner-of-the-order": "Customer is already the owner of the order", "transfer-orders-modal-success": "Success", "transfer-orders-modal-successfully-transferred-order-to-different-customer": "Successfully transferred order to different customer", "transfer-orders-modal-error": "Error", "transfer-orders-modal-could-not-transfer-order-to-different-customer": "Could not transfer order to different customer", "transfer-orders-modal-transfer-order": "Transfer order", "transfer-orders-modal-order": "Order", "transfer-orders-modal-current-owner": "Current Owner", "transfer-orders-modal-the-customer-currently-related-to-this-order": "The customer currently related to this order", "transfer-orders-modal-new-owner": "New Owner", "transfer-orders-modal-the-customer-to-transfer-this-order-to": "The customer to transfer this order to", "transfer-orders-modal-cancel": "Cancel", "transfer-orders-modal-confirm": "Confirm", "templates-edit-user": "Edit User", "templates-remove-user": "Remove User", "templates-resend-invitation": "Resend Invitation", "templates-success": "Success", "templates-invitiation-link-has-been-resent": "Invitiation link has been resent", "templates-copy-invite-link": "Copy invite link", "templates-invite-link-copied-to-clipboard": "Invite link copied to clipboard", "templates-remove-invitation": "Remove Invitation", "templates-expired": "Expired", "templates-pending": "Pending", "templates-all": "All", "templates-member": "Member", "templates-admin": "Admin", "templates-no-team-permissions": "No team permissions", "templates-status": "Status", "templates-active": "Active", "templates-name": "Name", "templates-email": "Email", "templates-team-permissions": "Team permissions", "templates-confirm-remove": "Are you sure you want to remove this user?", "templates-remove-user-heading": "Remove user", "templates-user-has-been-removed": "User has been removed", "templates-confirm-remove-invite": "Are you sure you want to remove this invite?", "templates-remove-invite": "Remove invite", "templates-invitiation-has-been-removed": "Invitiation has been removed", "multiselect-choose-categories": "Choose categories", "domain-categories-multiselect-selected-with-counts_one": "{{count}}", "domain-categories-multiselect-selected-with-counts_other": "{{count}}", "details-success": "Success", "details-updated-products-in-collection": "Updated products in collection", "details-error": "Error", "details-back-to-collections": "Back to Collections", "details-edit-collection": "Edit Collection", "details-delete": "Delete", "details-metadata": "<PERSON><PERSON><PERSON>", "details-edit-products": "Edit Products", "details-products-in-this-collection": "Products in this collection", "details-raw-collection": "Raw collection", "details-delete-collection": "Delete collection", "details-successfully-deleted-collection": "Successfully deleted collection", "details-yes-delete": "Yes, delete", "details-successfully-updated-customer": "Successfully updated customer", "details-customer-details": "Customer Details", "details-general": "General", "details-first-name": "First Name", "details-lebron": "Lebron", "details-last-name": "Last Name", "details-james": "<PERSON>", "details-email": "Email", "details-phone-number": "Phone number", "details-cancel": "Cancel", "details-save-and-close": "Save and close", "details-edit": "Edit", "details-back-to-customers": "Back to Customers", "details-first-seen": "First seen", "details-phone": "Phone", "details-orders": "Orders", "details-user": "User", "details-orders_one": "Orders {{count}}", "details-orders_other": "Orders {{count}}", "details-an-overview-of-customer-orders": "An overview of Customer Orders", "details-raw-customer": "Raw customer", "groups-group-updated": "Group Updated", "groups-group-created": "Group Created", "groups-the-customer-group-has-been-updated": "The customer group has been updated", "groups-the-customer-group-has-been-created": "The customer group has been created", "groups-edit-customer-group": "Edit Customer Group", "groups-create-a-new-customer-group": "Create a New Customer Group", "groups-details": "Details", "groups-metadata": "<PERSON><PERSON><PERSON>", "groups-cancel": "Cancel", "groups-edit-group": "Edit Group", "groups-publish-group": "Publish Group", "groups-no-customers-in-this-group-yet": "No customers in this group yet", "groups-customers": "Customers", "groups-edit": "Edit", "groups-delete": "Delete", "groups-yes-delete": "Yes, delete", "groups-delete-the-group": "Delete the group", "groups-group-deleted": "Group deleted", "groups-confirm-delete-customer-group": "Are you sure you want to delete this customer group?", "groups-back-to-customer-groups": "Back to customer groups", "groups-new-group": "New group", "add-condition-conditions-were-successfully-added": "Conditions were successfully added", "add-condition-discount-conditions-updated": "Discount conditions updated", "add-condition-use-conditions-must-be-used-within-a-conditions-provider": "useConditions must be used within a ConditionsProvider", "collections-search": "Search...", "collections-cancel": "Cancel", "collections-save-and-go-back": "Save and go back", "collections-save-and-close": "Save and close", "customer-groups-search": "Search...", "customer-groups-cancel": "Cancel", "customer-groups-save-and-go-back": "Save and go back", "customer-groups-save-and-close": "Save and close", "product-types-search": "Search...", "product-types-cancel": "Cancel", "product-types-save-and-go-back": "Save and go back", "product-types-save-and-close": "Save and close", "products-search": "Search...", "products-cancel": "Cancel", "products-save-and-go-back": "Save and go back", "products-save-and-close": "Save and close", "tags-search": "Search...", "tags-cancel": "Cancel", "tags-save-and-go-back": "Save and go back", "tags-save-and-close": "Save and close", "edit-condition-add-conditions": "Add conditions", "edit-condition-selected-with-count_one": "{{count}}", "edit-condition-selected-with-count_other": "{{count}}", "edit-condition-deselect": "Deselect", "edit-condition-remove": "Remove", "edit-condition-add": "Add", "edit-condition-title": "Edit {{type}} in Discount Condition", "edit-condition-close": "Close", "edit-condition-success": "Success", "edit-condition-the-resources-were-successfully-added": "The resources were successfully added", "edit-condition-error": "Error", "edit-condition-failed-to-add-resources": "Failed to add resources", "edit-condition-the-resources-were-successfully-removed": "The resources were successfully removed", "edit-condition-failed-to-remove-resources": "Failed to remove resources", "edit-condition-use-edit-condition-context-must-be-used-within-an-edit-condition-provider": "useEditConditionContext must be used within an EditConditionProvider", "conditions-conditions": "Conditions", "conditions-add-condition-label": "Add condition", "conditions-this-discount-has-no-conditions": "This discount has no conditions", "conditions-success": "Success", "conditions-condition-removed": "Condition removed", "conditions-error": "Error", "conditions-edit-condition": "Edit condition", "conditions-delete-condition": "Delete condition", "conditions-discount-is-applicable-to-specific-products": "Discount is applicable to specific products", "conditions-discount-is-applicable-to-specific-collections": "Discount is applicable to specific collections", "conditions-discount-is-applicable-to-specific-product-tags": "Discount is applicable to specific product tags", "conditions-discount-is-applicable-to-specific-product-types": "Discount is applicable to specific product types", "conditions-discount-is-applicable-to-specific-customer-groups": "Discount is applicable to specific customer groups", "configurations-success": "Success", "configurations-discount-updated-successfully": "Discount updated successfully", "configurations-error": "Error", "configurations-edit-configurations": "Edit configurations", "configurations-cancel": "Cancel", "configurations-save": "Save", "configurations-configurations": "Configurations", "configurations-start-date": "Start date", "configurations-end-date": "End date", "configurations-delete-configuration": "Delete configuration", "configurations-discount-end-date-removed": "Discount end date removed", "configurations-number-of-redemptions": "Number of redemptions", "configurations-redemption-limit-removed": "Redemption limit removed", "configurations-delete-setting": "Delete setting", "configurations-discount-duration-removed": "Discount duration removed", "general-success": "Success", "general-discount-updated-successfully": "Discount updated successfully", "general-error": "Error", "general-edit-general-information": "Edit general information", "general-details": "Details", "general-metadata": "<PERSON><PERSON><PERSON>", "general-cancel": "Cancel", "general-save-and-close": "Save and close", "general-delete-promotion": "Delete Promotion", "general-confirm-delete-promotion": "Are you sure you want to delete this promotion?", "general-promotion-deleted-successfully": "Promotion deleted successfully", "general-discount-published-successfully": "Discount published successfully", "general-discount-drafted-successfully": "Discount drafted successfully", "general-delete-discount": "Delete discount", "general-template-discount": "Template discount", "general-published": "Published", "general-draft": "Draft", "general-discount-amount": "Discount Amount", "general-valid-regions": "Valid Regions", "general-total-redemptions": "Total Redemptions", "general-free-shipping": "FREE SHIPPING", "general-unknown-discount-type": "Unknown discount type", "details-discount-deleted": "Discount deleted", "details-confirm-delete-discount": "Are you sure you want to delete this discount?", "details-delete-discount": "Delete discount", "details-back-to-discounts": "Back to Discounts", "details-raw-discount": "Raw discount", "discounts-add-discount": "Add Discount", "discount-form-add-conditions": "Add Conditions", "discount-form-choose-a-condition-type": "Choose a condition type", "discount-form-you-can-only-add-one-of-each-type-of-condition": "You can only add one of each type of condition", "discount-form-you-cannot-add-any-more-conditions": "You cannot add any more conditions", "discount-form-cancel": "Cancel", "discount-form-save": "Save", "add-condition-tables-cancel": "Cancel", "add-condition-tables-save-and-add-more": "Save and add more", "add-condition-tables-save-and-close": "Save and close", "add-condition-tables-search-by-title": "Search by title...", "add-condition-tables-search-groups": "Search groups...", "add-condition-tables-search-products": "Search products...", "add-condition-tables-search-by-tag": "Search by tag...", "add-condition-tables-search-by-type": "Search by type...", "details-condition-tables-search-by-title": "Search by title...", "details-condition-tables-search-groups": "Search groups...", "details-condition-tables-cancel": "Cancel", "details-condition-tables-save-and-add-more": "Save and add more", "details-condition-tables-save-and-close": "Save and close", "details-condition-tables-search-products": "Search products...", "details-condition-tables-search-by-tag": "Search by tag...", "details-condition-tables-search-by-type": "Search by type...", "edit-condition-tables-search-by-title": "Search by title...", "edit-condition-tables-title": "Title", "edit-condition-tables-search-groups": "Search groups...", "edit-condition-tables-cancel": "Cancel", "edit-condition-tables-delete-condition": "Delete condition", "edit-condition-tables-save": "Save", "edit-condition-tables-search-products": "Search products...", "edit-condition-tables-search-by-tag": "Search by tag...", "edit-condition-tables-search-by-type": "Search by type...", "shared-title": "Title", "shared-products": "Products", "shared-applies-to-the-selected-items": "Applies to the selected items.", "shared-applies-to-all-items-except-the-selected-items": "Applies to all items except the selected items.", "shared-members": "Members", "shared-status": "Status", "shared-variants": "Variants", "shared-tag": "Tag", "shared-type": "Type", "edit-conditions-modal-title": "Edit {{title}}", "form-use-discount-form-must-be-a-child-of-discount-form-context": "useDiscount<PERSON><PERSON> must be a child of DiscountFormContext", "discount-form-error": "Error", "discount-form-save-as-draft": "Save as draft", "discount-form-publish-discount": "Publish discount", "discount-form-create-new-discount": "Create new discount", "discount-form-discount-type": "Discount type", "discount-form-select-a-discount-type": "Select a discount type", "discount-form-allocation": "Allocation", "discount-form-general": "General", "discount-form-configuration": "Configuration", "discount-form-discount-code-application-disclaimer": "Discount code applies from when you hit the publish button and forever if left untouched.", "discount-form-conditions": "Conditions", "discount-form-discount-code-apply-to-all-products-if-left-untouched": "Discount code apply to all products if left untouched.", "discount-form-add-conditions-to-your-discount": "Add conditions to your Discount", "discount-form-metadata": "<PERSON><PERSON><PERSON>", "discount-form-metadata-usage-description": "Metadata allows you to add additional information to your discount.", "condition-item-remainder-more": "+{{remainder}} more", "conditions-edit": "Edit", "conditions-product": "Product", "conditions-collection": "Collection", "conditions-tag": "Tag", "conditions-customer-group": "Customer group", "conditions-type": "Type", "conditions-add-condition": "Add Condition", "sections-start-date": "Start date", "sections-schedule-the-discount-to-activate-in-the-future": "Schedule the discount to activate in the future.", "sections-select-discount-start-date": "If you want to schedule the discount to activate in the future, you can set a start date here, otherwise the discount will be active immediately.", "sections-start-time": "Start time", "sections-discount-has-an-expiry-date": "Discount has an expiry date?", "sections-schedule-the-discount-to-deactivate-in-the-future": "Schedule the discount to deactivate in the future.", "sections-select-discount-end-date": "If you want to schedule the discount to deactivate in the future, you can set an expiry date here.", "sections-expiry-date": "Expiry date", "sections-expiry-time": "Expiry time", "sections-limit-the-number-of-redemptions": "Limit the number of redemptions?", "sections-limit-applies-across-all-customers-not-per-customer": "Limit applies across all customers, not per customer.", "sections-limit-discount-number-of-uses": "If you wish to limit the amount of times a customer can redeem this discount, you can set a limit here.", "sections-number-of-redemptions": "Number of redemptions", "sections-availability-duration": "Availability duration?", "sections-set-the-duration-of-the-discount": "Set the duration of the discount.", "sections-select-a-discount-type": "Select a discount type", "sections-total-amount": "Total amount", "sections-apply-to-the-total-amount": "Apply to the total amount", "sections-item-specific": "Item specific", "sections-apply-to-every-allowed-item": "Apply to every allowed item", "sections-percentage": "Percentage", "sections-fixed-amount": "Fixed amount", "sections-discount-in-whole-numbers": "Discount in whole numbers", "sections-you-can-only-select-one-valid-region-if-you-want-to-use-the-fixed-amount-type": "You can only select one valid region if you want to use the fixed amount type", "sections-free-shipping": "Free shipping", "sections-override-delivery-amount": "Override delivery amount", "sections-at-least-one-region-is-required": "At least one region is required", "sections-choose-valid-regions": "Choose valid regions", "sections-code": "Code", "sections-summersale-10": "SUMMERSALE10", "sections-code-is-required": "Code is required", "sections-amount-is-required": "Amount is required", "sections-amount": "Amount", "sections-customer-invoice-code": "The code your customers will enter during checkout. This will appear on your customer’s invoice.", "sections-uppercase-letters-and-numbers-only": "Uppercase letters and numbers only.", "sections-description": "Description", "sections-summer-sale-2022": "Summer Sale 2022", "sections-this-is-a-template-discount": "This is a template discount", "sections-template-discounts-description": "Template discounts allow you to define a set of rules that can be used across a group of discounts. This is useful in campaigns that should generate unique codes for each user, but where the rules for all unique codes should be the same.", "discount-form-product": "Product", "discount-form-only-for-specific-products": "Only for specific products", "discount-form-choose-products": "Choose products", "discount-form-customer-group": "Customer group", "discount-form-only-for-specific-customer-groups": "Only for specific customer groups", "discount-form-choose-groups": "Choose groups", "discount-form-tag": "Tag", "discount-form-only-for-specific-tags": "Only for specific tags", "discount-form-collection": "Collection", "discount-form-only-for-specific-product-collections": "Only for specific product collections", "discount-form-choose-collections": "Choose collections", "discount-form-type": "Type", "discount-form-only-for-specific-product-types": "Only for specific product types", "discount-form-choose-types": "Choose types", "utils-products": "products", "utils-groups": "groups", "utils-tags": "tags", "utils-collections": "collections", "utils-types": "types", "gift-cards-created-gift-card": "Created gift card", "gift-cards-custom-gift-card-was-created-successfully": "Custom gift card was created successfully", "gift-cards-error": "Error", "gift-cards-custom-gift-card": "Custom Gift Card", "gift-cards-details": "Details", "gift-cards-receiver": "Receiver", "gift-cards-cancel": "Cancel", "gift-cards-create-and-send": "Create and send", "details-updated-gift-card": "Updated Gift card", "details-gift-card-was-successfully-updated": "Gift card was successfully updated", "details-failed-to-update-gift-card": "Failed to update Gift card", "details-edit-gift-card": "Edit Gift Card", "details-details": "Details", "details-edit-details": "Edit details", "details-update-balance-label": "Update balance", "details-updated-status": "Updated status", "details-successfully-updated-the-status-of-the-gift-card": "Successfully updated the status of the Gift Card", "details-back-to-gift-cards": "Back to Gift Cards", "details-original-amount": "Original amount", "details-balance": "Balance", "details-region": "Region", "details-expires-on": "Expires on", "details-created": "Created", "details-raw-gift-card": "Raw gift card", "details-balance-updated": "Balance updated", "details-gift-card-balance-was-updated": "Gift card balance was updated", "details-failed-to-update-balance": "Failed to update balance", "details-update-balance": "Update Balance", "manage-back-to-gift-cards": "Back to Gift Cards", "gift-cards-please-enter-a-name-for-the-gift-card": "Please enter a name for the Gift Card", "gift-cards-please-add-at-least-one-denomination": "Please add at least one denomination", "gift-cards-denominations": "Denominations", "gift-cards-success": "Success", "gift-cards-successfully-created-gift-card": "Successfully created Gift Card", "gift-cards-create-gift-card": "Create Gift Card", "gift-cards-gift-card-details": "Gift Card Details", "gift-cards-name": "Name", "gift-cards-the-best-gift-card": "The best Gift Card", "gift-cards-description": "Description", "gift-cards-the-best-gift-card-of-all-time": "The best Gift Card of all time", "gift-cards-thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "gift-cards-delete": "Delete", "gift-cards-size-recommended": "1200 x 1600 (3:4) recommended, up to 10MB each", "gift-cards-amount": "Amount", "gift-cards-add-denomination": "Add Denomination", "gift-cards-create-publish": "Create & Publish", "gift-cards-successfully-updated-gift-card": "Successfully updated Gift Card", "gift-cards-gift-cards": "Gift Cards", "gift-cards-manage": "Manage the Gift Cards of your Medusa store", "gift-cards-are-you-ready-to-sell-your-first-gift-card": "Are you ready to sell your first Gift Card?", "gift-cards-no-gift-card-has-been-added-yet": "No Gift Card has been added yet.", "gift-cards-history": "History", "gift-cards-see-the-history-of-purchased-gift-cards": "See the history of purchased Gift Cards", "gift-cards-successfully-deleted-gift-card": "Successfully deleted Gift Card", "gift-cards-yes-delete": "Yes, delete", "gift-cards-delete-gift-card": "Delete Gift Card", "inventory-filters": "Filters", "address-form-address": "Address", "address-form-company": "Company", "address-form-address-1": "Address 1", "address-form-this-field-is-required": "This field is required", "address-form-address-2": "Address 2", "address-form-postal-code": "Postal code", "address-form-city": "City", "address-form-country": "Country", "edit-sales-channels-edit-channels": "Edit channels", "edit-sales-channels-add-channels": "Add channels", "general-form-location-name": "Location name", "general-form-flagship-store-warehouse": "Flagship store, warehouse", "general-form-name-is-required": "Name is required", "location-card-delete-location": "Delete Location", "location-card-confirm-delete": "Are you sure you want to delete this location. This will also delete all inventory levels and reservations associated with this location.", "location-card-success": "Success", "location-card-location-deleted-successfully": "Location deleted successfully", "location-card-error": "Error", "location-card-edit-details": "Edit details", "location-card-delete": "Delete", "location-card-connected-sales-channels": "Connected sales channels", "sales-channels-form-add-sales-channels": "Add sales channels", "sales-channels-form-edit-channels": "Edit channels", "sales-channels-section-not-connected-to-any-sales-channels-yet": "Not connected to any sales channels yet", "edit-success": "Success", "edit-location-edited-successfully": "Location edited successfully", "edit-error": "Error", "edit-edit-location-details": "Edit Location Details", "edit-metadata": "<PERSON><PERSON><PERSON>", "edit-cancel": "Cancel", "edit-save-and-close": "Save and close", "new-location-added-successfully": "Location added successfully", "new-location-created": "Location was created successfully, but there was an error associating sales channels", "new-cancel-location-changes": "Are you sure you want to cancel with unsaved changes", "new-yes-cancel": "Yes, cancel", "new-no-continue-creating": "No, continue creating", "new-add-location": "Add location", "new-add-new-location": "Add new location", "new-general-information": "General Information", "new-location-details": "Specify the details about this location", "new-select-location-channel": "Specify which Sales Channels this location's items can be purchased through.", "oauth-complete-installation": "Complete Installation", "claim-type-form-refund": "Refund", "claim-type-form-replace": "Replace", "items-to-receive-form-items-to-receive": "Items to receive", "items-to-receive-form-product": "Product", "items-to-receive-form-quantity": "Quantity", "items-to-receive-form-refundable": "Refundable", "add-return-reason-reason-for-return": "Reason for Return", "add-return-reason-reason": "Reason", "add-return-reason-choose-a-return-reason": "Choose a return reason", "add-return-reason-note": "Note", "add-return-reason-product-was-damaged-during-shipping": "Product was damaged during shipping", "add-return-reason-cancel": "Cancel", "add-return-reason-save-and-go-back": "Save and go back", "add-return-reason-select-reason-title": "Select Reason", "add-return-reason-edit-reason": "Edit reason", "add-return-reason-select-reason": "Select reason", "items-to-return-form-items-to-claim": "Items to claim", "items-to-return-form-items-to-return": "Items to return", "items-to-return-form-product": "Product", "items-to-return-form-quantity": "Quantity", "items-to-return-form-refundable": "Refundable", "add-additional-items-screen-go-back": "Go back", "add-additional-items-screen-add-products": "Add products", "add-additional-items-screen-add-product-variants": "Add Product Variants", "add-additional-items-screen-search-products": "Search products", "add-additional-items-screen-variant-price-missing": "This variant does not have a price for the region/currency of this order, and cannot be selected.", "add-additional-items-screen-stock": "Stock", "add-additional-items-screen-price": "Price", "add-additional-items-screen-price-overridden-in-price-list-applicable-to-this-order": "The price has been overridden in a price list, that is applicable to this order.", "items-to-send-form-items-to-send": "Items to send", "items-to-send-form-add-products": "Add products", "items-to-send-form-product": "Product", "items-to-send-form-quantity": "Quantity", "items-to-send-form-price": "Price", "items-to-send-form-price-overridden-in-price-list-applicable-to-this-order": "The price has been overridden in a price list, that is applicable to this order.", "refund-amount-form-cancel-editing-refund-amount": "Cancel editing refund amount", "refund-amount-form-edit-refund-amount": "Edit refund amount", "refund-amount-form-refund-amount-cannot-be-negative": "Refund amount cannot be negative", "refund-amount-form-the-refund-amount-must-be-at-least-0": "The refund amount must be at least 0", "reservation-indicator-awaiting-reservation-count": "{{awaitingReservation}} items not reserved", "reservation-indicator-this-item-has-been-fulfilled": "This item has been fulfilled.", "edit-reservation-button-quantity-item-location-name": "{{quantity}} item: ${{locationName}}", "reservation-indicator-edit-reservation": "Edit reservation", "rma-summaries-claimed-items": "Claimed items", "rma-summaries-replacement-items": "Replacement items", "rma-summaries-customer-refund-description": "The customer will receive a full refund for the claimed items, as the cost of replacement items and shipping will not be deducted. Alternatively, you can choose to set a custom refund amount when you receive the returned items or create an exchange instead.", "rma-summaries-refund-amount": "Refund amount", "rma-summaries-the-customer-will-be-refunded-once-the-returned-items-are-received": "The customer will be refunded once the returned items are received", "rma-summaries-the-customer-will-be-refunded-immediately": "The customer will be refunded immediately", "rma-summaries-receiving": "Receiving", "rma-summaries-free": "Free", "send-notification-form-return": "return", "send-notification-form-exchange": "exchange", "send-notification-form-claim": "claim", "send-notification-form-send-notifications": "Send notifications", "send-notification-form-if-unchecked-the-customer-will-not-receive-communication": "If unchecked the customer will not receive communication about this {{subject}}.", "shipping-address-form-shipping-address": "Shipping address", "shipping-address-form-ship-to-a-different-address": "Ship to a different address", "shipping-address-form-cancel": "Cancel", "shipping-address-form-save-and-go-back": "Save and go back", "shipping-address-form-shipping-information": "Shipping Information", "shipping-form-shipping-for-return-items": "Shipping for return items", "shipping-form-shipping-for-replacement-items": "Shipping for replacement items", "shipping-form-shipping-method-is-required": "Shipping method is required", "shipping-form-choose-shipping-method": "Choose shipping method", "shipping-form-shipping-method": "Shipping method", "shipping-form-add-custom-price": "Add custom price", "shipping-form-return-shipping-for-items-claimed-by-the-customer-is-complimentary": "Return shipping for items claimed by the customer is complimentary.", "shipping-form-shipping-for-replacement-items-is-complimentary": "Shipping for replacement items is complimentary.", "components-decrease-quantity": "Decrease quantity", "components-increase-quantity": "Increase quantity", "details-successfully-updated-address": "Successfully updated address", "details-billing-address": "Billing Address", "details-shipping-address": "Shipping Address", "details-contact": "Contact", "details-location": "Location", "claim-are-you-sure-you-want-to-close": "Are you sure you want to close?", "claim-you-have-unsaved-changes-are-you-sure-you-want-to-close": "You have unsaved changes, are you sure you want to close?", "claim-please-select-a-reason": "Please select a reason", "claim-a-shipping-method-for-replacement-items-is-required": "A shipping method for replacement items is required", "claim-successfully-created-claim": "Successfully created claim", "claim-created": "A claim for order #{{display_id}} was successfully created", "claim-error-creating-claim": "Error creating claim", "claim-create-claim": "Create <PERSON><PERSON><PERSON>", "claim-location": "Location", "claim-choose-which-location-you-want-to-return-the-items-to": "Choose which location you want to return the items to.", "claim-select-location-to-return-to": "Select Location to Return to", "claim-cancel": "Cancel", "claim-submit-and-close": "Submit and close", "create-fulfillment-error": "Error", "create-fulfillment-please-select-a-location-to-fulfill-from": "Please select a location to fulfill from", "create-fulfillment-cant-allow-this-action": "Can't allow this action", "create-fulfillment-trying-to-fulfill-more-than-in-stock": "Trying to fulfill more than in stock", "create-fulfillment-successfully-fulfilled-order": "Successfully fulfilled order", "create-fulfillment-successfully-fulfilled-swap": "Successfully fulfilled swap", "create-fulfillment-successfully-fulfilled-claim": "Successfully fulfilled claim", "create-fulfillment-success": "Success", "create-fulfillment-cancel": "Cancel", "create-fulfillment-create-fulfillment": "Create fulfillment", "create-fulfillment-create-fulfillment-title": "Create Fulfillment", "create-fulfillment-locations": "Locations", "create-fulfillment-choose-where-you-wish-to-fulfill-from": "Choose where you wish to fulfill from.", "create-fulfillment-items-to-fulfill": "Items to fulfill", "create-fulfillment-select-the-number-of-items-that-you-wish-to-fulfill": "Select the number of items that you wish to fulfill.", "create-fulfillment-send-notifications": "Send notifications", "create-fulfillment-when-toggled-notification-emails-will-be-sent": "When toggled, notification emails will be sent.", "create-fulfillment-quantity-is-not-valid": "Quantity is not valid", "detail-cards-allocated": "Allocated", "detail-cards-not-fully-allocated": "Not fully allocated", "detail-cards-subtotal": "Subtotal", "detail-cards-shipping": "Shipping", "detail-cards-tax": "Tax", "detail-cards-total": "Total", "detail-cards-edit-order": "Edit Order", "detail-cards-allocate": "Allocate", "detail-cards-discount": "Discount:", "detail-cards-original-total": "Original Total", "details-successfully-updated-the-email-address": "Successfully updated the email address", "details-email-address": "Email Address", "details-save": "Save", "details-order-id-copied": "Order ID copied", "details-email-copied": "Email copied", "details-cancel-order-heading": "Cancel order", "details-are-you-sure-you-want-to-cancel-the-order": "Are you sure you want to cancel the order?", "order-details-display-id": "order #{{display_id}}", "details-successfully-canceled-order": "Successfully canceled order", "details-go-to-customer": "Go to Customer", "details-transfer-ownership": "Transfer ownership", "details-edit-shipping-address": "Edit Shipping Address", "details-edit-billing-address": "Edit Billing Address", "details-edit-email-address": "Edit Email Address", "details-back-to-orders": "Back to Orders", "details-cancel-order": "Cancel Order", "details-payment": "Payment", "details-refunded": "Refunded", "details-total-paid": "Total Paid", "details-fulfillment": "Fulfillment", "details-create-fulfillment": "Create Fulfillment", "details-shipping-method": "Shipping Method", "details-customer": "Customer", "details-shipping": "Shipping", "details-billing": "Billing", "details-raw-order": "Raw order", "mark-shipped-successfully-marked-order-as-shipped": "Successfully marked order as shipped", "mark-shipped-successfully-marked-swap-as-shipped": "Successfully marked swap as shipped", "mark-shipped-successfully-marked-claim-as-shipped": "Successfully marked claim as shipped", "mark-shipped-success": "Success", "mark-shipped-error": "Error", "mark-shipped-mark-fulfillment-shipped": "<PERSON>lfill<PERSON> Shipped", "mark-shipped-tracking": "Tracking", "mark-shipped-tracking-number-label": "Tracking number", "mark-shipped-tracking-number": "Tracking number...", "mark-shipped-add-additional-tracking-number": "+ Add Additional Tracking Number", "mark-shipped-send-notifications": "Send notifications", "mark-shipped-cancel": "Cancel", "mark-shipped-complete": "Complete", "order-line-warning": "Warning", "order-line-cannot-duplicate-an-item-without-a-variant": "Cannot duplicate an item without a variant", "order-line-error": "Error", "order-line-failed-to-duplicate-item": "Failed to duplicate item", "order-line-success": "Success", "order-line-item-removed": "Item removed", "order-line-failed-to-remove-item": "Failed to remove item", "order-line-item-added": "<PERSON><PERSON> added", "order-line-failed-to-replace-the-item": "Failed to replace the item", "order-line-replace-product-variants": "Replace Product Variants", "order-line-replace-with-other-item": "Replace with other item", "order-line-duplicate-item": "Duplicate item", "order-line-remove-item": "Remove item", "order-line-line-item-cannot-be-edited": "This line item is part of a fulfillment and cannot be edited. Cancel the fulfillment to edit the line item.", "order-line-new": "New", "order-line-modified": "Modified", "receive-return-please-select-at-least-one-item-to-receive": "Please select at least one item to receive", "receive-return-successfully-received-return": "Successfully received return", "receive-return-received-return-for-order": "Received return for order #{{display_id}}", "receive-return-failed-to-receive-return": "Failed to receive return", "receive-return-receive-return": "Receive Return", "receive-return-location": "Location", "receive-return-choose-location": "Choose which location you want to return the items to.", "receive-return-select-location-to-return-to": "Select Location to Return to", "receive-return-no-inventory-levels-exist-for-the-items-at-the-selected-location": "No inventory levels exist for the items at the selected location", "receive-return-cancel": "Cancel", "receive-return-save-and-close": "Save and close", "refund-success": "Success", "refund-successfully-refunded-order": "Successfully refunded order", "refund-error": "Error", "refund-create-a-refund": "Create a refund", "refund-attention": "Attention!", "refund-system-payment-disclaimer": "One or more of your payments is a system payment. Be aware, that captures and refunds are not handled by Medusa for such payments.", "refund-details": "Details", "refund-cannot-refund-more-than-the-orders-net-total": "Cannot refund more than the order's net total.", "refund-discount": "Discount", "refund-reason": "Reason", "refund-note": "Note", "refund-discount-for-loyal-customer": "Discount for loyal customer", "refund-send-notifications": "Send notifications", "refund-cancel": "Cancel", "refund-complete": "Complete", "reservation-reservation-was-deleted": "Reservation was deleted", "reservation-the-allocated-items-have-been-released": "The allocated items have been released.", "reservation-error": "Error", "reservation-failed-to-delete-the-reservation": "Failed to delete the reservation ", "reservation-reservation-was-updated": "Reservation was updated", "reservation-the-reservation-change-was-saved": "The reservation change was saved.", "reservation-errors": "Errors", "reservation-failed-to-update-reservation": "Failed to update reservation", "reservation-edit-reservation": "Edit Reservation", "reservation-location": "Location", "reservation-choose-which-location-you-want-to-ship-the-items-from": "Choose which location you want to ship the items from.", "reservation-items-to-allocate-title": "Items to Allocate", "reservation-select-the-number-of-items-that-you-wish-to-allocate": "Select the number of items that you wish to allocate.", "reservation-max-reservation-requested": " / {{maxReservation}} requested", "reservation-reserved": " reserved", "reservation-description": "Description", "reservation-what-type-of-reservation-is-this": "What type of reservation is this?", "reservation-metadata": "<PERSON><PERSON><PERSON>", "reservation-remove-metadata": "Remove metadata", "reservation-add-metadata": "Add metadata", "reservation-delete-reservation": "Delete reservation", "reservation-cancel": "Cancel", "reservation-save-and-close": "Save and close", "reservation-couldnt-allocate-items": "Couldn't allocate items", "reservation-items-allocated": "Items allocated", "reservation-items-have-been-allocated-successfully": "Items have been allocated successfully", "reservation-save-reservation": "Save reservation", "reservation-loading": "Loading...", "reservation-allocate-order-items": "Allocate order items", "reservation-choose-where-you-wish-to-allocate-from": "Choose where you wish to allocate from", "reservation-items-to-allocate": "Items to allocate", "returns-success": "Success", "returns-successfully-returned-order": "Successfully returned order", "returns-error": "Error", "returns-request-return": "Request Return", "returns-items-to-return": "Items to return", "returns-choose-which-location-you-want-to-return-the-items-to": "Choose which location you want to return the items to.", "returns-select-location-to-return-to": "Select Location to Return to", "returns-selected-location-has-no-inventory-levels": "The selected location does not have inventory levels for the selected items. The return can be requested but can't be received until an inventory level is created for the selected location.", "returns-shipping": "Shipping", "returns-choose-retur,-shipping-method": "Choose which shipping method you want to use for this return.", "returns-total-refund": "Total Refund", "returns-amount": "Amount", "returns-send-notifications": "Send notifications", "returns-notify-customer-of-created-return": "Notify customer of created return", "returns-back": "Back", "returns-submit": "Submit", "rma-sub-modals-search-for-additional": "Search for additional", "rma-sub-modals-general": "General", "rma-sub-modals-first-name": "First Name", "rma-sub-modals-last-name": "Last Name", "rma-sub-modals-phone": "Phone", "rma-sub-modals-shipping-address": "Shipping Address", "rma-sub-modals-address-1": "Address 1", "rma-sub-modals-address-2": "Address 2", "rma-sub-modals-province": "Province", "rma-sub-modals-postal-code": "Postal code", "rma-sub-modals-city": "City", "rma-sub-modals-country": "Country", "rma-sub-modals-back": "Back", "rma-sub-modals-add": "Add", "rma-sub-modals-name": "Name", "rma-sub-modals-status": "Status", "rma-sub-modals-in-stock": "In Stock", "rma-sub-modals-products": "Products", "rma-sub-modals-search-products": "Search Products..", "rma-sub-modals-reason-for-return": "Reason for Return", "rma-sub-modals-reason": "Reason", "rma-sub-modals-note": "Note", "swap-success": "Success", "swap-successfully-created-exchange": "Successfully created exchange", "swap-error": "Error", "swap-register-exchange": "Register Exchange", "swap-items-to-return": "Items to return", "swap-shipping": "Shipping", "swap-shipping-method": "Shipping Method", "swap-add-a-shipping-method": "Add a shipping method", "swap-location": "Location", "swap-choose-which-location-you-want-to-return-the-items-to": "Choose which location you want to return the items to.", "swap-select-location-to-return-to": "Select Location to Return to", "swap-items-to-send": "Items to send", "swap-add-product": "Add Product", "swap-return-total": "Return Total", "swap-additional-total": "Additional Total", "swap-outbond-shipping": "Outbond Shipping", "swap-calculated-at-checkout": "Calculated at checkout", "swap-estimated-difference": "Estimated difference", "swap-send-notifications": "Send notifications", "swap-if-unchecked-the-customer-will-not-receive-communication-about-this-exchange": "If unchecked the customer will not receive communication about this exchange", "swap-complete": "Complete", "templates-shipped": "Shipped", "templates-fulfilled": "Fulfilled", "templates-canceled": "Canceled", "templates-partially-fulfilled": "Partially fulfilled", "templates-fulfillment-status-requires-action": "Requires Action", "templates-awaiting-fulfillment": "Awaiting fulfillment", "templates-partially-shipped": "Partially Shipped", "templates-cancel-fulfillment-heading": "Cancel fulfillment?", "templates-are-you-sure-you-want-to-cancel-the-fulfillment": "Are you sure you want to cancel the fulfillment?", "templates-successfully-canceled-swap": "Successfully canceled swap", "templates-error": "Error", "templates-successfully-canceled-claim": "Successfully canceled claim", "templates-successfully-canceled-fulfillment": "Successfully canceled fulfillment", "templates-fulfillment-has-been-canceled": "Fulfillment has been canceled", "templates-fulfilled-by-provider": "{{title}} Fulfilled by {{provider}}", "templates-not-shipped": "Not shipped", "templates-tracking": "Tracking", "templates-shipped-from": "Shipped from", "templates-shipping-from": "Shipping from", "templates-mark-shipped": "<PERSON>", "templates-cancel-fulfillment": "Cancel Fulfillment", "templates-completed": "Completed", "templates-processing": "Processing", "templates-requires-action": "Requires action", "templates-capture-payment": "Capture payment", "templates-successfully-captured-payment": "Successfully captured payment", "templates-refund": "Refund", "templates-total-for-swaps": "Total for Swaps", "templates-refunded-for-swaps": "Refunded for Swaps", "templates-refunded-for-returns": "Refunded for Returns", "templates-manually-refunded": "Manually refunded", "templates-net-total": "Net Total", "templates-paid": "Paid", "templates-awaiting-payment": "Awaiting payment", "templates-payment-status-requires-action": "Requires Action", "draft-orders-completed": "Completed", "draft-orders-open": "Open", "draft-orders-mark-as-paid": "Mark as paid", "draft-orders-success": "Success", "draft-orders-successfully-mark-as-paid": "Successfully mark as paid", "draft-orders-error": "Error", "draft-orders-successfully-canceled-order": "Successfully canceled order", "draft-orders-back-to-draft-orders": "Back to Draft Orders", "on-mark-as-paid-confirm-order-id": "Order #{{display_id}}", "draft-orders-go-to-order": "Go to Order", "draft-orders-cancel-draft-order": "Cancel Draft Order", "draft-orders-draft-order": "Draft Order", "draft-orders-email": "Email", "draft-orders-phone": "Phone", "draft-orders-amount": "Amount {{currency_code}}", "draft-orders-payment": "Payment", "draft-orders-subtotal": "Subtotal", "draft-orders-shipping": "Shipping", "draft-orders-tax": "Tax", "draft-orders-total-to-pay": "Total to pay", "draft-orders-payment-link": "Payment link:", "draft-orders-configure-payment-link-in-store-settings": "Configure payment link in store settings", "draft-orders-shipping-method": "Shipping Method", "draft-orders-data": "Data", "draft-orders-1-item": "(1 item)", "draft-orders-customer": "Customer", "draft-orders-edit-shipping-address": "Edit Shipping Address", "draft-orders-edit-billing-address": "Edit Billing Address", "draft-orders-go-to-customer": "Go to Customer", "draft-orders-contact": "Contact", "draft-orders-billing": "Billing", "draft-orders-raw-draft-order": "Raw Draft Order", "draft-orders-are-you-sure": "Are you sure?", "draft-orders-remove-resource-heading": "Remove {{resource}}", "draft-orders-remove-resource-success-text": "{{resource}} has been removed", "draft-orders-this-will-create-an-order-mark-this-as-paid-if-you-received-the-payment": "This will create an order. Mark this as paid if you received the payment.", "draft-orders-mark-paid": "Mark paid", "draft-orders-cancel": "Cancel", "draft-orders-create-draft-order": "Create draft order", "edit-amount-paid": "Amount <PERSON>", "edit-new-total": "New Total", "edit-difference-due": "Difference Due", "edit-back": "Back", "edit-save-and-go-back": "Save and go back", "edit-order-edit-set-as-requested": "Order edit set as requested", "edit-failed-to-request-confirmation": "Failed to request confirmation", "edit-added-successfully": "Added successfully", "edit-error-occurred": "Error occurred", "edit-add-product-variants": "Add Product Variants", "edit-edit-order": "Edit Order", "edit-items": "Items", "edit-add-items": "Add items", "edit-filter-items": "Filter items...", "edit-note": "Note", "edit-add-a-note": "Add a note...", "variants-table-location": " in {{location}}", "edit-product": "Product", "edit-in-stock": "In Stock", "edit-price": "Price", "edit-products": "Products", "edit-search-product-variants": "Search Product Variants...", "orders-success": "Success", "orders-successfully-initiated-export": "Successfully initiated export", "orders-error": "Error", "orders-export-orders": "Export Orders", "components-billing-address": "Billing Address", "components-use-same-as-shipping": "Use same as shipping", "components-e-g-gift-wrapping": "E.g. Gift wrapping", "components-title": "Title", "components-price": "Price", "components-quantity": "Quantity", "components-back": "Back", "components-add": "Add", "components-items-for-the-order": "Items for the order", "components-details": "Details", "components-price-excl-taxes": "Price (excl. Taxes)", "components-add-custom": "Add Custom", "components-add-existing": "Add Existing", "components-add-products": "Add Products", "components-add-custom-item": "Add Custom Item", "components-choose-region": "Choose region", "components-region": "Region", "select-shipping-to-name": "(To {{name}})", "components-attention": "Attention!", "components-no-options-for-orders-without-shipping": "You don't have any options for orders without shipping. Please add one (e.g. \"In-store fulfillment\") with \"Show on website\" unchecked in region settings and continue.", "components-choose-a-shipping-method": "Choose a shipping method", "components-set-custom-price": "Set custom price", "components-custom-price": "Custom Price", "components-customer-and-shipping-details": "Customer and shipping details", "components-find-existing-customer": "Find existing customer", "components-email": "Email", "components-choose-existing-addresses": "Choose existing addresses", "components-create-new": "Create new", "components-the-discount-is-not-applicable-to-the-selected-region": "The discount is not applicable to the selected region", "components-the-discount-code-is-invalid": "The discount code is invalid", "components-add-discount": "Add Discount", "components-summer-10": "SUMMER10", "components-discount": "Discount", "select-shipping-code": "(Code: {{code}})", "components-type": "Type", "components-value": "Value", "components-address": "Address", "components-shipping-method": "Shipping method", "components-billing-details": "Billing details", "components-edit": "Edit", "form-use-new-order-form-must-be-used-within-new-order-form-provider": "useNewOrderForm must be used within NewOrderFormProvider", "new-order-created": "Order created", "new-create-draft-order": "Create Draft Order", "price-list-product-filter-created-at": "Created at", "price-list-product-filter-updated-at": "Updated at", "price-list-details-drawer-prompt-title": "Are you sure?", "price-list-details-drawer-prompt-description": "You have unsaved changes, are you sure you want to exit?", "price-list-details-notification-succes-title": "Price list updated", "price-list-details-drawer-notification-success-message": "Successfully updated price list", "price-list-details-drawer-notification-error-title": "An error occurred", "price-list-details-drawer-title": "Edit Price List Details", "price-list-details-drawer-cancel-button": "Cancel", "price-list-details-drawer-save-button": "Save", "price-list-details-section-prompt-confirm-text": "Delete", "price-list-details-section-prompt-cancel-text": "Cancel", "price-list-details-section-prompt-title": "Delete price list", "price-list-details-section-prompt-description": "Are you sure you want to delete the price list \"{{name}}\"?", "price-list-details-section-delete-notification-success-title": "Successfully deleted price list", "price-list-details-section-delete-notification-success-message": "The price list \"{{name}}\" was successfully deleted", "price-list-details-section-delete-notification-error-title": "Failed to delete price list", "price-list-details-section-customer-groups": "Customer Groups", "price-list-details-section-last-edited": "Last edited", "price-list-details-section-number-of-prices": "Prices", "price-list-details-section-status-menu-expired": "Expired", "price-list-details-section-status-menu-draft": "Draft", "price-list-details-section-status-menu-scheduled": "Scheduled", "price-list-details-section-status-active": "Active", "price-list-details-section-status-menu-notification-success-title": "Successfully updated price list status", "price-list-details-section-status-menu-notification-success-message": "The price list status was successfully updated to {{status}}", "price-list-details-section-status-menu-notification-error-title": "Failed to update price list status", "price-list-details-section-status-menu-item-draft": "Draft", "price-list-details-section-status-menu-item-activate": "Activate", "price-list-details-menu-item-edit": "Edit details", "price-list-details-menu-item-delete": "Delete", "price-list-edit-error": "An error occurred while loading price list. Reload the page and try again. If the issue persists, try again later.", "price-list-new-form-prompt-title": "Are you sure?", "price-list-new-form-prompt-exit-description": "You have unsaved changes, are you sure you want to exit?", "price-list-new-form-prompt-back-description": "You have unsaved changes, are you sure you want to go back?", "price-list-add-products-modal-no-prices-error": "Please assign prices for at least one product.", "price-list-add-products-modal-missing-prices-title": "Incomplete price list", "price-list-add-products-modal-missing-prices-description": "Prices have not been assigned to all of your chosen products. Would you like to continue?", "price-list-add-products-modal-success-title": "New prices added", "price-list-add-products-modal-success-message": "The new prices have been added to the price list.", "price-list-add-products-modal-error-title": "An error occurred", "price-list-add-products-modal-back-button-cancel": "Cancel", "price-list-add-products-modal-back-button": "Back", "price-list-add-products-modal-next-button-continue": "Continue", "price-list-add-products-modal-next-button-submit-and-close": "Submit and Close", "price-list-add-products-modal-next-button-continue-save-prices": "Save Prices", "price-list-add-products-modal-products-tab": "Choose Products", "price-list-add-products-modal-prices-tab": "Edit Prices", "price-list-add-products-modal-error": "An error occurred while preparing the form. Reload the page and try again. If the issue persists, try again later.", "price-list-edit-prices-modal-prompt-title": "Unsaved changes", "price-list-edit-prices-modal-prompt-exit-description": "You have unsaved changes, are you sure you want to exit?", "price-list-edit-prices-modal-prompt-back-description": "You have unsaved changes, are you sure you want to go back?", "price-list-edit-prices-modal-notification-update-error": "An error occurred", "price-list-edit-prices-modal-notification-remove-error-title": "An error occurred", "price-list-edit-prices-modal-notification-remove-error-description": "Some prices were not updated correctly. Try again.", "price-list-edit-prices-modal-notification-update-success-title": "Prices updated", "price-list-edit-prices-modal-notification-update-success-description": "Successfully updated prices", "price-list-edit-prices-modal-next-button-save-and-close": "Save and Close", "price-list-edit-prices-modal-next-button-save": "Save Prices", "price-list-edit-prices-modal-back-button-cancel": "Cancel", "price-list-edit-prices-modal-back-button-back": "Back", "price-list-edit-prices-modal-overview-tab": "Edit Prices", "price-list-edit-prices-modal-error-loading": "An error occurred while preparing the form. Reload the page and try again. If the issue persists, try again later.", "price-list-prices-section-prompt-title": "Are you sure?", "price-list-prices-section-prompt-description": "This will permanently delete the product prices from the list", "price-list-prices-secton-delete-success-title": "Prices deleted", "price-list-prices-section-delete-success-description_one": "Successfully deleted prices for {{count}} products", "price-list-prices-section-delete-success-description_other": "Successfully deleted prices for {{count}} products", "price-list-prices-section-delete-error-title": "An error occurred", "price-list-prices-section-heading": "Prices", "price-list-prices-section-search-placeholder": "Search products", "price-list-prices-section-prices-menu-edit": "Edit prices", "price-list-prices-section-prices-menu-add": "Add products", "price-list-prices-section-table-load-error": "An error occured while fetching the products. Try to reload the page, or if the issue persists, try again later.", "price-list-prices-section-bar-count_one": "{{count}} selected", "price-list-prices-section-bar-count_other": "{{count}} selected", "price-list-prices-section-edit-command": "Edit", "price-list-prices-section-delete-command": "Delete", "price-list-prices-section-select-all-checkbox-label": "Select all products on the current page", "price-list-prices-section-select-checkbox-label": "Select row", "price-list-prices-section-table-product": "Product", "price-list-prices-section-table-thumbnail-alt": "{{title}} thumbnail", "price-list-prices-section-table-collection": "Collection", "price-list-prices-section-table-variants": "Variants", "price-list-details-form-type-heading": "Type", "price-list-details-form-type-description": "Choose the type of price list you want to create.", "price-list-details-form-type-label-sale": "Sale", "price-list-details-form-type-hint-sale": "Use this if you are creating a sale.", "price-list-details-form-type-label-override": "Override", "price-list-details-form-type-hint-override": "Use this if you are overriding prices.", "price-list-details-form-general-heading": "General", "price-list-details-form-general-description": "Choose a title and description for the price list.", "price-list-details-form-general-name-label": "Name", "price-list-details-form-general-name-placeholder": "Black Friday Sale", "price-list-details-form-general-description-label": "Description", "price-list-details-form-general-description-placeholder": "Prices for the Black Friday sale...", "price-list-details-form-tax-inclusive-label": "Tax inclusive prices", "price-list-details-form-tax-inclusive-hint": "Choose to make all prices in this list inclusive of tax.", "price-list-details-form-dates-starts-at-heading": "Price list has a start date?", "price-list-details-form-dates-starts-at-description": "Schedule the price overrides to activate in the future.", "price-list-details-form-dates-starts-at-label": "Start date", "price-list-details-form-ends-at-heading": "Price list has an expiry date?", "price-list-details-form-ends-at-description": "Schedule the price overrides to deactivate in the future.", "price-list-details-form-ends-at-label": "Expiry date", "price-list-details-form-customer-groups-name": "Name", "price-list-details-form-customer-groups-members": "Members", "price-list-details-form-customer-groups-error": "An error occurred while loading customer groups. Reload the page and try again. If the issue persists, try again later.", "price-list-details-form-customer-groups-no-groups": "No customer groups found.", "price-list-details-form-customer-groups-heading": "Customer availability", "price-list-details-form-customer-groups-description": "Specify which customer groups the price overrides should apply for.", "price-list-details-form-customer-groups-content-heading": "Customer Groups", "price-list-details-form-customer-groups-search-placeholder": "Search", "price-list-prices-form-products-error": "An error occurred while preparing the form. Reload the page and try again. If the issue persists, try again later.", "price-list-prices-form-heading": "Edit prices", "price-list-prices-form-variant": "<PERSON><PERSON><PERSON>", "price-list-prices-form-sku": "SKU", "price-list-prices-form-prices": "Prices", "price-list-prices-form-prices-variant-count_one": "{{count}} variants", "price-list-prices-form-prices-variant-count_other": "{{count}} variants", "price-list-prices-form-add-prices-button": "Add prices", "price-list-prices-form-prices-count_one": "{{count}} prices", "price-list-prices-form-prices-count_other": "{{count}} prices", "price-list-product-prices-form-invalid-data-title": "Invalid data", "price-list-product-prices-form-invalid-data-body": "The data you pasted contains values that are not numbers.", "price-list-product-prices-form-column-visibility-button": "Currencies", "price-list-product-prices-form-column-visibility-currencies-label": "Currencies", "price-list-product-prices-form-column-visibility-regions-label": "Regions", "price-list-product-prices-form-column-product-label": "Product", "price-list-product-prices-form-column-currencies-price-label": "Price {{code}}", "price-list-product-prices-form-column-regions-price-label": "Price {{name}} ({{code}})", "price-list-products-form-select-all": "Select all products on the current page", "price-list-products-form-select-row": "Select row", "price-list-products-form-product-label": "Product", "price-list-products-form-product-thumbnail": "{{title}} thumbnail", "price-list-products-form-collection-label": "Collection", "price-list-products-form-sales-channels-label": "Availability", "price-list-products-form-sales-channels-value": "{{first}} + {{remaining}} more", "price-list-products-form-status-label": "Status", "price-list-products-form-inventory-label": "Inventory", "price-list-products-form-inventory-value": "{{totalStock}} in stock across {{variants}} variants", "price-list-products-form-loading": "Loading products", "price-list-products-form-error": "An error occurred while loading products. Reload the page and try again. If the issue persists, try again later.", "price-list-products-form-no-products": "No products found.", "price-list-products-form-heading": "Choose products", "price-list-products-form-search-placeholder": "Search", "price-list-new-form-no-prices-error": "Please set prices for at least one product.", "price-list-new-form-missing-prices-title": "Incomplete price list", "price-list-new-products-modal-missing-prices-description": "Prices have not been assigned to all of your chosen products. Would you like to proceed?", "price-list-new-form-notification-success-title": "Price list created", "price-list-new-form-notification-success-message": "Successfully created price list", "price-list-new-form-notification-error-title": "An error occurred", "price-list-new-form-next-button-save-and-publish": "Save and Publish", "price-list-new-form-next-button-save": "Save Prices", "price-list-new-form-next-button-continue": "Continue", "price-list-new-form-back-button-cancel": "Cancel", "price-list-new-form-back-button-back": "Back", "price-list-new-form-details-tab": "Create Price List", "price-list-new-form-products-tab": "Choose Products", "price-list-new-form-prices-tab": "Edit Prices", "price-list-new-form-save-as-draft": "Save as Draft", "price-list-new-form-error-loading-products": "An error occurred while preparing the form. Reload the page and try again. If the issue persists, try again later.", "components-success": "Success", "components-successfully-updated-category-tree": "Successfully updated category tree", "components-error": "Error", "components-failed-to-update-category-tree": "Failed to update category tree", "components-delete": "Delete", "components-category-deleted": "Category deleted", "components-category-deletion-failed": "Category deletion failed", "components-category-status-is-inactive": "Category status is inactive", "components-category-visibility-is-private": "Category visibility is private", "components-add-category-item-to": "Add category item to", "modals-public": "Public", "modals-private": "Private", "modals-active": "Active", "modals-inactive": "Inactive", "modals-success": "Success", "modals-successfully-created-a-category": "Successfully created a category", "modals-failed-to-create-a-new-category": "Failed to create a new category", "modals-error": "Error", "modals-save-category": "Save category", "modals-add-category-to": "Add category to {{name}}", "modals-add-category": "Add category", "modals-details": "Details", "modals-name": "Name", "modals-give-this-category-a-name": "Give this category a name", "modals-handle": "<PERSON><PERSON>", "modals-custom-handle": "Custom handle", "modals-description": "Description", "modals-give-this-category-a-description": "Give this category a description", "modals-status": "Status", "modals-visibility": "Visibility", "modals-successfully-updated-the-category": "Successfully updated the category", "modals-failed-to-update-the-category": "Failed to update the category", "modals-edit-product-category": "Edit product category", "modals-cancel": "Cancel", "modals-save-and-close": "Save and close", "pages-no-product-categories-yet": "No product categories yet, use the above button to create your first category.", "pages-add-category": "Add category", "pages-product-categories": "Product Categories", "pages-helps-you-to-keep-your-products-organized": "Helps you to keep your products organized.", "batch-job-success": "Success", "batch-job-import-confirmed-for-processing-progress-info-is-available-in-the-activity-drawer": "Import confirmed for processing. Progress info is available in the activity drawer.", "batch-job-error": "Error", "batch-job-import-failed": "Import failed.", "batch-job-failed-to-delete-the-csv-file": "Failed to delete the CSV file", "batch-job-failed-to-cancel-the-batch-job": "Failed to cancel the batch job", "batch-job-products-list": "products list", "batch-job-unsure-about-how-to-arrange-your-list": "Unsure about how to arrange your list?", "batch-job-download-template": "Download the template below to ensure you are following the correct format.", "batch-job-imports-description": "Through imports you can add or update products. To update existing products/variants you must set an existing id in the Product/Variant id columns. If the value is unset a new record will be created. You will be asked for confirmation before we import products.", "products-filters": "Filters", "products-status": "Status", "products-tags": "Tags", "products-spring-summer": "Spring, summer...", "new-sales-channels": "Sales channels", "new-this-product-will-only-be-available-in-the-default-sales-channel-if-left-untouched": "This product will only be available in the default sales channel if left untouched.", "new-change-availablity": "Change availablity", "add-variants-a-variant-with-these-options-already-exists": "A variant with these options already exists.", "add-variants-product-options": "Product options", "add-variants-options-are-used-to-define-the-color-size-etc-of-the-product": "Options are used to define the color, size, etc. of the product.", "add-variants-option-title": "Option title", "add-variants-variations-comma-separated": "Variations (comma separated)", "add-variants-color": "Color...", "add-variants-already-exists": "already exists", "add-variants-blue-red-black": "Blue, Red, Black...", "add-variants-add-an-option": "Add an option", "add-variants-product-variants": "Product variants", "add-variants-you-must-add-at-least-one-product-option-before-you-can-begin-adding-product-variants": "You must add at least one product option before you can begin adding product variants.", "add-variants-variant": "<PERSON><PERSON><PERSON>", "add-variants-inventory": "Inventory", "add-variants-add-a-variant": "Add a variant", "add-variants-create-variant": "C<PERSON> <PERSON><PERSON><PERSON>", "add-variants-cancel": "Cancel", "add-variants-save-and-close": "Save and close", "new-variant-a-variant-with-these-options-already-exists": "A variant with these options already exists.", "new-variant-are-you-sure-you-want-to-delete-this-variant": "Are you sure you want to delete this variant?", "new-variant-delete-variant": "Delete Variant", "new-variant-edit": "Edit", "new-variant-delete": "Delete", "new-variant-edit-variant": "<PERSON>", "new-variant-cancel": "Cancel", "new-variant-save-and-close": "Save and close", "new-something-went-wrong-while-trying-to-upload-images": "Something went wrong while trying to upload images.", "new-no-file-service-configured": "You might not have a file service configured. Please contact your administrator", "new-upload-thumbnail-error": "Something went wrong while trying to upload the thumbnail.", "new-save-as-draft": "Save as draft", "new-publish-product": "Publish product", "new-general-information-title": "General information", "new-to-start-selling-all-you-need-is-a-name-and-a-price": "To start selling, all you need is a name and a price.", "new-organize-product": "Organize Product", "new-add-variations-of-this-product": "Add variations of this product.", "new-offer-your-customers-different-options-for-color-format-size-shape-etc": "Offer your customers different options for color, format, size, shape, etc.", "new-used-for-shipping-and-customs-purposes": "Used for shipping and customs purposes.", "new-dimensions": "Dimensions", "new-customs": "Customs", "new-used-to-represent-your-product-during-checkout-social-sharing-and-more": "Used to represent your product during checkout, social sharing and more.", "new-media": "Media", "new-add-images-to-your-product": "Add images to your product.", "overview-import-products": "Import Products", "overview-export-products": "Export Products", "overview-new-product": "New Product", "overview-new-collection": "New Collection", "overview-success": "Success", "overview-successfully-created-collection": "Successfully created collection", "overview-error": "Error", "overview-successfully-initiated-export": "Successfully initiated export", "modals-add-sales-channels": "Add sales channels", "modals-find-channels": "Find channels", "modals-updated-the-api-key": "Updated the API key", "modals-failed-to-update-the-api-key": "Failed to update the API key", "modals-edit-api-key-details": "Edit API key details", "modals-title": "Title", "modals-name-your-key": "Name your key", "modals-sales-channels-added-to-the-scope": "Sales channels added to the scope", "modals-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "Error occurred while adding sales channels to the scope of the key", "modals-add-and-go-back": "Add and go back", "modals-add-and-close": "Add and close", "modals-sales-channels-removed-from-the-scope": "Sales channels removed from the scope", "modals-error-occurred-while-removing-sales-channels-from-the-scope-of-the-key": "Error occurred while removing sales channels from the scope of the key", "modals-edit-sales-channels": "Edit sales channels", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_one": "{{count}}", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_other": "{{count}}", "modals-deselect": "Deselect", "modals-remove": "Remove", "modals-add-channels": "Add channels", "modals-close": "Close", "pages-sales-channels": "Sales channels", "pages-connect-as-many-sales-channels-to-your-api-key-as-you-need": "Connect as many sales channels to your API key as you need.", "pages-add-sales-channels": "Add sales channels", "pages-edit-sales-channels": "Edit sales channels", "pages-success": "Success", "pages-created-a-new-api-key": "Created a new API key", "pages-error": "Error", "pages-failed-to-create-a-new-api-key": "Failed to create a new API key", "pages-sales-channels-added-to-the-scope": "Sales channels added to the scope", "pages-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "Error occurred while adding sales channels to the scope of the key", "pages-publish-api-key": "Publish API key", "pages-create-api-key": "Create API Key", "pages-create-and-manage-api-keys-right-now-this-is-only-related-to-sales-channels": "Create and manage API keys. Right now this is only related to sales channels.", "pages-create-api-key-label": "Create API key", "pages-back-to-settings": "Back to settings", "pages-publishable-api-keys": "Publishable API keys", "pages-these-publishable-keys-will-allow-you-to-authenticate-api-requests": "These publishable keys will allow you to authenticate API requests.", "tables-name": "Name", "tables-token": "Token", "tables-done": "done", "tables-copy-to-clipboard": "Copy to clipboard", "tables-created": "Created", "tables-status": "Status", "tables-revoked": "Revoked", "tables-live": "Live", "tables-edit-api-key-details": "Edit API key details", "tables-edit-sales-channels": "Edit sales channels", "tables-copy-token": "Copy token", "tables-revoke-token": "Revoke token", "tables-delete-api-key": "Delete API key", "tables-yes-delete": "Yes, delete", "tables-api-key-deleted": "API key deleted", "tables-are-you-sure-you-want-to-delete-this-public-key": "Are you sure you want to delete this public key?", "tables-delete-key": "Delete key", "tables-yes-revoke": "Yes, revoke", "tables-api-key-revoked": "API key revoked", "tables-are-you-sure-you-want-to-revoke-this-public-key": "Are you sure you want to revoke this public key?", "tables-revoke-key": "Revoke key", "tables-api-keys": "API Keys", "tables-no-keys-yet-use-the-above-button-to-create-your-first-publishable-key": "No keys yet, use the above button to create your first publishable key", "tables-title": "Title", "tables-description": "Description", "tables-no-added-sales-channels": "No added sales channels", "tables-sales-channels": "Sales Channels", "form-title": "Title", "form-website-app-amazon-physical-store-pos-facebook-product-feed": "Website, app, Amazon, physical store POS, facebook product feed...", "form-description": "Description", "form-available-products-at-our-website-app": "Available products at our website, app...", "form-success": "Success", "form-the-sales-channel-is-successfully-updated": "The sales channel is successfully updated", "form-error": "Error", "form-failed-to-update-the-sales-channel": "Failed to update the sales channel", "form-sales-channel-details": "Sales channel details", "form-general-info": "General info", "form-name": "Name", "form-close": "Close", "form-save": "Save", "pages-draft": "Draft", "pages-control-which-products-are-available-in-which-channels": "Control which products are available in which channels", "pages-search-by-title-or-description": "Search by title or description", "pages-confirm-delete-sales-channel": "Are you sure you want to delete this sales channel? The setup you made will be gone forever.", "pages-delete-channel-heading": "Delete Channel", "pages-edit-general-info": "Edit general info", "pages-add-products": "Add products", "pages-delete-channel": "Delete channel", "pages-disabled": "Disabled", "pages-enabled": "Enabled", "tables-collection": "Collection", "tables-start-building-your-channels-setup": "Start building your channels setup...", "tables-no-products-in-channels": "You haven’t added any products to this channels yet, but once you do they will live here.", "tables-add-products": "Add products", "tables-details": "Details", "tables-remove-from-the-channel": "Remove from the channel", "tables-products": "Products", "sales-channels-table-placeholder-selected-with-counts_one": "{{count}}", "sales-channels-table-placeholder-selected-with-counts_other": "{{count}}", "tables-remove": "Remove", "components-successfully-updated-currency": "Successfully updated currency", "components-default": "<PERSON><PERSON><PERSON>", "default-store-currency-success": "Success", "default-store-currency-successfully-updated-default-currency": "Successfully updated default currency", "default-store-currency-error": "Error", "default-store-currency-default-store-currency": "Default store currency", "default-store-currency-this-is-the-currency-your-prices-are-shown-in": "This is the currency your prices are shown in.", "store-currencies-success": "Success", "store-currencies-successfully-updated-currencies": "Successfully updated currencies", "store-currencies-error": "Error", "store-currencies-cancel": "Cancel", "store-currencies-save-and-go-back": "Save and go back", "store-currencies-save-and-close": "Save and close", "store-currencies-add-store-currencies": "Add Store Currencies", "store-currencies-current-store-currencies": "Current Store Currencies", "store-currencies-close": "Close", "current-currencies-screen-selected-with-count_one": "{{count}}", "current-currencies-screen-selected-with-count_other": "{{count}}", "store-currencies-deselect": "Deselect", "store-currencies-remove": "Remove", "store-currencies-add-currencies": "Add Currencies", "store-currencies-store-currencies": "Store currencies", "store-currencies-all-the-currencies-available-in-your-store": "All the currencies available in your store.", "store-currencies-edit-currencies": "Edit currencies", "currencies-an-unknown-error-occurred": "An unknown error occurred", "currencies-error": "Error", "currencies-back-to-settings": "Back to Settings", "currencies-manage-the-markets-that-you-will-operate-within": "Manage the markets that you will operate within.", "currencies-include-or-exclude-taxes": "Decide if you want to include or exclude taxes whenever you define a price in this currency", "currencies-tax-incl-prices": "Tax Incl. Prices", "settings-error": "Error", "settings-malformed-swap-url": "Malformed swap url", "settings-malformed-payment-url": "Malformed payment url", "settings-malformed-invite-url": "Malformed invite url", "settings-success": "Success", "settings-successfully-updated-store": "Successfully updated store", "settings-back-to-settings": "Back to settings", "settings-save": "Save", "settings-cancel": "Cancel", "settings-store-details": "Store Details", "settings-manage-your-business-details": "Manage your business details", "settings-general": "General", "settings-store-name": "Store name", "settings-medusa-store": "Medusa Store", "settings-advanced-settings": "Advanced settings", "settings-swap-link-template": "Swap link template", "settings-draft-order-link-template": "Draft order link template", "settings-invite-link-template": "Invite link template", "settings-manage-the-general-settings-for-your-store": "Manage the general settings for your store", "settings-manage-the-settings-for-your-store-apos-s-extensions": "Manage the settings for your store extensions", "edit-user-information-success": "Success", "edit-user-information-your-information-was-successfully-updated": "Your information was successfully updated", "edit-user-information-edit-information": "Edit information", "edit-user-information-cancel": "Cancel", "edit-user-information-submit-and-close": "Submit and close", "personal-information-back-to-settings": "Back to Settings", "personal-information-personal-information": "Personal information", "personal-information-manage-your-medusa-profile": "Manage your Medusa profile", "personal-information-language-settings-title": "Language", "personal-information-language-settings-description": "Adjust the language of Medusa Admin", "personal-information-language-settings-help-us-translate": "Help us translate", "personal-information-usage-insights-title": "Usage insights", "usage-insights-disabled": "Disabled", "usage-insights-active": "Active", "usage-insights-share-usage-insights-and-help-us-improve-medusa": "Share usage insights and help us improve Medusa", "usage-insights-edit-preferences": "Edit preferences", "usage-insights-success": "Success", "usage-insights-your-information-was-successfully-updated": "Your information was successfully updated", "usage-insights-error": "Error", "usage-insights-cancel": "Cancel", "usage-insights-submit-and-close": "Submit and close", "region-form-title": "Title", "region-form-europe": "Europe", "region-form-currency-code-is-required": "Currency code is required", "region-form-currency": "<PERSON><PERSON><PERSON><PERSON>", "region-form-choose-currency": "Choose currency", "region-form-default-tax-rate": "Default Tax Rate", "region-form-tax-rate-is-required": "Tax rate is required", "region-form-tax-rate-must-be-equal-to-or-less-than-100": "Tax rate must be equal to or less than 100", "region-form-default-tax-code": "Default Tax Code", "region-form-countries": "Countries", "region-form-choose-countries": "Choose countries", "region-form-tax-inclusive-prices": "Tax inclusive prices", "region-form-when-enabled-region-prices-will-be-tax-inclusive": "When enabled region prices will be tax inclusive.", "region-form-payment-providers-are-required": "Payment providers are required", "region-form-payment-providers": "Payment Providers", "region-form-choose-payment-providers": "Choose payment providers...", "region-form-fulfillment-providers-are-required": "Fulfillment providers are required", "region-form-fulfillment-providers": "Fulfillment Providers", "region-form-choose-fulfillment-providers": "Choose fulfillment providers...", "shipping-option-card-success": "Success", "shipping-option-card-shipping-option-updated": "Shipping option updated", "shipping-option-card-error": "Error", "shipping-option-card-edit-shipping-option": "Edit Shipping Option", "shipping-option-card-fulfillment-method": "Fulfillment Method", "shipping-option-card-cancel": "Cancel", "shipping-option-card-save-and-close": "Save and close", "shipping-option-card-shipping-option-has-been-deleted": "Shipping option has been deleted", "shipping-option-card-flat-rate": "Flat Rate", "shipping-option-card-calcualted": "Calculated", "shipping-option-card-min-subtotal": "Min. subtotal:", "shipping-option-card-max-subtotal": "Max. subtotal:", "shipping-option-card-admin": "Admin", "shipping-option-card-store": "Store", "shipping-option-card-edit": "Edit", "shipping-option-card-delete": "Delete", "shipping-option-form-visible-in-store": "Visible in store", "shipping-option-form-enable-or-disable-the-shipping-option-visiblity-in-store": "Enable or disable the shipping option visiblity in store.", "shipping-option-form-details": "Details", "shipping-option-form-title": "Title", "shipping-option-form-title-is-required": "Title is required", "shipping-option-form-price-type": "Price Type", "shipping-option-form-flat-rate": "Flat Rate", "shipping-option-form-calculated": "Calculated", "shipping-option-form-choose-a-price-type": "Choose a price type", "shipping-option-form-price": "Price", "shipping-option-form-shipping-profile": "Shipping Profile", "shipping-option-form-choose-a-shipping-profile": "Choose a shipping profile", "shipping-option-form-fulfillment-method": "Fulfillment Method", "shipping-option-form-choose-a-fulfillment-method": "Choose a fulfillment method", "shipping-option-form-requirements": "Requirements", "shipping-option-form-min-subtotal-must-be-less-than-max-subtotal": "Min. subtotal must be less than max. subtotal", "shipping-option-form-min-subtotal": "Min. subtotal", "shipping-option-form-max-subtotal": "Max. subtotal", "shipping-option-form-metadata": "<PERSON><PERSON><PERSON>", "general-section-success": "Success", "general-section-region-was-successfully-updated": "Region was successfully updated", "general-section-error": "Error", "general-section-edit-region-details": "Edit Region Details", "general-section-details": "Details", "general-section-providers": "Providers", "general-section-metadata": "<PERSON><PERSON><PERSON>", "general-section-cancel": "Cancel", "general-section-save-and-close": "Save and close", "edit-something-went-wrong": "Something went wrong...", "edit-no-region-found": "We can't find a region with that ID, use the menu to the left to select a region.", "return-shipping-options-success": "Success", "return-shipping-options-shipping-option-created": "Shipping option created", "return-shipping-options-error": "Error", "return-shipping-options-add-return-shipping-option": "Add Return Shipping Option", "return-shipping-options-cancel": "Cancel", "return-shipping-options-save-and-close": "Save and close", "return-shipping-options-return-shipping-options": "Return Shipping Options", "return-shipping-options-add-option": "Add Option", "return-shipping-options-enter-specifics-about-available-regional-return-shipment-methods": "Enter specifics about available regional return shipment methods.", "shipping-options-success": "Success", "shipping-options-shipping-option-created": "Shipping option created", "shipping-options-error": "Error", "shipping-options-add-shipping-option": "Add Shipping Option", "shipping-options-cancel": "Cancel", "shipping-options-save-and-close": "Save and close", "shipping-options-shipping-options": "Shipping Options", "shipping-options-add-option": "Add Option", "shipping-options-enter-specifics-about-available-regional-shipment-methods": "Enter specifics about available regional shipment methods.", "new-region-created": "Region created", "new-create-region": "Create region", "new-details": "Details", "new-add-the-region-details": "Add the region details.", "new-providers": "Providers", "new-add-which-fulfillment-and-payment-providers-should-be-available-in-this-region": "Add which fulfillment and payment providers should be available in this region.", "region-overview-regions": "Regions", "region-overview-manage-the-markets-that-you-will-operate-within": "Manage the markets that you will operate within.", "region-overview-not-configured": "Not configured", "region-overview-fulfillment-providers": "Fulfillment providers:", "return-reasons-notification-success": "Success", "return-reasons-created-a-new-return-reason": "Created a new return reason", "return-reasons-success": "success", "return-reasons-error": "Error", "return-reasons-cannot-create-a-return-reason-with-an-existing-value": "Cannot create a return reason with an existing value", "return-reasons-add-reason": "Add Reason", "return-reasons-value-is-required": "Value is required", "return-reasons-value": "Value", "return-reasons-label-is-required": "Label is required", "return-reasons-label": "Label", "return-reasons-description": "Description", "return-reasons-customer-received-the-wrong-size": "Customer received the wrong size", "return-reasons-cancel": "Cancel", "return-reasons-create": "Create", "return-reasons-success-title": "Success", "return-reasons-successfully-updated-return-reason": "Successfully updated return reason", "return-reasons-duplicate-reason": "Duplicate reason", "return-reasons-delete-reason": "Delete reason", "return-reasons-save": "Save", "return-reasons-details": "Details", "return-reasons-delete-return-reason": "Delete Return Reason", "return-reasons-are-you-sure-you-want-to-delete-this-return-reason": "Are you sure you want to delete this return reason?", "return-reasons-back-to-settings": "Back to settings", "return-reasons-return-reasons": "Return Reasons", "return-reasons-add-reason-label": "Add reason", "return-reasons-manage-reasons-for-returned-items": "Manage reasons for returned items", "taxes-details": "Details", "taxes-new-tax-rate": "New Tax Rate", "taxes-tax-calculation-settings": "Tax Calculation Settings", "taxes-success": "Success", "taxes-successfully-updated-tax-rate": "Successfully updated Tax Rate.", "taxes-error": "Error", "taxes-overrides": "Overrides", "taxes-product-rules": "Product Rules", "taxes-product-rules-description_one": "Applies to {{count}} productWithCount", "taxes-product-rules-description_other": "Applies to {{count}} productWithCount", "taxes-product-type-rules": "Product Type Rules", "taxes-product-type-rules-description_one": "Applies to {{count}} product typeWithCount", "taxes-product-type-rules-description_other": "Applies to {{count}} product typeWithCount", "taxes-shipping-option-rules": "Shipping Option Rules", "taxes-applies-to-shipping-option-with-count_one": "Applies to {{count}} shipping optionWithCount", "taxes-applies-to-shipping-option-with-count_other": "Applies to {{count}} shipping optionWithCount", "taxes-add-overrides": "Add Overrides", "taxes-cancel": "Cancel", "taxes-save": "Save", "taxes-name": "Name", "taxes-default": "<PERSON><PERSON><PERSON>", "taxes-rate-name": "Rate name", "taxes-tax-rate": "Tax Rate", "taxes-tax-code": "Tax Code", "taxes-edit-tax-rate": "Edit Tax Rate", "taxes-back-to-settings": "Back to settings", "taxes-regions": "Regions", "taxes-select-the-region-you-wish-to-manage-taxes-for": "Select the region you wish to manage taxes for", "taxes-go-to-region-settings": "Go to Region settings", "taxes-successfully-created-tax-rate": "Successfully created tax rate.", "taxes-add-tax-rate": "Add Tax Rate", "taxes-applies-to-product-type-with-count_one": "Applies to {{count}} product typeWithCount", "taxes-applies-to-product-type-with-count_other": "Applies to {{count}} product typeWithCount", "taxes-create": "Create", "taxes-select-products": "Select Products", "taxes-select-product-types-label": "Select Product Types", "taxes-product-types": "Product Types", "taxes-system-tax-provider": "System Tax Provider", "taxes-region-tax-settings-were-successfully-updated": "Region tax settings were successfully updated.", "taxes-tax-provider": "Tax Provider", "taxes-calculate-taxes-automatically": "Calculate taxes automatically?", "taxes-automatically-apply-tax-calculations-to-carts": "When checked Medusa will automatically apply tax calculations to Carts in this Region. When unchecked you will have to manually compute taxes at checkout. Manual taxes are recommended if using a 3rd party tax provider to avoid performing too many requests", "taxes-apply-tax-to-gift-cards": "Apply tax to gift cards?", "taxes-apply-taxes-to-gift-cards": "When checked taxes will be applied to gift cards on checkout. In some contries tax regulations require that taxes are applied to gift cards on purchase.", "taxes-search-products": "Search Products..", "taxes-select-shipping-option": "Select Shipping Option", "taxes-shipping-options": "Shipping Options", "taxes-delete-tax-rate-heading": "Delete tax rate", "taxes-confirm-delete": "Are you sure you want to delete this tax rate?", "taxes-tax-rate-was-deleted": "Tax rate was deleted.", "taxes-edit": "Edit", "taxes-delete-tax-rate": "Delete Tax Rate", "taxes-delete-rule": "Delete rule", "taxes-type": "Type", "taxes-products": "Products", "taxes-select-individual-products": "Select individual products", "taxes-select-product-types": "Select product types", "taxes-select-shipping-options": "Select shipping options", "taxes-back": "Back", "taxes-add": "Add", "taxes-code": "Code", "users-invite-users": "Invite Users", "users-back-to-settings": "Back to settings", "users-the-team": "The Team", "users-manage-users-of-your-medusa-store": "Manage users of your Medusa Store", "users-count_one": "{{count}}", "users-count_other": "{{count}}"}