{"back-button-go-back": "Voltar", "sales-channels-display-available-count": "Disponível em <2>{{availableChannelsCount}}</2> de um total de <6>{{totalChannelsCount}}</6> Canais de Vendas", "activity-drawer-activity": "Atividade", "activity-drawer-no-notifications-title": "Está tranquilo aqui...", "activity-drawer-no-notifications-description": "Você não possui notificações no momento, mas assim que tiver, elas aparecerão aqui.", "activity-drawer-error-title": "Oh não...", "activity-drawer-error-description": "Algo deu errado ao tentar buscar suas notificações - Vamos continuar tentando!", "activity-drawer-processing": "Processando...", "analytics-config-form-title": "Tornar meus dados de uso anônimos", "analytics-config-form-description": "Você pode escolher por tornar seus dados de uso anônimos. Se esta opção estiver selecionada, não coletaremos suas informações pessoais, como seu nome e endereço de e-mail.", "analytics-config-form-opt-out": "Optar por não compartilhar meus dados de uso", "analytics-config-form-opt-out-later": "Você sempre pode optar por não compartilhar seus dados de uso a qualquer momento.", "analytics-preferences-success": "Sucesso", "analytics-preferences-your-preferences-were-successfully-updated": "Suas preferências foram atualizadas com sucesso", "analytics-preferences-error": "Erro", "analytics-preferences-help-us-get-better": "Nos ajude a melhorar", "analytics-preferences-disclaimer": "Para criar a experiência de e-commerce mais cativante, gostaríamos de obter insights sobre como você usa o Medusa. Insights do usuário nos permitem construir produtos melhores, mais envolventes e mais usáveis. Coletamos dados apenas para melhorias no produto. Leia quais dados coletamos em nossa", "analytics-preferences-documentation": "documentação", "analytics-preferences-please-enter-a-valid-email": "Por favor, insira um e-mail válido", "analytics-preferences-continue": "<PERSON><PERSON><PERSON><PERSON>", "currency-input-currency": "<PERSON><PERSON>", "currency-input-amount-is-not-valid": "Valor não é válido", "organisms-success": "Sucesso", "organisms-delete-successful": "Exclusão bem-sucedida", "organisms-are-you-sure-you-want-to-delete": "Você tem certeza de que deseja excluir?", "organisms-no-cancel": "Não, cancelar", "organisms-yes-remove": "<PERSON>m, remover", "details-collapsible-hide-additional-details": "O<PERSON>ltar detalhes adicionais", "details-collapsible-show-additional-details": "<PERSON><PERSON> detalhes adicionais", "edit-user-modal-success": "Sucesso", "edit-user-modal-user-was-updated": "<PERSON><PERSON><PERSON><PERSON>", "edit-user-modal-error": "Erro", "edit-user-modal-edit-user": "<PERSON><PERSON>", "edit-user-modal-first-name-label": "Primeiro Nome", "edit-user-modal-first-name-placeholder": "Primeiro nome...", "edit-user-modal-last-name-label": "Sobrenome", "edit-user-modal-last-name-placeholder": "Sobrenome...", "edit-user-modal-email": "E-mail", "edit-user-modal-cancel": "<PERSON><PERSON><PERSON>", "edit-user-modal-save": "<PERSON><PERSON>", "error-boundary-back-to-dashboard": "Voltar ao dashboard", "error-boundary-an-unknown-error-occurred": "Ocorreu um erro desconhecido", "error-boundary-bad-request": "Requisição inválida", "error-boundary-you-are-not-logged-in": "Você não está logado", "error-boundary-you-do-not-have-permission-perform-this-action": "Você não tem permissão para realizar esta ação", "error-boundary-page-was-not-found": "Página não encontrada", "error-boundary-an-unknown-server-error-occurred": "Ocorreu um erro desconhecido no servidor", "error-boundary-503": "O servidor está indisponível no nomento", "error-boundary-500": "Ocorreu um erro com causas não especificadas, provavelmente devido a um problema técnico em nosso sistema. Por favor, tente atualizar a página. Se o problema persistir, entre em contato com o administrador.", "error-boundary-400": "A requisição estava malformada, corrija sua requisição e tente novamente.", "error-boundary-401": "Você não está logado, faça login para continuar.", "error-boundary-403": "Você não tem permissão para realizar esta ação. Se acredita que isso seja um erro, entre em contato com o administrador.", "error-boundary-404": "A página que você solicitou não foi encontrada. Verifique a URL e tente novamente.", "error-boundary-500-2": "O servidor não conseguiu lidar com sua solicitação, provavelmente devido a um problema técnico em nosso sistema. Por favor, tente novamente. Se o problema persistir, entre em contato com o administrador.", "error-boundary-503-2": "O servidor está temporariamente indisponível e sua solicitação não pôde ser processada. Por favor, tente novamente mais tarde. Se o problema persistir, entre em contato com o administrador.", "export-modal-title": "Iniciar uma exportação de seus dados", "export-modal-cancel": "<PERSON><PERSON><PERSON>", "export-modal-export": "Exportar", "file-upload-modal-upload-a-new-photo": "Carregar uma nova foto", "gift-card-banner-edit": "<PERSON><PERSON>", "gift-card-banner-unpublish": "Despublicar", "gift-card-banner-publish": "Publicar", "gift-card-banner-delete": "Excluir", "gift-card-banner-published": "Publicado", "gift-card-banner-unpublished": "Não publicado", "gift-card-denominations-section-denomination-added": "Denominação adicionada", "gift-card-denominations-section-a-new-denomination-was-successfully-added": "Uma nova denominação foi adicionada com sucesso", "gift-card-denominations-section-a-denomination-with-that-default-value-already-exists": "Já existe uma denominação com esse valor padrão", "gift-card-denominations-section-error": "Erro", "gift-card-denominations-section-add-denomination": "Adicionar <PERSON>", "gift-card-denominations-section-cancel": "<PERSON><PERSON><PERSON>", "gift-card-denominations-section-save-and-close": "<PERSON>var e fechar", "gift-card-denominations-section-denomination-updated": "Denominação atualizada", "gift-card-denominations-section-a-new-denomination-was-successfully-updated": "Uma nova denominação foi atualizada com sucesso", "gift-card-denominations-section-edit-denomination": "Editar <PERSON>", "gift-card-denominations-section-denominations": "Denominações", "gift-card-denominations-section-denomination": "Denominação", "gift-card-denominations-section-in-other-currencies": "Em <PERSON>ras moedas", "gift-card-denominations-section-and-more_one": ", e mais {{count}}", "gift-card-denominations-section-and-more_other": ", e mais {{count}}", "gift-card-denominations-section-delete-denomination": "Excluir denominação", "gift-card-denominations-section-confirm-delete": "Tem certeza de que deseja excluir esta denominação?", "gift-card-denominations-section-denomination-deleted": "Denominação excluída", "gift-card-denominations-section-denomination-was-successfully-deleted": "A denominação foi excluída com sucesso", "gift-card-denominations-section-edit": "<PERSON><PERSON>", "gift-card-denominations-section-delete": "Excluir", "help-dialog-how-can-we-help": "Como podemos ajudar?", "help-dialog-we-usually-respond-in-a-few-hours": "Normalmente respondemos em poucas horas", "help-dialog-subject": "<PERSON><PERSON><PERSON>", "help-dialog-what-is-it-about": "Sobre o que é isso?...", "help-dialog-write-a-message": "Escreva uma mensagem...", "help-dialog-feel-free-to-join-our-community-of": "Sinta-se à vontade para se juntar à nossa comunidade de", "help-dialog-merchants-and-e-commerce-developers": "comerciantes e desenvolvedores de e-commerce", "help-dialog-send-a-message": "Enviar uma mensagem", "invite-modal-success": "Sucesso", "invite-modal-invitation-sent-to": "Convite enviado para {{user}}", "invite-modal-error": "Erro", "invite-modal-member": "Membro", "invite-modal-admin": "Administrador", "invite-modal-developer": "<PERSON><PERSON><PERSON><PERSON>", "invite-modal-invite-users": "<PERSON><PERSON><PERSON>", "invite-modal-email": "E-mail", "invite-modal-role": "Função", "invite-modal-select-role": "Selecione a função", "invite-modal-cancel": "<PERSON><PERSON><PERSON>", "invite-modal-invite": "<PERSON><PERSON><PERSON>", "login-card-no-match": "Essas credenciais não correspondem aos nossos registros.", "login-card-log-in-to-medusa": "Entrar no Medusa", "login-card-email": "E-mail", "login-card-password": "<PERSON><PERSON>", "login-card-forgot-your-password": "Esqueceu sua senha?", "metadata-add-metadata": "<PERSON><PERSON><PERSON><PERSON>", "product-attributes-section-title": "Atributos", "product-attributes-section-edit-attributes": "<PERSON><PERSON>", "product-attributes-section-dimensions": "Dimensões", "product-attributes-section-configure-to-calculate-the-most-accurate-shipping-rates": "Configure para calcular as taxas de envio mais precisas", "product-attributes-section-customs": "Alfândegas", "product-attributes-section-cancel": "<PERSON><PERSON><PERSON>", "product-attributes-section-save": "<PERSON><PERSON>", "product-attributes-section-height": "Altura", "product-attributes-section-width": "<PERSON><PERSON><PERSON>", "product-attributes-section-length": "Comprimento", "product-attributes-section-weight": "Peso", "product-attributes-section-mid-code": "Código <PERSON>", "product-attributes-section-hs-code": "Código HS", "product-attributes-section-country-of-origin": "<PERSON><PERSON> or<PERSON>", "product-general-section-success": "Sucesso", "product-general-section-successfully-updated-sales-channels": "Atualização dos canais de vendas realizada com sucesso", "product-general-section-error": "Erro", "product-general-section-failed-to-update-sales-channels": "Falha ao atualizar os canais de vendas", "product-general-section-edit-general-information": "Editar Informações Gerais", "product-general-section-gift-card": "Cartão de Presente", "product-general-section-product": "Produ<PERSON>", "product-general-section-metadata": "Metadados", "product-general-section-cancel": "<PERSON><PERSON><PERSON>", "product-general-section-save": "<PERSON><PERSON>", "product-general-section-delete": "Excluir", "product-general-section-edit-sales-channels": "Editar Canais de Vendas", "product-general-section-published": "Publicado", "product-general-section-draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product-general-section-details": "<PERSON><PERSON><PERSON>", "product-general-section-subtitle": "Subtítulo", "product-general-section-handle": "Identificador", "product-general-section-type": "Tipo", "product-general-section-collection": "Coleção", "product-general-section-category": "Categoria", "product-general-section-discountable": "<PERSON><PERSON>t<PERSON>vel", "product-general-section-true": "<PERSON><PERSON><PERSON><PERSON>", "product-general-section-false": "<PERSON><PERSON><PERSON>", "product-general-section-count_one": "{{count}}", "product-general-section-count_other": "{{count}}", "product-general-section-sales-channels": "Canais de Vendas", "product-media-section-edit-media": "<PERSON><PERSON>", "product-media-section-upload-images-error": "Ocorreu um erro ao tentar carregar as imagens.", "product-media-section-file-service-not-configured": "Você pode não ter um serviço de arquivo configurado. Por favor, entre em contato com o administrador.", "product-media-section-error": "Erro", "product-media-section-media": "Mí<PERSON>", "product-media-section-add-images-to-your-product": "Adicione imagens ao seu produto.", "product-media-section-cancel": "<PERSON><PERSON><PERSON>", "product-media-section-save-and-close": "<PERSON>var e fechar", "product-raw-section-raw-gift-card": "Cartão de Presente Bruto", "product-raw-section-raw-product": "<PERSON><PERSON><PERSON>", "product-thumbnail-section-success": "Sucesso", "product-thumbnail-section-successfully-deleted-thumbnail": "Miniatura excluída com sucesso", "product-thumbnail-section-error": "Erro", "product-thumbnail-section-edit": "<PERSON><PERSON>", "product-thumbnail-section-upload": "<PERSON><PERSON><PERSON>", "product-thumbnail-section-upload-thumbnail-error": "Ocorreu um erro ao tentar carregar a miniatura.", "product-thumbnail-section-you-might-not-have-a-file-service-configured-please-contact-your-administrator": "Você pode não ter um serviço de arquivo configurado. Por favor, entre em contato com o administrador.", "product-thumbnail-section-upload-thumbnail": "Carregar Miniatura", "product-thumbnail-section-thumbnail": "Miniatura", "product-thumbnail-section-used-to-represent-your-product-during-checkout-social-sharing-and-more": "Usada para representar seu produto durante o checkout, compartilhamento nas redes sociais e mais.", "product-thumbnail-section-cancel": "<PERSON><PERSON><PERSON>", "product-thumbnail-section-save-and-close": "<PERSON>var e fechar", "product-variant-tree-count_one": "{{count}}", "product-variant-tree-count_other": "{{count}}", "product-variant-tree-add-prices": "<PERSON><PERSON><PERSON><PERSON>", "product-variants-section-add-variant": "<PERSON><PERSON><PERSON><PERSON>", "product-variants-section-cancel": "<PERSON><PERSON><PERSON>", "product-variants-section-save-and-close": "<PERSON>var e fechar", "product-variants-section-edit-stock-inventory": "Editar estoque e inventário", "product-variants-section-edit-variant": "<PERSON><PERSON>", "edit-variants-modal-cancel": "<PERSON><PERSON><PERSON>", "edit-variants-modal-save-and-go-back": "Salvar e voltar", "edit-variants-modal-save-and-close": "<PERSON>var e fechar", "edit-variants-modal-edit-variant": "<PERSON><PERSON>", "edit-variants-modal-update-success": "Variantes foram atualizadas com sucesso", "edit-variants-modal-edit-variants": "<PERSON><PERSON>", "edit-variants-modal-product-variants": "V<PERSON>tes de Produto", "edit-variants-modal-variant": "<PERSON><PERSON><PERSON>", "edit-variants-modal-inventory": "Inventário", "product-variants-section-edit-prices": "<PERSON><PERSON>", "product-variants-section-edit-variants": "<PERSON><PERSON>", "product-variants-section-edit-options": "Editar <PERSON>", "product-variants-section-product-variants": "V<PERSON>tes de Produto", "product-variants-section-error": "Erro", "product-variants-section-failed-to-update-product-options": "Falha ao atualizar as opções do produto", "product-variants-section-success": "Sucesso", "product-variants-section-successfully-updated-product-options": "Opções do produto atualizadas com sucesso", "product-variants-section-product-options": "Opções do Produto", "product-variants-section-option-title": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>ção", "product-variants-section-option-title-is-required": "O título da opção é obrigatório", "product-variants-section-add-an-option": "Adicionar uma opção", "product-variants-section-inventory": "Inventário", "product-variants-section-title": "<PERSON><PERSON><PERSON><PERSON>", "product-variants-section-sku": "SKU", "product-variants-section-ean": "EAN", "product-variants-section-manage-inventory": "Gerenciar Inventário", "product-variants-section-duplicate-variant": "<PERSON><PERSON><PERSON><PERSON>", "product-variants-section-delete-variant-label": "Excluir <PERSON>", "product-variants-section-yes-delete": "Sim, excluir", "product-variants-section-delete-variant-heading": "Excluir variante", "product-variants-section-confirm-delete": "Você tem certeza de que deseja excluir esta variante? ", "product-variants-section-note-deleting-the-variant-will-also-remove-inventory-items-and-levels": " Observação: A exclusão da variante também removerá os itens e níveis do inventário", "reset-token-card-error": "Erro", "reset-token-card-reset-your-password": "Redefina sua senha", "reset-token-card-password-reset-description": "Insira seu endereço de e-mail abaixo e nós<1></1>enviaremos instruções sobre como redefinir<3></3>sua senha.", "reset-token-card-email": "E-mail", "reset-token-card-this-is-not-a-valid-email": "Este não é um e-mail válido", "reset-token-card-send-reset-instructions": "Enviar instruções de redefinição", "reset-token-card-successfully-sent-you-an-email": "Enviamos com sucesso um e-mail para você", "reset-token-card-go-back-to-sign-in": "Voltar para a tela de login", "rma-return-product-table-product-details": "Detalhes do Produto", "rma-return-product-table-quantity": "Quantidade", "rma-select-product-table-product-details": "Detalhes do Produto", "rma-select-product-table-quantity": "Quantidade", "rma-select-product-table-refundable": "Reembolsável", "rma-select-product-table-images-witch-count_one": "{{count}} imagem", "rma-select-product-table-images-witch-count_other": "{{count}} imagens", "rma-select-product-table-select-reason": "Selecione o Motivo", "sidebar-store": "<PERSON><PERSON>", "sidebar-orders": "Pedidos", "sidebar-products": "<PERSON><PERSON><PERSON>", "sidebar-categories": "Categorias", "sidebar-customers": "Clientes", "sidebar-inventory": "Inventário", "sidebar-discounts": "Descontos", "sidebar-gift-cards": "Car<PERSON><PERSON><PERSON> Presente", "sidebar-pricing": "Preços", "sidebar-settings": "Configurações", "table-container-soothed-offset_one": "{{soothedOffset}} - {{pageSize}} de {{count}} {{title}}", "table-container-soothed-offset_other": "{{soothedOffset}} - {{pageSize}} de {{count}} {{title}}", "table-container-current-page": "{{currentPage}} de {{soothedPageCount}}", "timeline-request-return": "Solicitar Devolução", "timeline-register-exchange": "Registrar Troca", "timeline-register-claim": "Registrar Reclam<PERSON>ção", "timeline-success": "Sucesso", "timeline-added-note": "Nota adicionada", "timeline-error": "Erro", "timeline-timeline": "Linha do Tempo", "upload-modal-new": "novo", "upload-modal-updates": "atualizações", "upload-modal-drop-your-file-here-or": "Solte seu arquivo aqui ou", "upload-modal-click-to-browse": "clique para navegar.", "upload-modal-only-csv-files-are-supported": "Apenas arquivos .csv são suportados.", "upload-modal-import-file-title": "Importar {{fileTitle}}", "upload-modal-cancel": "<PERSON><PERSON><PERSON>", "upload-modal-import-list": "Importar Lista", "add-products-modal-add-products": "<PERSON><PERSON><PERSON><PERSON>", "add-products-modal-search-by-name-or-description": "Pesquisar por nome ou descrição...", "add-products-modal-cancel": "<PERSON><PERSON><PERSON>", "add-products-modal-save": "<PERSON><PERSON>", "add-products-modal-product-details": "Detalhes do Produto", "add-products-modal-status": "Status", "add-products-modal-variants": "<PERSON><PERSON><PERSON>", "templates-general": "G<PERSON>", "templates-first-name": "Nome", "templates-last-name": "Sobrenome", "templates-company": "Empresa", "templates-phone": "Telefone", "templates-billing-address": "Endereço de Cobrança", "templates-shipping-address": "Endereço de Envio", "templates-address": "Endereço", "templates-address-1": "Endereço 1", "templates-address-2": "Endereço 2", "templates-postal-code": "Código Postal", "templates-city": "Cidade", "templates-province": "Estado", "templates-country": "<PERSON><PERSON>", "templates-metadata": "Metadados", "collection-modal-success": "Sucesso", "collection-modal-successfully-updated-collection": "Coleção atualizada com sucesso", "collection-modal-error": "Erro", "collection-modal-successfully-created-collection": "Coleção criada com sucesso", "collection-modal-edit-collection": "<PERSON><PERSON>", "collection-modal-add-collection": "<PERSON><PERSON><PERSON><PERSON>", "collection-modal-description": "Para criar uma coleção, tudo o que você precisa é de um título e um identificador.", "collection-modal-details": "<PERSON><PERSON><PERSON>", "collection-modal-title-label": "<PERSON><PERSON><PERSON><PERSON>", "collection-modal-title-placeholder": "Ó<PERSON><PERSON> de <PERSON>", "collection-modal-handle-label": "Identificador", "collection-modal-handle-placeholder": "oculos-de-sol", "collection-modal-slug-description": "URL Slug para a coleção. Será gerado automaticamente se deixado em branco.", "collection-modal-metadata": "Metadados", "collection-modal-cancel": "<PERSON><PERSON><PERSON>", "collection-modal-save-collection": "<PERSON><PERSON>", "collection-modal-publish-collection": "Publicar coleção", "collection-product-table-add-products": "<PERSON><PERSON><PERSON><PERSON>", "collection-product-table-products": "<PERSON><PERSON><PERSON>", "collection-product-table-search-products": "<PERSON><PERSON><PERSON><PERSON>", "collection-product-table-cancel": "<PERSON><PERSON><PERSON>", "collection-product-table-save": "<PERSON><PERSON>", "collection-product-table-sort-by": "Ordenar por", "collection-product-table-all": "Todos", "collection-product-table-newest": "<PERSON><PERSON>", "collection-product-table-oldest": "<PERSON><PERSON>", "collection-product-table-title": "<PERSON><PERSON><PERSON><PERSON>", "collection-product-table-decide-status-published": "Publicado", "collection-product-table-draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collection-product-table-proposed": "Proposto", "collection-product-table-rejected": "<PERSON><PERSON><PERSON><PERSON>", "collection-product-table-remove-product-from-collection": "Remover produto da coleção", "collection-product-table-product-removed-from-collection": "Produto removido da coleção", "collections-table-delete-collection": "Excluir <PERSON>", "collections-table-confirm-delete": "Tem certeza de que deseja excluir esta coleção?", "collections-table-edit": "<PERSON><PERSON>", "collections-table-delete": "Excluir", "collections-table-title": "<PERSON><PERSON><PERSON><PERSON>", "collections-table-handle": "Identificador", "collections-table-created-at": "C<PERSON><PERSON> em", "collections-table-updated-at": "Atualizado em", "collections-table-products": "<PERSON><PERSON><PERSON>", "customer-group-table-details": "<PERSON><PERSON><PERSON>", "customer-group-table-delete": "Excluir", "customer-group-table-success": "Sucesso", "customer-group-table-group-deleted": "Grupo excluído", "customer-group-table-error": "Erro", "customer-group-table-failed-to-delete-the-group": "Falha ao excluir o grupo", "customer-group-table-customer-groups": "Grupos de Clientes", "customer-group-table-delete-from-the-group": "Excluir do grupo", "customer-group-table-customer-groups-title": "Grupos de Clientes", "customer-group-table-groups": "Grupos", "customer-group-table-all": "Todos", "customer-group-table-edit-customers": "<PERSON><PERSON>", "customer-group-table-customers": "Clientes", "customer-group-table-cancel": "<PERSON><PERSON><PERSON>", "customer-group-table-save": "<PERSON><PERSON>", "customer-orders-table-orders": "Pedidos", "customer-orders-table-transfer-order": "Transferir pedido", "customer-orders-table-paid": "Pago", "customer-orders-table-awaiting": "Aguardando", "customer-orders-table-requires-action": "Requer <PERSON>", "customer-orders-table-n-a": "N/A", "customer-orders-table-fulfilled": "Atendido", "customer-orders-table-shipped": "Enviado", "customer-orders-table-not-fulfilled": "Não Atendido", "customer-orders-table-partially-fulfilled": "Parcialmente <PERSON>endido", "customer-orders-table-partially-shipped": "Parcialmente Enviado", "customer-orders-table-order": "Pedido", "customer-orders-table-remainder-more": "+ {{remainder}} mais", "customer-orders-table-date": "Data", "customer-orders-table-fulfillment": "Atendimento", "customer-orders-table-status": "Status", "customer-orders-table-total": "Total", "customer-table-customers": "Clientes", "customer-table-edit": "<PERSON><PERSON>", "customer-table-details": "<PERSON><PERSON><PERSON>", "customer-table-date-added": "Data de Adição", "customer-table-name": "Nome", "customer-table-email": "Email", "customer-table-orders": "Pedidos", "discount-filter-dropdown-filters": "<PERSON><PERSON><PERSON>", "discount-table-discounts": "Descontos", "discount-table-search-by-code-or-description": "Pesquisar por código ou descrição...", "discount-table-success": "Sucesso", "discount-table-successfully-copied-discount": "Desconto copiado com sucesso", "discount-table-error": "Erro", "discount-table-scheduled": "Agendado", "discount-table-expired": "<PERSON><PERSON><PERSON>", "discount-table-active": "Ativo", "discount-table-disabled": "Desativado", "discount-table-free-shipping": "Frete <PERSON>", "discount-table-code": "Código", "discount-table-description": "Descrição", "discount-table-amount": "Valor", "discount-table-status": "Status", "discount-table-redemptions": "Resgates", "discount-table-delete-discount": "Excluir Desconto", "discount-table-confirm-delete": "Tem certeza de que deseja excluir este desconto?", "discount-table-publish": "Publicar", "discount-table-unpublish": "Despublicar", "discount-table-successfully-published-discount": "Desconto publicado com sucesso", "discount-table-successfully-unpublished-discount": "Desconto despublicado com sucesso", "discount-table-duplicate": "Duplicar", "discount-table-delete": "Excluir", "draft-order-table-draft-orders": "Pedidos em Rascunho", "draft-order-table-completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draft-order-table-open": "Abe<PERSON>o", "draft-order-table-draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draft-order-table-order": "Pedido", "draft-order-table-date-added": "Data de Adição", "draft-order-table-customer": "Cliente", "draft-order-table-status": "Status", "gift-card-filter-dropdown-is-in-the-last": "está no último", "gift-card-filter-dropdown-is-older-than": "é mais antigo que", "gift-card-filter-dropdown-is-after": "é depois de", "gift-card-filter-dropdown-is-before": "é antes de", "gift-card-filter-dropdown-is-equal-to": "é igual a", "gift-card-filter-dropdown-filters": "<PERSON><PERSON><PERSON>", "gift-card-filter-dropdown-status": "Status", "gift-card-filter-dropdown-payment-status": "Status de Pagamento", "gift-card-filter-dropdown-fulfillment-status": "Status de Atendimento", "gift-card-filter-dropdown-date": "Data", "gift-card-table-gift-cards": "Car<PERSON><PERSON><PERSON> Presente", "gift-card-table-code": "Código", "gift-card-table-order": "Pedido", "gift-card-table-original-amount": "Valor Original", "gift-card-table-balance": "<PERSON><PERSON>", "gift-card-table-region-has-been-deleted": "Região foi excluída", "gift-card-table-none": "<PERSON><PERSON><PERSON>", "gift-card-table-created": "<PERSON><PERSON><PERSON>", "image-table-file-name": "Nome do Arquivo", "image-table-thumbnail": "Miniatura", "image-table-select-thumbnail-image-for-product": "Selecione qual imagem deseja usar como miniatura para este produto", "inventory-table-inventory-items": "Itens de Estoque", "inventory-table-actions-adjust-availability": "Ajustar Disponibilidade", "inventory-table-view-product": "<PERSON><PERSON>du<PERSON>", "inventory-table-success": "Sucesso", "inventory-table-inventory-item-updated-successfully": "Item de estoque atualizado com sucesso", "inventory-table-adjust-availability": "Ajustar disponibilidade", "inventory-table-cancel": "<PERSON><PERSON><PERSON>", "inventory-table-save-and-close": "<PERSON><PERSON> e <PERSON>char", "inventory-table-item": "<PERSON><PERSON>", "inventory-table-variant": "<PERSON><PERSON><PERSON>", "inventory-table-sku": "SKU", "inventory-table-reserved": "Reservado", "inventory-table-in-stock": "Em estoque", "order-filter-dropdown-filters": "<PERSON><PERSON><PERSON>", "order-filter-dropdown-status": "Status", "order-filter-dropdown-payment-status": "Status de pagamento", "order-filter-dropdown-fulfillment-status": "Status de atendimento", "order-filter-dropdown-regions": "Regiões", "order-filter-dropdown-sales-channel": "Canal de vendas", "order-filter-dropdown-date": "Data", "order-table-paid": "Pago", "order-table-awaiting": "Aguardando", "order-table-requires-action": "Requer ação", "order-table-canceled": "Cancelado", "order-table-n-a": "N/A", "order-table-order": "Pedido", "order-table-date-added": "Data de adição", "order-table-customer": "Cliente", "order-table-fulfillment": "Atendimento", "order-table-payment-status": "Status de pagamento", "order-table-sales-channel": "Canal de vendas", "order-table-total": "Total", "order-table-filters-complete": "Completo", "order-table-filters-incomplete": "Incompleto", "price-list-table-filters": "<PERSON><PERSON><PERSON>", "price-list-table-status": "Status", "price-list-table-type": "Tipo", "price-list-table-price-lists": "Listas de preços", "price-list-table-success": "Sucesso", "price-list-table-successfully-copied-price-list": "Lista de preços copiada com sucesso", "price-list-table-error": "Erro", "price-list-table-delete-price-list": "Excluir Lista de Preços", "price-list-table-confirm-delete": "Tem certeza de que deseja excluir esta lista de preços?", "price-list-table-successfully-deleted-the-price-list": "Lista de preços excluída com sucesso", "price-list-table-successfully-unpublished-price-list": "Lista de preços despublicada com sucesso", "price-list-table-successfully-published-price-list": "Lista de preços publicada com sucesso", "price-list-table-unpublish": "Despublicar", "price-list-table-publish": "Publicar", "price-list-table-delete": "Excluir", "price-list-table-name": "Nome", "price-list-table-description": "Descrição", "price-list-table-groups": "Grupos", "price-list-table-other-more": "+ {{other}} mais", "price-overrides-apply-overrides-on-selected-variants": "Aplicar substituições nas variantes selecionadas", "price-overrides-apply-on-all-variants": "Aplicar em todas as variantes", "price-overrides-prices": "Preços", "price-overrides-cancel": "<PERSON><PERSON><PERSON>", "price-overrides-save-and-close": "<PERSON>var e fechar", "price-overrides-show-regions": "Mostrar regiões", "product-table-products": "<PERSON><PERSON><PERSON>", "product-table-copy-success": "Sucesso", "product-table-copy-created-a-new-product": "Criou um novo produto", "product-table-copy-error": "Erro", "product-table-delete-product": "Excluir Produto", "product-table-confirm-delete": "Tem certeza de que deseja excluir este produto?", "product-table-edit": "<PERSON><PERSON>", "product-table-unpublish": "Despublicar", "product-table-publish": "Publicar", "product-table-draft": "ras<PERSON><PERSON><PERSON>", "product-table-published": "publicado", "product-table-success": "Sucesso", "product-table-successfully-unpublished-product": "Produto despublicado com sucesso", "product-table-successfully-published-product": "Produto publicado com sucesso", "product-table-error": "Erro", "product-table-duplicate": "Duplicar", "product-table-delete": "Excluir", "product-table-proposed": "Proposto", "product-table-published-title": "Publicado", "product-table-rejected": "<PERSON><PERSON><PERSON><PERSON>", "product-table-draft-title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "product-table-name": "Nome", "product-table-collection": "Coleção", "product-table-status": "Status", "product-table-availability": "Disponibilidade", "product-table-inventory": "Inventário", "product-table-inventory-in-stock-count_one": " em estoque para {{count}} variante", "product-table-inventory-in-stock-count_other": " em estoque para {{count}} variantes", "reservation-form-location": "Localização", "reservation-form-choose-where-you-wish-to-reserve-from": "Escolha de onde deseja fazer a reserva.", "reservation-form-item-to-reserve": "<PERSON>em para reservar", "reservation-form-select-the-item-that-you-wish-to-reserve": "Selecione o item que deseja reservar.", "reservation-form-item": "<PERSON><PERSON>", "reservation-form-in-stock": "Em estoque", "reservation-form-available": "Disponível", "reservation-form-reserve": "<PERSON><PERSON><PERSON>", "reservation-form-remove-item": "Remover item", "reservation-form-description": "Descrição", "reservation-form-what-type-of-reservation-is-this": "Que tipo de reserva é esta?", "reservations-table-reservations": "<PERSON><PERSON><PERSON>", "reservations-table-edit": "<PERSON><PERSON>", "reservations-table-delete": "Excluir", "reservations-table-confirm-delete": "Tem certeza de que deseja remover esta reserva?", "reservations-table-remove-reservation": "Remover reserva", "reservations-table-reservation-has-been-removed": "Reserva removida", "new-success": "Sucesso", "new-successfully-created-reservation": "Reserva criada com sucesso", "new-error": "Erro", "new-cancel": "<PERSON><PERSON><PERSON>", "new-save-reservation": "<PERSON>var reserva", "new-reserve-item": "<PERSON><PERSON><PERSON>", "new-metadata": "Metadados", "reservations-table-order-id": "ID do Pedido", "reservations-table-description": "Descrição", "reservations-table-created": "<PERSON><PERSON><PERSON>", "reservations-table-quantity": "Quantidade", "search-modal-start-typing-to-search": "Comece a digitar para pesquisar...", "search-modal-clear-search": "<PERSON><PERSON> pes<PERSON>a", "search-modal-or": "ou", "search-modal-to-navigate": "para navegar", "search-modal-to-select-and": "para selecionar e", "search-modal-to-search-anytime": "para pesquisar a qualquer momento", "templates-settings": "Configurações", "templates-manage-the-settings-for-your-medusa-store": "<PERSON><PERSON><PERSON><PERSON> as configura<PERSON><PERSON><PERSON> da sua loja Medusa", "transfer-orders-modal-info": "Informação", "transfer-orders-modal-customer-is-already-the-owner-of-the-order": "O cliente já é o proprietário do pedido", "transfer-orders-modal-success": "Sucesso", "transfer-orders-modal-successfully-transferred-order-to-different-customer": "Pedido transferido com sucesso para um cliente diferente", "transfer-orders-modal-error": "Erro", "transfer-orders-modal-could-not-transfer-order-to-different-customer": "Não foi possível transferir o pedido para um cliente diferente", "transfer-orders-modal-transfer-order": "Transferir pedido", "transfer-orders-modal-order": "Pedido", "transfer-orders-modal-current-owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transfer-orders-modal-the-customer-currently-related-to-this-order": "O cliente atualmente associado a este pedido", "transfer-orders-modal-new-owner": "Novo Proprietário", "transfer-orders-modal-the-customer-to-transfer-this-order-to": "O cliente para o qual deseja transferir este pedido", "transfer-orders-modal-cancel": "<PERSON><PERSON><PERSON>", "transfer-orders-modal-confirm": "Confirmar", "templates-edit-user": "<PERSON><PERSON>", "templates-remove-user": "Remover <PERSON>", "templates-resend-invitation": "<PERSON><PERSON><PERSON><PERSON>", "templates-success": "Sucesso", "templates-invitiation-link-has-been-resent": "O link do convite foi reenviado", "templates-copy-invite-link": "Copiar link do convite", "templates-invite-link-copied-to-clipboard": "Link do convite copiado para a área de transferência", "templates-remove-invitation": "Remover Convite", "templates-expired": "<PERSON><PERSON><PERSON>", "templates-pending": "Pendente", "templates-all": "Todos", "templates-member": "Membro", "templates-admin": "Administrador", "templates-no-team-permissions": "Sem permissões de equipe", "templates-status": "Status", "templates-active": "Ativo", "templates-name": "Nome", "templates-email": "Email", "templates-team-permissions": "Permissões de Equipe", "templates-confirm-remove": "Tem certeza de que deseja remover este usuário?", "templates-remove-user-heading": "Remover usuário", "templates-user-has-been-removed": "O usuário foi removido", "templates-confirm-remove-invite": "Tem certeza de que deseja remover este convite?", "templates-remove-invite": "Remover convite", "templates-invitiation-has-been-removed": "O convite foi removido", "multiselect-choose-categories": "Escolher categorias", "domain-categories-multiselect-selected-with-counts_one": "{{count}} se<PERSON><PERSON><PERSON>", "domain-categories-multiselect-selected-with-counts_other": "{{count}} sele<PERSON>ados", "details-success": "Sucesso", "details-updated-products-in-collection": "Produtos atualizados na coleção", "details-error": "Erro", "details-back-to-collections": "Voltar para Coleções", "details-edit-collection": "<PERSON><PERSON>", "details-delete": "Excluir", "details-metadata": "Metadados", "details-edit-products": "<PERSON><PERSON>", "details-products-in-this-collection": "Produtos nesta cole<PERSON>", "details-raw-collection": "Coleção bruta", "details-delete-collection": "Excluir coleção", "details-successfully-deleted-collection": "Coleção excluída com sucesso", "details-yes-delete": "Sim, excluir", "details-successfully-updated-customer": "Cliente atualizado com sucesso", "details-customer-details": "Detalhes do Cliente", "details-general": "G<PERSON>", "details-first-name": "Primeiro Nome", "details-lebron": "Le<PERSON><PERSON>", "details-last-name": "Sobrenome", "details-james": "<PERSON>", "details-email": "Email", "details-phone-number": "Número de Telefone", "details-cancel": "<PERSON><PERSON><PERSON>", "details-save-and-close": "<PERSON><PERSON> e <PERSON>char", "details-edit": "<PERSON><PERSON>", "details-back-to-customers": "Voltar para Clientes", "details-first-seen": "<PERSON>ira visita", "details-phone": "Telefone", "details-orders": "Pedidos", "details-user": "<PERSON><PERSON><PERSON><PERSON>", "details-orders_one": "{{count}} Pedido", "details-orders_other": "{{count}} Pedidos", "details-an-overview-of-customer-orders": "Visão geral dos Pedidos do Cliente", "details-raw-customer": "Cliente original", "groups-group-updated": "Grupo Atualizado", "groups-group-created": "Grupo Criado", "groups-the-customer-group-has-been-updated": "O grupo de clientes foi atualizado", "groups-the-customer-group-has-been-created": "O grupo de clientes foi criado", "groups-edit-customer-group": "Editar Grupo de Clientes", "groups-create-a-new-customer-group": "Criar um Novo Grupo de Clientes", "groups-details": "<PERSON><PERSON><PERSON>", "groups-metadata": "Metadados", "groups-cancel": "<PERSON><PERSON><PERSON>", "groups-edit-group": "Editar Grupo", "groups-publish-group": "Publicar Grupo", "groups-no-customers-in-this-group-yet": "Ainda não há clientes neste grupo", "groups-customers": "Clientes", "groups-edit": "<PERSON><PERSON>", "groups-delete": "Excluir", "groups-yes-delete": "Sim, excluir", "groups-delete-the-group": "Excluir o grupo", "groups-group-deleted": "Grupo excluído", "groups-confirm-delete-customer-group": "Tem certeza de que deseja excluir este grupo de clientes?", "groups-back-to-customer-groups": "Voltar para grupos de clientes", "groups-new-group": "Novo grupo", "add-condition-conditions-were-successfully-added": "Condições foram adicionadas com sucesso", "add-condition-discount-conditions-updated": "Condições de desconto atualizadas", "add-condition-use-conditions-must-be-used-within-a-conditions-provider": "useConditions deve ser usado dentro de um ConditionsProvider", "collections-search": "Pesquisar...", "collections-cancel": "<PERSON><PERSON><PERSON>", "collections-save-and-go-back": "Salvar e voltar", "collections-save-and-close": "<PERSON>var e fechar", "customer-groups-search": "Pesquisar...", "customer-groups-cancel": "<PERSON><PERSON><PERSON>", "customer-groups-save-and-go-back": "Salvar e voltar", "customer-groups-save-and-close": "<PERSON>var e fechar", "product-types-search": "Pesquisar...", "product-types-cancel": "<PERSON><PERSON><PERSON>", "product-types-save-and-go-back": "Salvar e voltar", "product-types-save-and-close": "<PERSON>var e fechar", "products-search": "Pesquisar...", "products-cancel": "<PERSON><PERSON><PERSON>", "products-save-and-go-back": "Salvar e voltar", "products-save-and-close": "<PERSON>var e fechar", "tags-search": "Pesquisar...", "tags-cancel": "<PERSON><PERSON><PERSON>", "tags-save-and-go-back": "Salvar e voltar", "tags-save-and-close": "<PERSON>var e fechar", "edit-condition-add-conditions": "Adicionar condi<PERSON>", "edit-condition-selected-with-count_one": "{{count}} se<PERSON><PERSON><PERSON>", "edit-condition-selected-with-count_other": "{{count}} sele<PERSON>ados", "edit-condition-deselect": "<PERSON><PERSON><PERSON>", "edit-condition-remove": "Remover", "edit-condition-add": "<PERSON><PERSON><PERSON><PERSON>", "edit-condition-title": "Editar {{type}} na Condição de Desconto", "edit-condition-close": "<PERSON><PERSON><PERSON>", "edit-condition-success": "Sucesso", "edit-condition-the-resources-were-successfully-added": "Os recursos foram adicionados com sucesso", "edit-condition-error": "Erro", "edit-condition-failed-to-add-resources": "Falha ao adicionar recursos", "edit-condition-the-resources-were-successfully-removed": "Os recursos foram removidos com sucesso", "edit-condition-failed-to-remove-resources": "Falha ao remover recursos", "edit-condition-use-edit-condition-context-must-be-used-within-an-edit-condition-provider": "useEditConditionContext deve ser usado dentro de um EditConditionProvider", "conditions-conditions": "Condições", "conditions-add-condition-label": "Adicionar condi<PERSON>", "conditions-this-discount-has-no-conditions": "Este desconto não possui condições", "conditions-success": "Sucesso", "conditions-condition-removed": "Condição removida", "conditions-error": "Erro", "conditions-edit-condition": "Editar condi<PERSON>", "conditions-delete-condition": "Excluir condição", "conditions-discount-is-applicable-to-specific-products": "O desconto é aplicável a produtos específicos", "conditions-discount-is-applicable-to-specific-collections": "O desconto é aplicável a coleções específicas", "conditions-discount-is-applicable-to-specific-product-tags": "O desconto é aplicável a tags de produtos específicas", "conditions-discount-is-applicable-to-specific-product-types": "O desconto é aplicável a tipos de produtos específicos", "conditions-discount-is-applicable-to-specific-customer-groups": "O desconto é aplicável a grupos de clientes específicos", "configurations-success": "Sucesso", "configurations-discount-updated-successfully": "Desconto atualizado com sucesso", "configurations-error": "Erro", "configurations-edit-configurations": "Editar configurações", "configurations-cancel": "<PERSON><PERSON><PERSON>", "configurations-save": "<PERSON><PERSON>", "configurations-configurations": "Configurações", "configurations-start-date": "Data de início", "configurations-end-date": "Data de término", "configurations-delete-configuration": "Excluir configuração", "configurations-discount-end-date-removed": "Data de término do desconto removida", "configurations-number-of-redemptions": "Número de resgates", "configurations-redemption-limit-removed": "Limite de resgates removido", "configurations-delete-setting": "Excluir configuração", "configurations-discount-duration-removed": "Duração do desconto removida", "general-success": "Sucesso", "general-discount-updated-successfully": "Desconto atualizado com sucesso", "general-error": "Erro", "general-edit-general-information": "Editar informações gerais", "general-details": "<PERSON><PERSON><PERSON>", "general-metadata": "Metadados", "general-cancel": "<PERSON><PERSON><PERSON>", "general-save-and-close": "<PERSON>var e fechar", "general-delete-promotion": "Excluir Promoção", "general-confirm-delete-promotion": "Tem certeza de que deseja excluir esta promoção?", "general-promotion-deleted-successfully": "Promoção excluída com sucesso", "general-discount-published-successfully": "Desconto publicado com sucesso", "general-discount-drafted-successfully": "Desconto salvo como rascunho com sucesso", "general-delete-discount": "Excluir desconto", "general-template-discount": "<PERSON><PERSON>", "general-published": "Publicado", "general-draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "general-discount-amount": "Valor do desconto", "general-valid-regions": "Regiões válidas", "general-total-redemptions": "Total de resgates", "general-free-shipping": "FRETE GRÁTIS", "general-unknown-discount-type": "Tipo de desconto desconhecido", "details-discount-deleted": "Desconto excluído", "details-confirm-delete-discount": "Tem certeza de que deseja excluir este desconto?", "details-delete-discount": "Excluir desconto", "details-back-to-discounts": "Voltar para Descontos", "details-raw-discount": "<PERSON><PERSON><PERSON> bruto", "discounts-add-discount": "<PERSON><PERSON><PERSON><PERSON>", "discount-form-add-conditions": "Adicionar <PERSON>", "discount-form-choose-a-condition-type": "Escolher um tipo de condição", "discount-form-you-can-only-add-one-of-each-type-of-condition": "Você só pode adicionar um de cada tipo de condição", "discount-form-you-cannot-add-any-more-conditions": "Você não pode adicionar mais condições", "discount-form-cancel": "<PERSON><PERSON><PERSON>", "discount-form-save": "<PERSON><PERSON>", "add-condition-tables-cancel": "<PERSON><PERSON><PERSON>", "add-condition-tables-save-and-add-more": "Salvar e adicionar mais", "add-condition-tables-save-and-close": "<PERSON>var e fechar", "add-condition-tables-search-by-title": "Pesquisar por título...", "add-condition-tables-search-groups": "Pesquisar grupos...", "add-condition-tables-search-products": "Pesquisar produtos...", "add-condition-tables-search-by-tag": "Pesquisar por tag...", "add-condition-tables-search-by-type": "Pesquisar por tipo...", "details-condition-tables-search-by-title": "Pesquisar por título...", "details-condition-tables-search-groups": "Pesquisar grupos...", "details-condition-tables-cancel": "<PERSON><PERSON><PERSON>", "details-condition-tables-save-and-add-more": "Salvar e adicionar mais", "details-condition-tables-save-and-close": "<PERSON>var e fechar", "details-condition-tables-search-products": "Pesquisar produtos...", "details-condition-tables-search-by-tag": "Pesquisar por tag...", "details-condition-tables-search-by-type": "Pesquisar por tipo...", "edit-condition-tables-search-by-title": "Pesquisar por título...", "edit-condition-tables-title": "<PERSON><PERSON><PERSON><PERSON>", "edit-condition-tables-search-groups": "Pesquisar grupos...", "edit-condition-tables-cancel": "<PERSON><PERSON><PERSON>", "edit-condition-tables-delete-condition": "Excluir condição", "edit-condition-tables-save": "<PERSON><PERSON>", "edit-condition-tables-search-products": "Pesquisar produtos...", "edit-condition-tables-search-by-tag": "Pesquisar por tag...", "edit-condition-tables-search-by-type": "Pesquisar por tipo...", "shared-title": "<PERSON><PERSON><PERSON><PERSON>", "shared-products": "<PERSON><PERSON><PERSON>", "shared-applies-to-the-selected-items": "Aplica-se aos itens selecionados.", "shared-applies-to-all-items-except-the-selected-items": "Aplica-se a todos os itens, exceto aos itens selecionados.", "shared-members": "Me<PERSON><PERSON>", "shared-status": "Status", "shared-variants": "<PERSON><PERSON><PERSON>", "shared-tag": "Tag", "shared-type": "Tipo", "edit-conditions-modal-title": "Editar {{title}}", "form-use-discount-form-must-be-a-child-of-discount-form-context": "useDiscountForm deve ser um filho de DiscountFormContext", "discount-form-error": "Erro", "discount-form-save-as-draft": "<PERSON><PERSON> como rascunho", "discount-form-publish-discount": "Publicar desconto", "discount-form-create-new-discount": "Criar novo desconto", "discount-form-discount-type": "Tipo de desconto", "discount-form-select-a-discount-type": "Selecione um tipo de desconto", "discount-form-allocation": "Alocação", "discount-form-general": "G<PERSON>", "discount-form-configuration": "Configuração", "discount-form-discount-code-application-disclaimer": "O código de desconto se aplica a partir do momento em que você pressiona o botão de publicar e permanecerá assim até que seja alterado.", "discount-form-conditions": "Condições", "discount-form-discount-code-apply-to-all-products-if-left-untouched": "Código de desconto se aplica a todos os produtos até que seja alterado.", "discount-form-add-conditions-to-your-discount": "Adicione condições ao seu Desconto", "discount-form-metadata": "Metadados", "discount-form-metadata-usage-description": "Metadados permitem que você adicione informações adicionais ao seu desconto.", "condition-item-remainder-more": "+{{remainder}} mais", "conditions-edit": "<PERSON><PERSON>", "conditions-product": "Produ<PERSON>", "conditions-collection": "Coleção", "conditions-tag": "Tag", "conditions-customer-group": "Grupo de clientes", "conditions-type": "Tipo", "conditions-add-condition": "Adicionar <PERSON>", "sections-start-date": "Data de início", "sections-schedule-the-discount-to-activate-in-the-future": "Agende o desconto para ativar no futuro.", "sections-select-discount-start-date": "Se você deseja agendar o desconto para ativar no futuro, você pode definir uma data de início aqui; caso contr<PERSON><PERSON>, o desconto estará ativo imediatamente.", "sections-start-time": "Hora de início", "sections-discount-has-an-expiry-date": "O desconto tem uma data de expiração?", "sections-schedule-the-discount-to-deactivate-in-the-future": "Agende o desconto para desativar no futuro.", "sections-select-discount-end-date": "Se você deseja agendar o desconto para desativar no futuro, você pode definir uma data de expiração aqui.", "sections-expiry-date": "Data de expiração", "sections-expiry-time": "Hora de expiração", "sections-limit-the-number-of-redemptions": "Limitar o número de resgates?", "sections-limit-applies-across-all-customers-not-per-customer": "O limite se aplica a todos os clientes, não por cliente.", "sections-limit-discount-number-of-uses": "Se você deseja limitar a quantidade de vezes que um cliente pode resgatar este desconto, você pode definir um limite aqui.", "sections-number-of-redemptions": "Número de resgates", "sections-availability-duration": "Duração da disponibilidade?", "sections-set-the-duration-of-the-discount": "Defina a duração do desconto.", "sections-select-a-discount-type": "Selecione um tipo de desconto", "sections-total-amount": "Valor total", "sections-apply-to-the-total-amount": "Aplicar ao valor total", "sections-item-specific": "Específico do item", "sections-apply-to-every-allowed-item": "Aplicar a todos os itens permitidos", "sections-percentage": "Porcentagem", "sections-fixed-amount": "Valor fixo", "sections-discount-in-whole-numbers": "Desconto em números inteiros", "sections-you-can-only-select-one-valid-region-if-you-want-to-use-the-fixed-amount-type": "Você só pode selecionar uma região válida se desejar usar o tipo de valor fixo", "sections-free-shipping": "Frete gr<PERSON><PERSON>", "sections-override-delivery-amount": "Substituir valor de entrega", "sections-at-least-one-region-is-required": "Pelo menos uma região é necessária", "sections-choose-valid-regions": "Escolha regiões válidas", "sections-code": "Código", "sections-summersale-10": "VERAO10", "sections-code-is-required": "Código é obrigatório", "sections-amount-is-required": "Valor é obrigatório", "sections-amount": "Valor", "sections-customer-invoice-code": "O código que seus clientes irão inserir durante o checkout. Isso aparecerá na fatura do seu cliente.", "sections-uppercase-letters-and-numbers-only": "Apenas letras maiúsculas e números.", "sections-description": "Descrição", "sections-summer-sale-2022": "Promoção de Verão 2022", "sections-this-is-a-template-discount": "Este é um desconto modelo", "sections-template-discounts-description": "Descontos modelo permitem que você defina um conjunto de regras que podem ser usadas em um grupo de descontos. Isso é útil em campanhas que devem gerar códigos exclusivos para cada usuário, mas onde as regras para todos os códigos exclusivos devem ser as mesmas.", "discount-form-product": "Produ<PERSON>", "discount-form-only-for-specific-products": "Apenas para produtos específicos", "discount-form-choose-products": "<PERSON><PERSON><PERSON><PERSON> produtos", "discount-form-customer-group": "Grupo de clientes", "discount-form-only-for-specific-customer-groups": "Apenas para grupos de clientes específicos", "discount-form-choose-groups": "Escolher grupos", "discount-form-tag": "Tag", "discount-form-only-for-specific-tags": "Apenas para tags específicas", "discount-form-collection": "Coleção", "discount-form-only-for-specific-product-collections": "Apenas para coleções de produtos específicas", "discount-form-choose-collections": "<PERSON><PERSON><PERSON><PERSON>", "discount-form-type": "Tipo", "discount-form-only-for-specific-product-types": "Apenas para tipos de produtos específicos", "discount-form-choose-types": "<PERSON><PERSON><PERSON><PERSON> tipos", "utils-products": "produtos", "utils-groups": "grupos", "utils-tags": "tags", "utils-collections": "coleç<PERSON><PERSON>", "utils-types": "tipos", "gift-cards-created-gift-card": "Cartão de presente criado", "gift-cards-custom-gift-card-was-created-successfully": "O cartão de presente personalizado foi criado com sucesso", "gift-cards-error": "Erro", "gift-cards-custom-gift-card": "Cartão de Presente Personalizado", "gift-cards-details": "<PERSON><PERSON><PERSON>", "gift-cards-receiver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gift-cards-cancel": "<PERSON><PERSON><PERSON>", "gift-cards-create-and-send": "Criar e enviar", "details-updated-gift-card": "Cartão de presente atualizado", "details-gift-card-was-successfully-updated": "O cartão de presente foi atualizado com sucesso", "details-failed-to-update-gift-card": "Falha ao atualizar o cartão de presente", "details-edit-gift-card": "<PERSON><PERSON>", "details-details": "<PERSON><PERSON><PERSON>", "details-edit-details": "<PERSON><PERSON>", "details-update-balance-label": "<PERSON><PERSON><PERSON><PERSON> saldo", "details-updated-status": "Status atualizado", "details-successfully-updated-the-status-of-the-gift-card": "Status do Cartão de Presente atualizado com sucesso", "details-back-to-gift-cards": "Voltar para Cartões de Presente", "details-original-amount": "Valor original", "details-balance": "<PERSON><PERSON>", "details-region": "Região", "details-expires-on": "Expira em", "details-created": "<PERSON><PERSON><PERSON>", "details-raw-gift-card": "Cartão de presente bruto", "details-balance-updated": "<PERSON><PERSON>", "details-gift-card-balance-was-updated": "O saldo do cartão de presente foi atualizado", "details-failed-to-update-balance": "Falha ao atualizar o saldo", "details-update-balance": "<PERSON><PERSON><PERSON><PERSON>", "manage-back-to-gift-cards": "Voltar para Cartões de Presente", "gift-cards-please-enter-a-name-for-the-gift-card": "Por favor, insira um nome para o Cartão de Presente", "gift-cards-please-add-at-least-one-denomination": "Por favor, adicione pelo menos uma denominação", "gift-cards-denominations": "Denominações", "gift-cards-success": "Sucesso", "gift-cards-successfully-created-gift-card": "Cartão de Presente criado com sucesso", "gift-cards-create-gift-card": "<PERSON><PERSON><PERSON>", "gift-cards-gift-card-details": "Detalhes do Cartão de Presente", "gift-cards-name": "Nome", "gift-cards-the-best-gift-card": "<PERSON>hor <PERSON> de Presente", "gift-cards-description": "Descrição", "gift-cards-the-best-gift-card-of-all-time": "O me<PERSON>hor <PERSON>t<PERSON> de Presente de todos os tempos", "gift-cards-thumbnail": "Miniatura", "gift-cards-delete": "Excluir", "gift-cards-size-recommended": "Recomendado 1200 x 1600 (3:4), até 10MB cada", "gift-cards-amount": "Valor", "gift-cards-add-denomination": "Adicionar <PERSON>", "gift-cards-create-publish": "Criar e Publicar", "gift-cards-successfully-updated-gift-card": "Cartão de Presente atualizado com sucesso", "gift-cards-gift-cards": "Car<PERSON><PERSON><PERSON> Presente", "gift-cards-manage": "Gerencie os Cartões de Presente de sua loja Medusa", "gift-cards-are-you-ready-to-sell-your-first-gift-card": "Você está pronto para vender seu primeiro Cartão de Presente?", "gift-cards-no-gift-card-has-been-added-yet": "<PERSON>enhum <PERSON> de Presente foi adicionado ainda.", "gift-cards-history": "Hist<PERSON><PERSON><PERSON>", "gift-cards-see-the-history-of-purchased-gift-cards": "Veja o histórico de Cartões de Presente comprados", "gift-cards-successfully-deleted-gift-card": "Cartão de Presente excluído com sucesso", "gift-cards-yes-delete": "Sim, excluir", "gift-cards-delete-gift-card": "Excluir <PERSON> Presente", "inventory-filters": "<PERSON><PERSON><PERSON>", "address-form-address": "Endereço", "address-form-company": "Empresa", "address-form-address-1": "Endereço 1", "address-form-this-field-is-required": "Este campo é obrigatório", "address-form-address-2": "Endereço 2", "address-form-postal-code": "Código postal", "address-form-city": "Cidade", "address-form-country": "<PERSON><PERSON>", "edit-sales-channels-edit-channels": "<PERSON><PERSON>", "edit-sales-channels-add-channels": "<PERSON><PERSON><PERSON><PERSON>", "general-form-location-name": "Nome da Localização", "general-form-flagship-store-warehouse": "<PERSON><PERSON> principal, de<PERSON><PERSON><PERSON>", "general-form-name-is-required": "Nome é obrigatório", "location-card-delete-location": "Excluir Localização", "location-card-confirm-delete": "Você tem certeza de que deseja excluir esta localização? Isso também excluirá todos os níveis de inventário e reservas associados a esta localização.", "location-card-success": "Sucesso", "location-card-location-deleted-successfully": "Localização excluída com sucesso", "location-card-error": "Erro", "location-card-edit-details": "<PERSON><PERSON>", "location-card-delete": "Excluir", "location-card-connected-sales-channels": "Canais de Vendas Conectados", "sales-channels-form-add-sales-channels": "Adicionar can<PERSON> de vendas", "sales-channels-form-edit-channels": "<PERSON><PERSON>", "sales-channels-section-not-connected-to-any-sales-channels-yet": "Ainda não está conectado a nenhum canal de vendas", "edit-success": "Sucesso", "edit-location-edited-successfully": "Localização editada com sucesso", "edit-error": "Erro", "edit-edit-location-details": "<PERSON><PERSON> da Localização", "edit-metadata": "Metadados", "edit-cancel": "<PERSON><PERSON><PERSON>", "edit-save-and-close": "<PERSON>var e fechar", "new-location-added-successfully": "Localização adicionada com sucesso", "new-location-created": "Localização foi criada com sucesso, mas houve um erro associando canais de vendas", "new-cancel-location-changes": "Tem certeza de que deseja cancelar com alterações não salvas", "new-yes-cancel": "<PERSON>m, cancelar", "new-no-continue-creating": "<PERSON><PERSON>, continuar criando", "new-add-location": "Adicionar localização", "new-add-new-location": "Adicionar nova localização", "new-general-information": "Informações Gerais", "new-location-details": "Especifique os detalhes sobre esta localização", "new-select-location-channel": "Especifique através de quais Canais de Vendas os itens desta localização podem ser adquiridos.", "oauth-complete-installation": "Instalação Completa", "claim-type-form-refund": "Reembolso", "claim-type-form-replace": "Substituir", "items-to-receive-form-items-to-receive": "It<PERSON> a Receber", "items-to-receive-form-product": "Produ<PERSON>", "items-to-receive-form-quantity": "Quantidade", "items-to-receive-form-refundable": "Reembolsável", "add-return-reason-reason-for-return": "Motivo da Devolução", "add-return-reason-reason": "Motivo", "add-return-reason-choose-a-return-reason": "Escolha um motivo de devolução", "add-return-reason-note": "Observação", "add-return-reason-product-was-damaged-during-shipping": "Produto foi danificado durante o transporte", "add-return-reason-cancel": "<PERSON><PERSON><PERSON>", "add-return-reason-save-and-go-back": "Salvar e voltar", "add-return-reason-select-reason-title": "Selecionar Motivo", "add-return-reason-edit-reason": "Editar motivo", "add-return-reason-select-reason": "Selecionar motivo", "items-to-return-form-items-to-claim": "Itens a reivindicar", "items-to-return-form-items-to-return": "Itens a devolver", "items-to-return-form-product": "Produ<PERSON>", "items-to-return-form-quantity": "Quantidade", "items-to-return-form-refundable": "Reembolsável", "add-additional-items-screen-go-back": "Voltar", "add-additional-items-screen-add-products": "<PERSON><PERSON><PERSON><PERSON> produtos", "add-additional-items-screen-add-product-variants": "<PERSON><PERSON><PERSON><PERSON> Produto", "add-additional-items-screen-search-products": "Buscar produtos", "add-additional-items-screen-variant-price-missing": "Esta variante não possui um preço para a região/moeda deste pedido e não pode ser selecionada.", "add-additional-items-screen-stock": "Estoque", "add-additional-items-screen-price": "Preço", "add-additional-items-screen-price-overridden-in-price-list-applicable-to-this-order": "O preço foi substituído em uma lista de preços que se aplica a este pedido.", "items-to-send-form-items-to-send": "Itens a enviar", "items-to-send-form-add-products": "<PERSON><PERSON><PERSON><PERSON> produtos", "items-to-send-form-product": "Produ<PERSON>", "items-to-send-form-quantity": "Quantidade", "items-to-send-form-price": "Preço", "items-to-send-form-price-overridden-in-price-list-applicable-to-this-order": "O preço foi substituído em uma lista de preços que se aplica a este pedido.", "refund-amount-form-cancel-editing-refund-amount": "Cancelar edição do valor de reembolso", "refund-amount-form-edit-refund-amount": "<PERSON>ar valor de reembolso", "refund-amount-form-refund-amount-cannot-be-negative": "O valor de reembolso não pode ser negativo", "refund-amount-form-the-refund-amount-must-be-at-least-0": "O valor de reembolso deve ser pelo menos 0", "reservation-indicator-awaiting-reservation-count": "{{awaitingReservation}} itens não reservados", "reservation-indicator-this-item-has-been-fulfilled": "Este item foi atendido.", "edit-reservation-button-quantity-item-location-name": "{{quantity}} item: ${{locationName}}", "reservation-indicator-edit-reservation": "Editar reserva", "rma-summaries-claimed-items": "Itens reivindicados", "rma-summaries-replacement-items": "Itens de substituição", "rma-summaries-customer-refund-description": "O cliente receberá um reembolso total pelos itens reivindicados, pois o custo dos itens de substituição e o envio não serão deduzidos. Alternativamente, você pode definir um valor de reembolso personalizado quando receber os itens devolvidos ou criar uma troca em vez disso.", "rma-summaries-refund-amount": "Valor de reembolso", "rma-summaries-the-customer-will-be-refunded-once-the-returned-items-are-received": "O cliente receberá o reembolso assim que os itens devolvidos forem recebidos", "rma-summaries-the-customer-will-be-refunded-immediately": "O cliente será reembolsado imediatamente", "rma-summaries-receiving": "Recebimento", "rma-summaries-free": "<PERSON><PERSON><PERSON><PERSON>", "send-notification-form-return": "devolução", "send-notification-form-exchange": "troca", "send-notification-form-claim": "reivindicação", "send-notification-form-send-notifications": "Enviar notificações", "send-notification-form-if-unchecked-the-customer-will-not-receive-communication": "Se não estiver marcado, o cliente não receberá comunicações sobre esta {{subject}}.", "shipping-address-form-shipping-address": "Endereço de entrega", "shipping-address-form-ship-to-a-different-address": "Enviar para um endereço diferente", "shipping-address-form-cancel": "<PERSON><PERSON><PERSON>", "shipping-address-form-save-and-go-back": "Salvar e voltar", "shipping-address-form-shipping-information": "Informações de Envio", "shipping-form-shipping-for-return-items": "Envio para itens devolvidos", "shipping-form-shipping-for-replacement-items": "Envio para itens de substituição", "shipping-form-shipping-method-is-required": "O método de envio é obrigatório", "shipping-form-choose-shipping-method": "Escolha o método de envio", "shipping-form-shipping-method": "M<PERSON><PERSON><PERSON>", "shipping-form-add-custom-price": "Adicionar preço personalizado", "shipping-form-return-shipping-for-items-claimed-by-the-customer-is-complimentary": "O envio de devolução para itens reivindicados pelo cliente é gratuito.", "shipping-form-shipping-for-replacement-items-is-complimentary": "O envio de itens de substituição é gratuito.", "components-decrease-quantity": "Di<PERSON><PERSON>r quantidade", "components-increase-quantity": "Aumentar quantidade", "details-successfully-updated-address": "Endereço atualizado com sucesso", "details-billing-address": "Endereço de Cobrança", "details-shipping-address": "Endereço de Entrega", "details-contact": "Contato", "details-location": "Localização", "claim-are-you-sure-you-want-to-close": "Você tem certeza de que deseja fechar?", "claim-you-have-unsaved-changes-are-you-sure-you-want-to-close": "Você tem alterações não salvas, tem certeza de que deseja fechar?", "claim-please-select-a-reason": "Por favor, selecione um motivo", "claim-a-shipping-method-for-replacement-items-is-required": "Um método de envio para os itens de substituição é necessário", "claim-successfully-created-claim": "Reivindicação criada com sucesso", "claim-created": "Uma reivindicação para o pedido #{{display_id}} foi criada com sucesso", "claim-error-creating-claim": "Erro ao criar reivindicação", "claim-create-claim": "<PERSON><PERSON><PERSON>", "claim-location": "Localização", "claim-choose-which-location-you-want-to-return-the-items-to": "Escolha para qual local deseja devolver os itens.", "claim-select-location-to-return-to": "Selecione o Local de Devolução", "claim-cancel": "<PERSON><PERSON><PERSON>", "claim-submit-and-close": "Enviar e fechar", "create-fulfillment-error": "Erro", "create-fulfillment-please-select-a-location-to-fulfill-from": "Por favor, selecione uma localização para atender", "create-fulfillment-cant-allow-this-action": "Não é possível permitir esta ação", "create-fulfillment-trying-to-fulfill-more-than-in-stock": "<PERSON><PERSON><PERSON> atender mais do que há em estoque", "create-fulfillment-successfully-fulfilled-order": "Pedido cumprido com sucesso", "create-fulfillment-successfully-fulfilled-swap": "Troca cumprida com sucesso", "create-fulfillment-successfully-fulfilled-claim": "Reivindicação cumprida com sucesso", "create-fulfillment-success": "Sucesso", "create-fulfillment-cancel": "<PERSON><PERSON><PERSON>", "create-fulfillment-create-fulfillment": "<PERSON><PERSON><PERSON>", "create-fulfillment-create-fulfillment-title": "<PERSON><PERSON><PERSON>", "create-fulfillment-locations": "Locais", "create-fulfillment-choose-where-you-wish-to-fulfill-from": "<PERSON><PERSON><PERSON><PERSON> de onde deseja atender.", "create-fulfillment-items-to-fulfill": "Itens a cumprir", "create-fulfillment-select-the-number-of-items-that-you-wish-to-fulfill": "Selecione o número de itens que deseja cumprir.", "create-fulfillment-send-notifications": "Enviar notificações", "create-fulfillment-when-toggled-notification-emails-will-be-sent": "<PERSON>uando at<PERSON>, e-mails de notificação serão enviados.", "create-fulfillment-quantity-is-not-valid": "A quantidade não é válida", "detail-cards-allocated": "Alocado", "detail-cards-not-fully-allocated": "Não totalmente alocado", "detail-cards-subtotal": "Subtotal", "detail-cards-shipping": "<PERSON><PERSON>", "detail-cards-tax": "Imposto", "detail-cards-total": "Total", "detail-cards-edit-order": "<PERSON><PERSON>", "detail-cards-allocate": "Alocar", "detail-cards-discount": "Desconto:", "detail-cards-original-total": "Total Original", "details-successfully-updated-the-email-address": "Endereço de e-mail atualizado com sucesso", "details-email-address": "Endereço de E-mail", "details-save": "<PERSON><PERSON>", "details-order-id-copied": "ID do Pedido copiado", "details-email-copied": "E-mail copiado", "details-cancel-order-heading": "Cancelar pedido", "details-are-you-sure-you-want-to-cancel-the-order": "Você tem certeza de que deseja cancelar o pedido?", "order-details-display-id": "pedido #{{display_id}}", "details-successfully-canceled-order": "Pedido cancelado com sucesso", "details-go-to-customer": "<PERSON>r para o Cliente", "details-transfer-ownership": "Transferir <PERSON>", "details-edit-shipping-address": "<PERSON><PERSON> de Entrega", "details-edit-billing-address": "<PERSON><PERSON> Cobrança", "details-edit-email-address": "<PERSON><PERSON> de E-mail", "details-back-to-orders": "Voltar para Pedidos", "details-cancel-order": "<PERSON><PERSON>ar <PERSON>", "details-payment": "Pagamento", "details-refunded": "Reembolsado", "details-total-paid": "Total Pago", "details-fulfillment": "Atendimento", "details-create-fulfillment": "<PERSON><PERSON><PERSON>", "details-shipping-method": "<PERSON><PERSON><PERSON><PERSON>", "details-customer": "Cliente", "details-shipping": "<PERSON><PERSON>", "details-billing": "Cobrança", "details-raw-order": "Pedido Bruto", "mark-shipped-successfully-marked-order-as-shipped": "Pedido marcado como enviado com sucesso", "mark-shipped-successfully-marked-swap-as-shipped": "Troca marcada como enviada com sucesso", "mark-shipped-successfully-marked-claim-as-shipped": "Reivindicação marcada como enviada com sucesso", "mark-shipped-success": "Sucesso", "mark-shipped-error": "Erro", "mark-shipped-mark-fulfillment-shipped": "Marcar Atendimento como Enviado", "mark-shipped-tracking": "Rastreamento", "mark-shipped-tracking-number-label": "Número de Rastreamento", "mark-shipped-tracking-number": "Número de Rastreamento...", "mark-shipped-add-additional-tracking-number": "+ Adicionar Nú<PERSON>o de Rastreamento Adicional", "mark-shipped-send-notifications": "Enviar notificações", "mark-shipped-cancel": "<PERSON><PERSON><PERSON>", "mark-shipped-complete": "Concluir", "order-line-warning": "Aviso", "order-line-cannot-duplicate-an-item-without-a-variant": "Não é possível duplicar um item sem uma variante", "order-line-error": "Erro", "order-line-failed-to-duplicate-item": "Falha ao duplicar o item", "order-line-success": "Sucesso", "order-line-item-removed": "<PERSON>em removido", "order-line-failed-to-remove-item": "Falha ao remover o item", "order-line-item-added": "<PERSON>em adicionado", "order-line-failed-to-replace-the-item": "Falha ao substituir o item", "order-line-replace-product-variants": "Substituir <PERSON> Produto", "order-line-replace-with-other-item": "Substituir por outro item", "order-line-duplicate-item": "Duplicar item", "order-line-remove-item": "Remover item", "order-line-line-item-cannot-be-edited": "Este item de linha faz parte de um atendimento e não pode ser editado. Cancele o atendimento para editar o item de linha.", "order-line-new": "Novo", "order-line-modified": "Modificado", "receive-return-please-select-at-least-one-item-to-receive": "Por favor, selecione pelo menos um item para receber", "receive-return-successfully-received-return": "Devolução recebida com sucesso", "receive-return-received-return-for-order": "Devolução recebida para o pedido #{{display_id}}", "receive-return-failed-to-receive-return": "Falha ao receber devolução", "receive-return-receive-return": "Receber Devolução", "receive-return-location": "Localização", "receive-return-choose-location": "Escolha para qual local deseja devolver os itens.", "receive-return-select-location-to-return-to": "Selecione o Local de Devolução", "receive-return-no-inventory-levels-exist-for-the-items-at-the-selected-location": "Não existem níveis de inventário para os itens no local selecionado", "receive-return-cancel": "<PERSON><PERSON><PERSON>", "receive-return-save-and-close": "<PERSON>var e fechar", "refund-success": "Sucesso", "refund-successfully-refunded-order": "Pedido reembolsado com sucesso", "refund-error": "Erro", "refund-create-a-refund": "Criar um reembolso", "refund-attention": "Atenção!", "refund-system-payment-disclaimer": "Um ou mais dos seus pagamentos são pagamentos do sistema. Esteja ciente de que capturas e reembolsos não são tratados pela Medusa para tais pagamentos.", "refund-details": "<PERSON><PERSON><PERSON>", "refund-cannot-refund-more-than-the-orders-net-total": "Não é possível reembolsar mais do que o total líquido do pedido.", "refund-discount": "Desconto", "refund-reason": "Motivo", "refund-note": "<PERSON>a", "refund-discount-for-loyal-customer": "Desconto para cliente fiel", "refund-send-notifications": "Enviar notificações", "refund-cancel": "<PERSON><PERSON><PERSON>", "refund-complete": "Concluir", "reservation-reservation-was-deleted": "Reserva foi excluída", "reservation-the-allocated-items-have-been-released": "Os itens alocados foram liberados.", "reservation-error": "Erro", "reservation-failed-to-delete-the-reservation": "Falha ao excluir a reserva", "reservation-reservation-was-updated": "Reserva foi atualizada", "reservation-the-reservation-change-was-saved": "A alteração na reserva foi salva.", "reservation-errors": "<PERSON><PERSON><PERSON>", "reservation-failed-to-update-reservation": "Falha ao atualizar a reserva", "reservation-edit-reservation": "<PERSON><PERSON>", "reservation-location": "Localização", "reservation-choose-which-location-you-want-to-ship-the-items-from": "Escolha de qual local você deseja enviar os itens.", "reservation-items-to-allocate-title": "Itens para Alocar", "reservation-select-the-number-of-items-that-you-wish-to-allocate": "Selecione o número de itens que você deseja alocar.", "reservation-max-reservation-requested": " / {{maxReservation}} solicitados", "reservation-reserved": " reservados", "reservation-description": "Descrição", "reservation-what-type-of-reservation-is-this": "Que tipo de reserva é esta?", "reservation-metadata": "Metadados", "reservation-remove-metadata": "Remover metadados", "reservation-add-metadata": "Adiciona<PERSON>", "reservation-delete-reservation": "Excluir reserva", "reservation-cancel": "<PERSON><PERSON><PERSON>", "reservation-save-and-close": "<PERSON>var e fechar", "reservation-couldnt-allocate-items": "Não foi possível alocar itens", "reservation-items-allocated": "Itens alocados", "reservation-items-have-been-allocated-successfully": "Os itens foram alocados com sucesso", "reservation-save-reservation": "<PERSON>var reserva", "reservation-loading": "Carregando...", "reservation-allocate-order-items": "Alocar itens do pedido", "reservation-choose-where-you-wish-to-allocate-from": "Escolha de onde deseja alocar", "reservation-items-to-allocate": "Itens para alocar", "returns-success": "Sucesso", "returns-successfully-returned-order": "Pedido devolvido com sucesso", "returns-error": "Erro", "returns-request-return": "Solicitar Devolução", "returns-items-to-return": "Itens para devolver", "returns-choose-which-location-you-want-to-return-the-items-to": "Escolha para qual local deseja devolver os itens.", "returns-select-location-to-return-to": "Selecione o Local de Devolução", "returns-selected-location-has-no-inventory-levels": "A localização selecionada não possui níveis de inventário para os itens selecionados. A devolução pode ser solicitada, mas não pode ser recebida até que um nível de inventário seja criado para a localização selecionada.", "returns-shipping": "<PERSON><PERSON>", "returns-choose-retur,-shipping-method": "Escolha o método de envio que deseja usar para esta devolução.", "returns-total-refund": "Reembolso Total", "returns-amount": "Valor", "returns-send-notifications": "Enviar notificações", "returns-notify-customer-of-created-return": "Notificar o cliente da devolução criada", "returns-back": "Voltar", "returns-submit": "Enviar", "rma-sub-modals-search-for-additional": "Procurar por adicionais", "rma-sub-modals-general": "G<PERSON>", "rma-sub-modals-first-name": "Nome", "rma-sub-modals-last-name": "Sobrenome", "rma-sub-modals-phone": "Telefone", "rma-sub-modals-shipping-address": "Endereço de Envio", "rma-sub-modals-address-1": "Endereço 1", "rma-sub-modals-address-2": "Endereço 2", "rma-sub-modals-province": "<PERSON>v<PERSON><PERSON>", "rma-sub-modals-postal-code": "Código postal", "rma-sub-modals-city": "Cidade", "rma-sub-modals-country": "<PERSON><PERSON>", "rma-sub-modals-back": "Voltar", "rma-sub-modals-add": "<PERSON><PERSON><PERSON><PERSON>", "rma-sub-modals-name": "Nome", "rma-sub-modals-status": "Status", "rma-sub-modals-in-stock": "Em Estoque", "rma-sub-modals-products": "<PERSON><PERSON><PERSON>", "rma-sub-modals-search-products": "Buscar Produtos...", "rma-sub-modals-reason-for-return": "Motivo da Devolução", "rma-sub-modals-reason": "Motivo", "rma-sub-modals-note": "Observação", "swap-success": "Sucesso", "swap-successfully-created-exchange": "Troca criada com sucesso", "swap-error": "Erro", "swap-register-exchange": "Registrar Troca", "swap-items-to-return": "Itens a devolver", "swap-shipping": "<PERSON><PERSON>", "swap-shipping-method": "<PERSON><PERSON><PERSON><PERSON>", "swap-add-a-shipping-method": "Adicionar um método de envio", "swap-location": "Localização", "swap-choose-which-location-you-want-to-return-the-items-to": "Escolha para qual local deseja devolver os itens.", "swap-select-location-to-return-to": "Selecionar Local de Devolução", "swap-items-to-send": "Itens a enviar", "swap-add-product": "<PERSON><PERSON><PERSON><PERSON>", "swap-return-total": "Total da Devolução", "swap-additional-total": "Total Adicional", "swap-outbond-shipping": "<PERSON><PERSON>", "swap-calculated-at-checkout": "Calculado no checkout", "swap-estimated-difference": "Diferença estimada", "swap-send-notifications": "Enviar notificações", "swap-if-unchecked-the-customer-will-not-receive-communication-about-this-exchange": "Se não estiver marcado, o cliente não receberá comunicações sobre esta troca", "swap-complete": "Concluir", "templates-shipped": "Enviado", "templates-fulfilled": "Atendido", "templates-canceled": "Cancelado", "templates-partially-fulfilled": "Atendido parcialmente", "templates-fulfillment-status-requires-action": "Requer <PERSON>", "templates-awaiting-fulfillment": "Aguardando atendimento", "templates-partially-shipped": "Parcialmente Enviado", "templates-cancel-fulfillment-heading": "Cancelar atendimento?", "templates-are-you-sure-you-want-to-cancel-the-fulfillment": "Tem certeza de que deseja cancelar o atendimento?", "templates-successfully-canceled-swap": "Troca cancelada com sucesso", "templates-error": "Erro", "templates-successfully-canceled-claim": "Reivindicação cancelada com sucesso", "templates-successfully-canceled-fulfillment": "Atendimento cancelado com sucesso", "templates-fulfillment-has-been-canceled": "O atendimento foi cancelado", "templates-fulfilled-by-provider": "{{title}} Atendido por {{provider}}", "templates-not-shipped": "Não Enviado", "templates-tracking": "Rastreamento", "templates-shipped-from": "Enviado de", "templates-shipping-from": "Enviado de", "templates-mark-shipped": "Marcar como Enviado", "templates-cancel-fulfillment": "<PERSON><PERSON><PERSON>", "templates-completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "templates-processing": "Processando", "templates-requires-action": "Requer ação", "templates-capture-payment": "<PERSON><PERSON><PERSON> paga<PERSON>", "templates-successfully-captured-payment": "Pagamento capturado com sucesso", "templates-refund": "Reembolso", "templates-total-for-swaps": "Total para Trocas", "templates-refunded-for-swaps": "Reembolsado para Trocas", "templates-refunded-for-returns": "Reembolsado para Devoluções", "templates-manually-refunded": "Reembolsado manualmente", "templates-net-total": "Total Líquido", "templates-paid": "Pago", "templates-awaiting-payment": "<PERSON><PERSON><PERSON><PERSON>aga<PERSON>", "templates-payment-status-requires-action": "Requer <PERSON>", "draft-orders-completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "draft-orders-open": "Abe<PERSON>o", "draft-orders-mark-as-paid": "Marcar como Pago", "draft-orders-success": "Sucesso", "draft-orders-successfully-mark-as-paid": "Marcado como pago com sucesso", "draft-orders-error": "Erro", "draft-orders-successfully-canceled-order": "Pedido cancelado com sucesso", "draft-orders-back-to-draft-orders": "Voltar para Pedidos em Rascunho", "on-mark-as-paid-confirm-order-id": "Pedido #{{display_id}}", "draft-orders-go-to-order": "<PERSON>r para Pedido", "draft-orders-cancel-draft-order": "Cancelar Pedido em Rascunho", "draft-orders-draft-order": "Pedido em Rascunho", "draft-orders-email": "E-mail", "draft-orders-phone": "Telefone", "draft-orders-amount": "Valor {{currency_code}}", "draft-orders-payment": "Pagamento", "draft-orders-subtotal": "Subtotal", "draft-orders-shipping": "<PERSON><PERSON>", "draft-orders-tax": "Imposto", "draft-orders-total-to-pay": "Total a Pagar", "draft-orders-payment-link": "<PERSON>:", "draft-orders-configure-payment-link-in-store-settings": "Configure o link de pagamento nas configurações da loja", "draft-orders-shipping-method": "<PERSON><PERSON><PERSON><PERSON>", "draft-orders-data": "<PERSON><PERSON>", "draft-orders-1-item": "(1 item)", "draft-orders-customer": "Cliente", "draft-orders-edit-shipping-address": "<PERSON><PERSON>vio", "draft-orders-edit-billing-address": "<PERSON><PERSON> Cobrança", "draft-orders-go-to-customer": "<PERSON><PERSON> <PERSON>", "draft-orders-contact": "Contato", "draft-orders-billing": "Cobrança", "draft-orders-raw-draft-order": "Pedido em Rascunho Original", "draft-orders-are-you-sure": "Você tem certeza?", "draft-orders-remove-resource-heading": "Remover {{resource}}", "draft-orders-remove-resource-success-text": "{{resource}} foi removido", "draft-orders-this-will-create-an-order-mark-this-as-paid-if-you-received-the-payment": "<PERSON><PERSON> criar<PERSON> um pedido. Marque como pago se você recebeu o pagamento.", "draft-orders-mark-paid": "Marcar como Pago", "draft-orders-cancel": "<PERSON><PERSON><PERSON>", "draft-orders-create-draft-order": "<PERSON><PERSON>r Pedido em Rascunho", "edit-amount-paid": "Valor <PERSON>", "edit-new-total": "Novo Total", "edit-difference-due": "Diferença Devida", "edit-back": "Voltar", "edit-save-and-go-back": "Salvar e Voltar", "edit-order-edit-set-as-requested": "Edição do Pedido marcada como solicitada", "edit-failed-to-request-confirmation": "Falha ao solicitar confirmação", "edit-added-successfully": "Adicionado com sucesso", "edit-error-occurred": "Ocorreu um erro", "edit-add-product-variants": "Adicionar Variantes do Produto", "edit-edit-order": "<PERSON><PERSON>", "edit-items": "<PERSON><PERSON>", "edit-add-items": "<PERSON><PERSON><PERSON><PERSON> itens", "edit-filter-items": "Filtrar itens...", "edit-note": "Observação", "edit-add-a-note": "Adicionar uma observação...", "variants-table-location": " em {{location}}", "edit-product": "Produ<PERSON>", "edit-in-stock": "Em Estoque", "edit-price": "Preço", "edit-products": "<PERSON><PERSON><PERSON>", "edit-search-product-variants": "Pesquisar Variantes do Produto...", "orders-success": "Sucesso", "orders-successfully-initiated-export": "Exportação iniciada com sucesso", "orders-error": "Erro", "orders-export-orders": "Exportar Pedidos", "components-billing-address": "Endereço de Cobrança", "components-use-same-as-shipping": "Usar o mesmo que o de envio", "components-e-g-gift-wrapping": "Por exemplo, embrulho para presente", "components-title": "<PERSON><PERSON><PERSON><PERSON>", "components-price": "Preço", "components-quantity": "Quantidade", "components-back": "Voltar", "components-add": "<PERSON><PERSON><PERSON><PERSON>", "components-items-for-the-order": "Itens para o pedido", "components-details": "<PERSON><PERSON><PERSON>", "components-price-excl-taxes": "Preço (excl. Impostos)", "components-add-custom": "<PERSON><PERSON><PERSON><PERSON>", "components-add-existing": "<PERSON><PERSON><PERSON><PERSON>", "components-add-products": "<PERSON><PERSON><PERSON><PERSON>", "components-add-custom-item": "<PERSON><PERSON><PERSON><PERSON>", "components-choose-region": "E<PERSON><PERSON><PERSON> regi<PERSON>", "components-region": "Região", "select-shipping-to-name": "(Para {{name}})", "components-attention": "Atenção!", "components-no-options-for-orders-without-shipping": "Você não tem opções para pedidos sem envio. Por favor, adicione uma (por exemplo, \"Retirada na Loja\") com \"Mostrar no site\" desmarcado nas configurações da região e continue.", "components-choose-a-shipping-method": "Escolher um método de envio", "components-set-custom-price": "Definir preço personalizado", "components-custom-price": "Preço Personalizado", "components-customer-and-shipping-details": "Detalhes do Cliente e de Envio", "components-find-existing-customer": "Encontrar cliente existente", "components-email": "E-mail", "components-choose-existing-addresses": "Escolher endereços existentes", "components-create-new": "Criar novo", "components-the-discount-is-not-applicable-to-the-selected-region": "O desconto não é aplicável à região selecionada", "components-the-discount-code-is-invalid": "O código de desconto é inválido", "components-add-discount": "<PERSON><PERSON><PERSON><PERSON>", "components-summer-10": "VERAO10", "components-discount": "Desconto", "select-shipping-code": "(Código: {{code}})", "components-type": "Tipo", "components-value": "Valor", "components-address": "Endereço", "components-shipping-method": "<PERSON><PERSON><PERSON><PERSON>", "components-billing-details": "<PERSON><PERSON><PERSON> de Cobrança", "components-edit": "<PERSON><PERSON>", "form-use-new-order-form-must-be-used-within-new-order-form-provider": "useNewOrderForm deve ser usado dentro de NewOrderFormProvider", "new-order-created": "Pedido criado", "new-create-draft-order": "<PERSON><PERSON>r Pedido em Rascunho", "batch-job-price-list-prices": "Preços da Lista de Preços", "batch-job-upload-a-csv-file-with-variants": "Faça upload de um arquivo CSV com variantes e preços para atualizar sua lista de preços. Observe que quaisquer preços existentes serão excluídos.", "batch-job-unsure-about-how-to-arrange-your-list": "Incerto sobre como organizar sua lista?", "batch-job-download-the-template-file-below-and-update-your-prices": "Baixe o arquivo de modelo abaixo e atualize seus preços", "details-back-to-pricing": "Voltar para Preços", "details-raw-price-list": "Lista de preços brutos", "sections-customer-groups": "Grupos de clientes", "sections-last-edited": "Última edição", "sections-price-overrides": "Substituições de preços", "sections-more": "mais", "sections-delete-price-list-heading": "Excluir Lista de Preços", "sections-are-you-sure-you-want-to-delete-this-price-list": "Tem certeza de que deseja excluir esta lista de preços?", "sections-success": "Sucesso", "sections-price-list-deleted-successfully": "Lista de preços excluída com sucesso", "sections-edit-price-list-details": "Editar detalhes da lista de preços", "sections-delete-price-list": "Excluir lista de preços", "edit-prices-overrides-edit-price-overrides": "Editar substituições de preços", "edit-prices-overrides-success": "Sucesso", "edit-prices-overrides-price-overrides-updated": "Substituições de preços atualizadas", "edit-prices-overrides-cancel": "<PERSON><PERSON><PERSON>", "edit-prices-overrides-save": "<PERSON><PERSON>", "edit-prices-overrides-count_one": "{{count}}", "edit-prices-overrides-count_other": "{{count}}", "edit-prices-overrides-add-prices": "<PERSON><PERSON><PERSON><PERSON>", "prices-details-edit-prices": "<PERSON><PERSON>", "prices-details-prices": "Preços", "prices-details-you-will-be-able-to-override-the-prices-for-the-products-you-add-here": "Você poderá substituir os preços dos produtos que adicionar aqui", "prices-details-remove-from-list": "Remover da lista", "prices-details-edit-manually": "Editar manualmente", "prices-details-import-price-list": "Importar lista de preços", "prices-table-search-by-name-or-sku": "Pesquisar por nome ou SKU...", "prices-table-edit-prices": "<PERSON><PERSON>", "prices-table-remove-product": "Remover produto", "prices-table-success": "Sucesso", "prices-table-deleted-prices-of-product": "Preços excluídos do produto: {{title}}", "prices-table-error": "Erro", "prices-table-name": "Nome", "prices-table-collection": "Coleção", "prices-table-no-collection": "Sem coleção", "prices-table-variants": "<PERSON><PERSON><PERSON>", "pricing-add-price-list": "Adicionar lista de preços", "pricing-price-lists": "Listas de preços", "form-header-error": "Erro", "form-header-success": "Sucesso", "form-header-successfully-updated-price-list": "Lista de preços atualizada com sucesso", "form-header-publish-price-list": "Publicar lista de preços", "form-header-save-as-draft": "<PERSON><PERSON> como rascunho", "form-header-save-changes": "<PERSON><PERSON>", "form-header-cancel": "<PERSON><PERSON><PERSON>", "pricing-form-create-new-price-list": "Criar nova lista de preços", "pricing-form-edit-price-list": "Editar lista de preços", "sections-configuration": "Configuração", "sections-optional-configuration-for-the-price-list": "Configuração opcional para a lista de preços", "sections-price-overrides-time-application": "As substituições de preços se aplicam a partir do momento em que você clica no botão de publicação e permanecerá assim até que sejam alterados.", "sections-price-overrides-has-a-start-date": "As substituições de preços têm uma data de início?", "sections-schedule-the-price-overrides-to-activate-in-the-future": "Agende as substituições de preços para ativar no futuro.", "sections-price-overrides-has-an-expiry-date": "As substituições de preços têm uma data de expiração?", "sections-schedule-the-price-overrides-to-deactivate-in-the-future": "Agende as substituições de preços para desativar no futuro.", "sections-end-date": "Data de término", "sections-customer-availabilty": "Disponibilidade do cliente", "sections-specifiy-which-customer-groups-the-price-overrides-should-apply-for": "Especifique para quais grupos de clientes as substituições de preços devem ser aplicadas.", "sections-customer-groups-label": "Grupos de Clientes", "sections-general": "G<PERSON>", "sections-general-information-for-the-price-list": "Informações gerais para a lista de preços.", "sections-name": "Nome", "sections-b-2-b-black-friday": "B2B, Black Friday...", "sections-for-our-business-partners": "Para nossos parceiros de negócios...", "sections-tax-inclusive-prices": "Preços incluem impostos", "sections-choose-to-make-all-prices-in-this-list-inclusive-of-tax": "Escolha tornar todos os preços nesta lista incluídos de impostos.", "sections-prices": "Preços", "sections-you-will-be-able-to-override-the-prices-for-the-products-you-add-here": "Você poderá substituir os preços dos produtos que adicionar aqui", "sections-define-the-price-overrides-for-the-price-list": "Defina as substituições de preços para a lista de preços", "sections-edit-prices-label": "<PERSON><PERSON>", "sections-remove-from-list": "Remover da lista", "sections-search-by-name-or-sku": "Pesquisar por nome ou SKU...", "sections-edit-prices": "<PERSON><PERSON>", "sections-price-list-type": "Tipo de lista de preços", "sections-select-the-type-of-the-price-list": "Selecione o tipo de lista de preços", "sections-sale-prices-compare-to-price-override": "Ao contrário dos preços de venda, uma substituição de preço não comunica ao cliente que o preço faz parte de uma venda.", "sections-sale": "<PERSON><PERSON><PERSON>", "sections-use-this-if-you-are-creating-prices-for-a-sale": "Use isso se você estiver criando preços para uma venda.", "sections-override": "Substituir", "sections-use-this-to-override-prices": "Use isso para substituir preços.", "components-success": "Sucesso", "components-successfully-updated-category-tree": "Árvore de categorias atualizada com sucesso", "components-error": "Erro", "components-failed-to-update-category-tree": "Falha ao atualizar a árvore de categorias", "components-delete": "Excluir", "components-category-deleted": "Categoria excluída", "components-category-deletion-failed": "Falha na exclusão da categoria", "components-category-status-is-inactive": "Status da categoria está inativo", "components-category-visibility-is-private": "Visibilidade da categoria é privada", "components-add-category-item-to": "Adicionar item de categoria a", "modals-public": "Público", "modals-private": "Privado", "modals-active": "Ativo", "modals-inactive": "Inativo", "modals-success": "Sucesso", "modals-successfully-created-a-category": "Categoria criada com sucesso", "modals-failed-to-create-a-new-category": "Falha ao criar uma nova categoria", "modals-error": "Erro", "modals-save-category": "Salvar categoria", "modals-add-category-to": "Adicionar categoria a {{name}}", "modals-add-category": "Adicionar categoria", "modals-details": "<PERSON><PERSON><PERSON>", "modals-name": "Nome", "modals-give-this-category-a-name": "Dê um nome a esta categoria", "modals-handle": "Identificador", "modals-custom-handle": "Identificador personalizado", "modals-description": "Descrição", "modals-give-this-category-a-description": "Dê uma descrição a esta categoria", "modals-status": "Status", "modals-visibility": "Visibilidade", "modals-successfully-updated-the-category": "Categoria atualizada com sucesso", "modals-failed-to-update-the-category": "Falha ao atualizar a categoria", "modals-edit-product-category": "Editar categoria de produto", "modals-cancel": "<PERSON><PERSON><PERSON>", "modals-save-and-close": "<PERSON>var e fechar", "pages-no-product-categories-yet": "Ainda não existem categorias de produtos, use o botão acima para criar sua primeira categoria.", "pages-add-category": "Adicionar categoria", "pages-product-categories": "Categorias de Produtos", "pages-helps-you-to-keep-your-products-organized": "Ajuda a manter seus produtos organizados.", "batch-job-success": "Sucesso", "batch-job-import-confirmed-for-processing-progress-info-is-available-in-the-activity-drawer": "Importação confirmada para processamento. Informações de progresso estão disponíveis na quadro de atividades.", "batch-job-error": "Erro", "batch-job-import-failed": "Falha na importação.", "batch-job-failed-to-delete-the-csv-file": "Falha ao excluir o arquivo CSV", "batch-job-failed-to-cancel-the-batch-job": "Falha ao cancelar o trabalho em lote", "batch-job-products-list": "lista de produtos", "batch-job-download-template": "Baixe o modelo abaixo para garantir que você esteja seguindo o formato correto.", "batch-job-imports-description": "Através das importações, você pode adicionar ou atualizar produtos. Para atualizar produtos/variantes existentes, você deve definir um ID existente nas colunas de Produto/Variante ID. Se o valor não estiver definido, um novo registro será criado. Você será solicitado a confirmar antes de importar os produtos.", "products-filters": "<PERSON><PERSON><PERSON>", "products-status": "Status", "products-tags": "Tags", "products-spring-summer": "Primavera, verão...", "new-sales-channels": "Canais de venda", "new-this-product-will-only-be-available-in-the-default-sales-channel-if-left-untouched": "Este produto estará disponível apenas no canal de vendas padrão se não for alterado.", "new-change-availablity": "Alterar disponibilidade", "add-variants-a-variant-with-these-options-already-exists": "Já existe uma variante com essas opções.", "add-variants-product-options": "Opções de produto", "add-variants-options-are-used-to-define-the-color-size-etc-of-the-product": "As opções são usadas para definir a cor, tamanho, etc. do produto.", "add-variants-option-title": "Título da opção", "add-variants-variations-comma-separated": "Variações (separadas por vírgula)", "add-variants-color": "Cor...", "add-variants-already-exists": "j<PERSON> <PERSON>e", "add-variants-blue-red-black": "<PERSON><PERSON><PERSON>, Vermelho, Preto...", "add-variants-add-an-option": "Adicionar uma opção", "add-variants-product-variants": "V<PERSON><PERSON> de produto", "add-variants-you-must-add-at-least-one-product-option-before-you-can-begin-adding-product-variants": "Você deve adicionar pelo menos uma opção de produto antes de começar a adicionar variantes de produto.", "add-variants-variant": "<PERSON><PERSON><PERSON>", "add-variants-inventory": "Inventário", "add-variants-add-a-variant": "Adicionar uma variante", "add-variants-create-variant": "<PERSON><PERSON><PERSON>", "add-variants-cancel": "<PERSON><PERSON><PERSON>", "add-variants-save-and-close": "<PERSON>var e fechar", "new-variant-a-variant-with-these-options-already-exists": "Já existe uma variante com essas opções.", "new-variant-are-you-sure-you-want-to-delete-this-variant": "Tem certeza de que deseja excluir esta variante?", "new-variant-delete-variant": "Excluir <PERSON>", "new-variant-edit": "<PERSON><PERSON>", "new-variant-delete": "Excluir", "new-variant-edit-variant": "<PERSON><PERSON>", "new-variant-cancel": "<PERSON><PERSON><PERSON>", "new-variant-save-and-close": "<PERSON>var e fechar", "new-something-went-wrong-while-trying-to-upload-images": "Algo deu errado ao tentar fazer upload de imagens.", "new-no-file-service-configured": "Você pode não ter um serviço de arquivo configurado. Por favor, entre em contato com o seu administrador.", "new-upload-thumbnail-error": "Algo deu errado ao tentar fazer upload da miniatura.", "new-save-as-draft": "<PERSON><PERSON> como rascunho", "new-publish-product": "Publicar produto", "new-general-information-title": "Informações gerais", "new-to-start-selling-all-you-need-is-a-name-and-a-price": "Para começar a vender, você só precisa de um nome e um preço.", "new-organize-product": "Organizar Produto", "new-add-variations-of-this-product": "Adicionar variações deste produto.", "new-offer-your-customers-different-options-for-color-format-size-shape-etc": "Ofereça aos seus clientes diferentes opções de cor, formato, tamanho, forma, etc.", "new-used-for-shipping-and-customs-purposes": "Usado para fins de envio e alfândega.", "new-dimensions": "Dimensões", "new-customs": "Alfândega", "new-used-to-represent-your-product-during-checkout-social-sharing-and-more": "Usado para representar seu produto durante o checkout, compartilhamento social e mais.", "new-media": "Mí<PERSON>", "new-add-images-to-your-product": "Adicionar imagens ao seu produto.", "overview-import-products": "Importar Produtos", "overview-export-products": "Exportar Produtos", "overview-new-product": "Novo Produto", "overview-new-collection": "Nova Coleção", "overview-success": "Sucesso", "overview-successfully-created-collection": "Coleção criada com sucesso", "overview-error": "Erro", "overview-successfully-initiated-export": "Exportação iniciada com sucesso", "modals-add-sales-channels": "Adicionar can<PERSON> de vendas", "modals-find-channels": "Encontrar canais", "modals-updated-the-api-key": "Chave API atualizada", "modals-failed-to-update-the-api-key": "Falha ao atualizar a chave API", "modals-edit-api-key-details": "<PERSON><PERSON> de<PERSON> da chave API", "modals-title": "<PERSON><PERSON><PERSON><PERSON>", "modals-name-your-key": "Nomeie sua chave", "modals-sales-channels-added-to-the-scope": "Canais de vendas adicionados ao escopo", "modals-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "Ocorreu um erro ao adicionar canais de vendas ao escopo da chave", "modals-add-and-go-back": "Adicionar e voltar", "modals-add-and-close": "Adicionar e fechar", "modals-sales-channels-removed-from-the-scope": "Canais de vendas removidos do escopo", "modals-error-occurred-while-removing-sales-channels-from-the-scope-of-the-key": "Ocorreu um erro ao remover canais de vendas do escopo da chave", "modals-edit-sales-channels": "Editar canais de vendas", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_one": "{{count}}", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_other": "{{count}}", "modals-deselect": "<PERSON><PERSON><PERSON>", "modals-remove": "Remover", "modals-add-channels": "<PERSON><PERSON><PERSON><PERSON>", "modals-close": "<PERSON><PERSON><PERSON>", "pages-sales-channels": "Canais de vendas", "pages-connect-as-many-sales-channels-to-your-api-key-as-you-need": "Conecte quantos canais de vendas você precisar à sua chave de API.", "pages-add-sales-channels": "Adicionar can<PERSON> de vendas", "pages-edit-sales-channels": "Editar canais de vendas", "pages-success": "Sucesso", "pages-created-a-new-api-key": "Criou uma nova chave de API", "pages-error": "Erro", "pages-failed-to-create-a-new-api-key": "Falha ao criar uma nova chave de API", "pages-sales-channels-added-to-the-scope": "Canais de vendas adicionados ao escopo", "pages-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "Ocorreu um erro ao adicionar canais de vendas ao escopo da chave", "pages-publish-api-key": "Publicar chave de API", "pages-create-api-key": "<PERSON><PERSON><PERSON><PERSON>", "pages-create-and-manage-api-keys-right-now-this-is-only-related-to-sales-channels": "Criar e gerenciar chaves de API. Atualmente, isso está relacionado apenas aos canais de vendas.", "pages-create-api-key-label": "Criar chave de API", "pages-back-to-settings": "Voltar para configurações", "pages-publishable-api-keys": "Chaves de API publicáveis", "pages-these-publishable-keys-will-allow-you-to-authenticate-api-requests": "Essas chaves publicáveis permitirão que você autentique solicitações de API.", "tables-name": "Nome", "tables-token": "Token", "tables-done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tables-copy-to-clipboard": "Copiar para a área de transferência", "tables-created": "<PERSON><PERSON><PERSON>", "tables-status": "Status", "tables-revoked": "Rev<PERSON><PERSON>", "tables-live": "Ativo", "tables-edit-api-key-details": "<PERSON><PERSON> de<PERSON>hes da chave de API", "tables-edit-sales-channels": "Editar canais de vendas", "tables-copy-token": "Copiar token", "tables-revoke-token": "Revogar token", "tables-delete-api-key": "Excluir chave de API", "tables-yes-delete": "Sim, excluir", "tables-api-key-deleted": "Chave de API excluída", "tables-are-you-sure-you-want-to-delete-this-public-key": "Tem certeza de que deseja excluir esta chave pública?", "tables-delete-key": "Excluir chave", "tables-yes-revoke": "Sim, revogar", "tables-api-key-revoked": "Chave de API revogada", "tables-are-you-sure-you-want-to-revoke-this-public-key": "Tem certeza de que deseja revogar esta chave pública?", "tables-revoke-key": "<PERSON>ogar chave", "tables-api-keys": "<PERSON>ves de <PERSON>", "tables-no-keys-yet-use-the-above-button-to-create-your-first-publishable-key": "Nenhuma chave ainda, use o botão acima para criar sua primeira chave publicável", "tables-title": "<PERSON><PERSON><PERSON><PERSON>", "tables-description": "Descrição", "tables-no-added-sales-channels": "Nenhum canal de vendas adicionado", "tables-sales-channels": "Canais de Vendas", "form-title": "<PERSON><PERSON><PERSON><PERSON>", "form-website-app-amazon-physical-store-pos-facebook-product-feed": "Site, aplicativo, Amazon, loja física POS, feed de produtos do Facebook...", "form-description": "Descrição", "form-available-products-at-our-website-app": "Produtos disponíveis em nosso site e aplicativo...", "form-success": "Sucesso", "form-the-sales-channel-is-successfully-updated": "O canal de vendas foi atualizado com sucesso", "form-error": "Erro", "form-failed-to-update-the-sales-channel": "Falha ao atualizar o canal de vendas", "form-sales-channel-details": "Detalhes do canal de vendas", "form-general-info": "Informações gerais", "form-name": "Nome", "form-close": "<PERSON><PERSON><PERSON>", "form-save": "<PERSON><PERSON>", "pages-draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pages-control-which-products-are-available-in-which-channels": "Controle quais produtos estão disponíveis em quais canais", "pages-search-by-title-or-description": "Pesquisar por título ou descrição", "pages-confirm-delete-sales-channel": "Tem certeza de que deseja excluir este canal de vendas? A configuração que você fez será perdida permanentemente.", "pages-delete-channel-heading": "Excluir Canal", "pages-edit-general-info": "Editar informações gerais", "pages-add-products": "<PERSON><PERSON><PERSON><PERSON> produtos", "pages-delete-channel": "Excluir canal", "pages-disabled": "Desativado", "pages-enabled": "<PERSON><PERSON>do", "tables-collection": "Coleção", "tables-start-building-your-channels-setup": "Comece a construir sua configuração de canais...", "tables-no-products-in-channels": "Você ainda não adicionou produtos a este canal, mas assim que fizer isso, eles ficarão aqui.", "tables-add-products": "<PERSON><PERSON><PERSON><PERSON> produtos", "tables-details": "<PERSON><PERSON><PERSON>", "tables-remove-from-the-channel": "Remover do canal", "tables-products": "<PERSON><PERSON><PERSON>", "sales-channels-table-placeholder-selected-with-counts_one": "{{count}}", "sales-channels-table-placeholder-selected-with-counts_other": "{{count}}", "tables-remove": "Remover", "components-successfully-updated-currency": "Moeda atualizada com sucesso", "components-default": "Padrão", "default-store-currency-success": "Sucesso", "default-store-currency-successfully-updated-default-currency": "Moeda padrão atualizada com sucesso", "default-store-currency-error": "Erro", "default-store-currency-default-store-currency": "Moeda padrão da loja", "default-store-currency-this-is-the-currency-your-prices-are-shown-in": "Esta é a moeda em que seus preços são exibidos.", "store-currencies-success": "Sucesso", "store-currencies-successfully-updated-currencies": "Moedas atualizadas com sucesso", "store-currencies-error": "Erro", "store-currencies-cancel": "<PERSON><PERSON><PERSON>", "store-currencies-save-and-go-back": "Salvar e voltar", "store-currencies-save-and-close": "<PERSON>var e fechar", "store-currencies-add-store-currencies": "<PERSON><PERSON><PERSON><PERSON>", "store-currencies-current-store-currencies": "<PERSON><PERSON>", "store-currencies-close": "<PERSON><PERSON><PERSON>", "current-currencies-screen-selected-with-count_one": "{{count}}", "current-currencies-screen-selected-with-count_other": "{{count}}", "store-currencies-deselect": "<PERSON><PERSON><PERSON>", "store-currencies-remove": "Remover", "store-currencies-add-currencies": "<PERSON><PERSON><PERSON><PERSON>", "store-currencies-store-currencies": "<PERSON><PERSON>", "store-currencies-all-the-currencies-available-in-your-store": "<PERSON><PERSON> as moedas disponíveis em sua loja.", "store-currencies-edit-currencies": "<PERSON><PERSON> moe<PERSON>", "currencies-an-unknown-error-occurred": "Ocorreu um erro desconhecido", "currencies-error": "Erro", "currencies-back-to-settings": "Voltar para Configurações", "currencies-manage-the-markets-that-you-will-operate-within": "Gerencie os mercados nos quais você irá operar.", "currencies-include-or-exclude-taxes": "Decida se deseja incluir ou excluir impostos sempre que definir um preço nesta moeda", "currencies-tax-incl-prices": "Preços com impostos incluídos", "settings-error": "Erro", "settings-malformed-swap-url": "URL de troca malformada", "settings-malformed-payment-url": "URL de pagamento malformada", "settings-malformed-invite-url": "URL de convite malformada", "settings-success": "Sucesso", "settings-successfully-updated-store": "Loja atualizada com sucesso", "settings-back-to-settings": "Voltar para Configurações", "settings-save": "<PERSON><PERSON>", "settings-cancel": "<PERSON><PERSON><PERSON>", "settings-store-details": "<PERSON><PERSON><PERSON>", "settings-manage-your-business-details": "Gerencie os detalhes do seu negócio", "settings-general": "G<PERSON>", "settings-store-name": "Nome da loja", "settings-medusa-store": "<PERSON><PERSON>", "settings-advanced-settings": "Configurações avançadas", "settings-swap-link-template": "Modelo de link de troca", "settings-draft-order-link-template": "Modelo de link de pedido em rascunho", "settings-invite-link-template": "Modelo de link de convite", "settings-manage-the-general-settings-for-your-store": "<PERSON><PERSON><PERSON><PERSON> as configuraç<PERSON>es gerais da sua loja", "settings-manage-the-settings-for-your-store-apos-s-extensions": "<PERSON><PERSON><PERSON><PERSON> as configurações das extensões da sua loja", "edit-user-information-success": "Sucesso", "edit-user-information-your-information-was-successfully-updated": "Suas informações foram atualizadas com sucesso", "edit-user-information-edit-information": "Editar informações", "edit-user-information-cancel": "<PERSON><PERSON><PERSON>", "edit-user-information-submit-and-close": "Enviar e fechar", "personal-information-back-to-settings": "Voltar para Configurações", "personal-information-personal-information": "Informações pessoais", "personal-information-manage-your-medusa-profile": "Gere<PERSON><PERSON> seu perfil Medusa", "personal-information-language-settings-title": "Idioma", "personal-information-language-settings-description": "Ajuste o idioma do Medusa Admin", "personal-information-language-settings-help-us-translate": "Ajude-nos a traduzir", "personal-information-usage-insights-title": "Aná<PERSON><PERSON> de uso", "usage-insights-disabled": "Desativado", "usage-insights-active": "Ativo", "usage-insights-share-usage-insights-and-help-us-improve-medusa": "Compartilhe análises de uso e nos ajude a melhorar o Medusa", "usage-insights-edit-preferences": "Editar prefer<PERSON>", "usage-insights-success": "Sucesso", "usage-insights-your-information-was-successfully-updated": "Suas informações foram atualizadas com sucesso", "usage-insights-error": "Erro", "usage-insights-cancel": "<PERSON><PERSON><PERSON>", "usage-insights-submit-and-close": "Enviar e fechar", "region-form-title": "<PERSON><PERSON><PERSON><PERSON>", "region-form-europe": "Europa", "region-form-currency-code-is-required": "O código da moeda é obrigatório", "region-form-currency": "<PERSON><PERSON>", "region-form-choose-currency": "Escolha a moeda", "region-form-default-tax-rate": "Taxa de imposto padrão", "region-form-tax-rate-is-required": "A taxa de imposto é obrigatória", "region-form-tax-rate-must-be-equal-to-or-less-than-100": "A taxa de imposto deve ser igual ou inferior a 100", "region-form-default-tax-code": "Código de Imposto Padrão", "region-form-countries": "Países", "region-form-choose-countries": "Escolha os países", "region-form-tax-inclusive-prices": "Preços com impostos incluídos", "region-form-when-enabled-region-prices-will-be-tax-inclusive": "<PERSON>uando habilitado, os preços na região incluirão impostos.", "region-form-payment-providers-are-required": "Provedores de pagamento são obrigatórios", "region-form-payment-providers": "Provedores de Pagamento", "region-form-choose-payment-providers": "Escolha os provedores de pagamento...", "region-form-fulfillment-providers-are-required": "Provedores de atendimento são obrigatórios", "region-form-fulfillment-providers": "Provedores de Atendimento", "region-form-choose-fulfillment-providers": "Escolha os provedores de atendimento...", "shipping-option-card-success": "Sucesso", "shipping-option-card-shipping-option-updated": "Opção de envio atualizada", "shipping-option-card-error": "Erro", "shipping-option-card-edit-shipping-option": "Editar Opção de Envio", "shipping-option-card-fulfillment-method": "Método de Atendimento", "shipping-option-card-cancel": "<PERSON><PERSON><PERSON>", "shipping-option-card-save-and-close": "<PERSON>var e fechar", "shipping-option-card-shipping-option-has-been-deleted": "Opção de envio foi excluída", "shipping-option-card-flat-rate": "Taxa Fixa", "shipping-option-card-calculated": "Calculado", "shipping-option-card-min-subtotal": "Subtotal Mín.:", "shipping-option-card-max-subtotal": "Subtotal Máx.:", "shipping-option-card-admin": "Admin", "shipping-option-card-store": "<PERSON><PERSON>", "shipping-option-card-edit": "<PERSON><PERSON>", "shipping-option-card-delete": "Excluir", "shipping-option-form-visible-in-store": "Visível na loja", "shipping-option-form-enable-or-disable-the-shipping-option-visiblity-in-store": "Habilite ou desabilite a visibilidade da opção de envio na loja.", "shipping-option-form-details": "<PERSON><PERSON><PERSON>", "shipping-option-form-title": "<PERSON><PERSON><PERSON><PERSON>", "shipping-option-form-title-is-required": "Título é obrigatório", "shipping-option-form-price-type": "Tipo de Preço", "shipping-option-form-flat-rate": "Taxa Fixa", "shipping-option-form-calculated": "Calculado", "shipping-option-form-choose-a-price-type": "Escolha um tipo de preço", "shipping-option-form-price": "Preço", "shipping-option-form-shipping-profile": "<PERSON><PERSON><PERSON>vio", "shipping-option-form-choose-a-shipping-profile": "Escolha um perfil de envio", "shipping-option-form-fulfillment-method": "Método de Atendimento", "shipping-option-form-choose-a-fulfillment-method": "Escolha um método de atendimento", "shipping-option-form-requirements": "Requisitos", "shipping-option-form-min-subtotal-must-be-less-than-max-subtotal": "O Subtotal Mín. deve ser menor que o Subtotal Máx.", "shipping-option-form-min-subtotal": "Subtotal Mín.", "shipping-option-form-max-subtotal": "Subtotal Máx.", "shipping-option-form-metadata": "Metadados", "general-section-success": "Sucesso", "general-section-region-was-successfully-updated": "Região foi atualizada com sucesso", "general-section-error": "Erro", "general-section-edit-region-details": "<PERSON><PERSON>i<PERSON>", "general-section-details": "<PERSON><PERSON><PERSON>", "general-section-providers": "Provedores", "general-section-metadata": "Metadados", "general-section-cancel": "<PERSON><PERSON><PERSON>", "general-section-save-and-close": "<PERSON>var e fechar", "edit-something-went-wrong": "Algo deu errado...", "edit-no-region-found": "Não conseguimos encontrar uma região com esse ID. Use o menu à esquerda para selecionar uma região.", "return-shipping-options-success": "Sucesso", "return-shipping-options-shipping-option-created": "Opção de envio criada", "return-shipping-options-error": "Erro", "return-shipping-options-add-return-shipping-option": "Adicionar Opção de Devolução", "return-shipping-options-cancel": "<PERSON><PERSON><PERSON>", "return-shipping-options-save-and-close": "<PERSON><PERSON> e <PERSON>char", "return-shipping-options-return-shipping-options": "Opções de Envio de Devolução", "return-shipping-options-add-option": "Adicionar <PERSON>", "return-shipping-options-enter-specifics-about-available-regional-return-shipment-methods": "Informe detalhes sobre os métodos de envio de devolução regionais disponíveis.", "shipping-options-success": "Sucesso", "shipping-options-shipping-option-created": "Opção de envio criada", "shipping-options-error": "Erro", "shipping-options-add-shipping-option": "Adicionar Opção de Envio", "shipping-options-cancel": "<PERSON><PERSON><PERSON>", "shipping-options-save-and-close": "<PERSON><PERSON> e <PERSON>char", "shipping-options-shipping-options": "Opções de Envio", "shipping-options-add-option": "Adicionar <PERSON>", "shipping-options-enter-specifics-about-available-regional-shipment-methods": "Informe detalhes sobre os métodos de envio regionais disponíveis.", "new-region-created": "Região criada", "new-create-region": "<PERSON><PERSON><PERSON>", "new-details": "<PERSON><PERSON><PERSON>", "new-add-the-region-details": "Adicione os detalhes da região.", "new-providers": "Provedores", "new-add-which-fulfillment-and-payment-providers-should-be-available-in-this-region": "Adicione quais provedores de atendimento e pagamento devem estar disponíveis nesta região.", "region-overview-regions": "Regiões", "region-overview-manage-the-markets-that-you-will-operate-within": "Gerencie os mercados nos quais você vai operar.", "region-overview-not-configured": "Não configurado", "region-overview-fulfillment-providers": "Provedores de Atendimento:", "return-reasons-notification-success": "Sucesso", "return-reasons-created-a-new-return-reason": "Criou um novo motivo de devolução", "return-reasons-success": "Sucesso", "return-reasons-error": "Erro", "return-reasons-cannot-create-a-return-reason-with-an-existing-value": "Não é possível criar um motivo de devolução com um valor existente", "return-reasons-add-reason": "Adiciona<PERSON>", "return-reasons-value-is-required": "O Valor é obrigatório", "return-reasons-value": "Valor", "return-reasons-label-is-required": "O Rótulo é obrigatório", "return-reasons-label": "<PERSON><PERSON><PERSON><PERSON>", "return-reasons-description": "Descrição", "return-reasons-customer-received-the-wrong-size": "Cliente recebeu o tamanho errado", "return-reasons-cancel": "<PERSON><PERSON><PERSON>", "return-reasons-create": "<PERSON><PERSON><PERSON>", "return-reasons-success-title": "Sucesso", "return-reasons-successfully-updated-return-reason": "Motivo de devolução atualizado com sucesso", "return-reasons-duplicate-reason": "Motivo duplicado", "return-reasons-delete-reason": "Excluir motivo", "return-reasons-save": "<PERSON><PERSON>", "return-reasons-details": "<PERSON><PERSON><PERSON>", "return-reasons-delete-return-reason": "Excluir Motivo de Devolução", "return-reasons-are-you-sure-you-want-to-delete-this-return-reason": "Tem certeza de que deseja excluir este motivo de devolução?", "return-reasons-back-to-settings": "Voltar para Configurações", "return-reasons-return-reasons": "Motivos de Devolução", "return-reasons-add-reason-label": "Adiciona<PERSON>", "return-reasons-manage-reasons-for-returned-items": "Gerenciar motivos para itens devolvidos", "taxes-details": "<PERSON><PERSON><PERSON>", "taxes-new-tax-rate": "Nova Taxa de Imposto", "taxes-tax-calculation-settings": "Configurações de Cálculo de Imposto", "taxes-success": "Sucesso", "taxes-successfully-updated-tax-rate": "Taxa de imposto atualizada com sucesso", "taxes-error": "Erro", "taxes-overrides": "Substituições", "taxes-product-rules": "Regras de Produto", "taxes-product-rules-description_one": "Aplica-se a {{count}} productWithCount", "taxes-product-rules-description_other": "Aplicado a {{count}} productWithCount", "taxes-product-type-rules": "Regras de Tipo de Produto", "taxes-product-type-rules-description_one": "Aplicado a {{count}} tipo de typeWithCount", "taxes-product-type-rules-description_other": "Aplicado a {{count}} tipos de typeWithCount", "taxes-shipping-option-rules": "Regras de Opção de Envio", "taxes-applies-to-shipping-option-with-count_one": "Aplicado a {{count}} opção de optionWithCount", "taxes-applies-to-shipping-option-with-count_other": "Aplicado a {{count}} opções de optionWithCount", "taxes-add-overrides": "Adicionar Substituições", "taxes-cancel": "<PERSON><PERSON><PERSON>", "taxes-save": "<PERSON><PERSON>", "taxes-name": "Nome", "taxes-default": "Padrão", "taxes-rate-name": "<PERSON><PERSON> da Taxa", "taxes-tax-rate": "Taxa de Imposto", "taxes-tax-code": "Código de Imposto", "taxes-edit-tax-rate": "Editar Taxa de Imposto", "taxes-back-to-settings": "Voltar para Configurações", "taxes-regions": "Regiões", "taxes-select-the-region-you-wish-to-manage-taxes-for": "Selecione a região que deseja gerenciar impostos", "taxes-go-to-region-settings": "Ir para Configurações da Região", "taxes-successfully-created-tax-rate": "Taxa de imposto criada com sucesso.", "taxes-add-tax-rate": "Adicionar Taxa de Imposto", "taxes-applies-to-product-type-with-count_one": "Aplicado a {{count}} tipo de typeWithCount", "taxes-applies-to-product-type-with-count_other": "Aplicado a {{count}} tipos de typeWithCount", "taxes-create": "<PERSON><PERSON><PERSON>", "taxes-select-products": "Selecionar Produtos", "taxes-select-product-types-label": "Selecionar Tipos de Produto", "taxes-product-types": "Tipos de Produto", "taxes-system-tax-provider": "Provedor de Imposto do Sistema", "taxes-region-tax-settings-were-successfully-updated": "As configurações fiscais da região foram atualizadas com sucesso.", "taxes-tax-provider": "<PERSON><PERSON><PERSON>", "taxes-calculate-taxes-automatically": "Calcular impostos automaticamente?", "taxes-automatically-apply-tax-calculations-to-carts": "<PERSON>uando marcado, o Medusa calculará automaticamente os impostos nos Carrinhos desta Região. Quando desmarcado, você terá que calcular manualmente os impostos no checkout. Impostos manuais são recomendados se você estiver usando um provedor de impostos de terceiros para evitar realizar muitas solicitações.", "taxes-apply-tax-to-gift-cards": "Aplicar impostos a cartões-presente?", "taxes-apply-taxes-to-gift-cards": "<PERSON>uando marcado, os impostos serão aplicados aos cartões-presente no checkout. Em alguns países, as regulamentações fiscais exigem que os impostos sejam aplicados aos cartões-presente na compra.", "taxes-search-products": "Buscar Produtos...", "taxes-select-shipping-option": "Selecionar Opção de Envio", "taxes-shipping-options": "Opções de Envio", "taxes-delete-tax-rate-heading": "Excluir taxa de imposto", "taxes-confirm-delete": "Tem certeza de que deseja excluir esta taxa de imposto?", "taxes-tax-rate-was-deleted": "A taxa de imposto foi excluída.", "taxes-edit": "<PERSON><PERSON>", "taxes-delete-tax-rate": "Excluir Taxa de Imposto", "taxes-delete-rule": "Excluir Regra", "taxes-type": "Tipo", "taxes-products": "<PERSON><PERSON><PERSON>", "taxes-select-individual-products": "Selecionar produtos individuais", "taxes-select-product-types": "Selecionar tipos de produto", "taxes-select-shipping-options": "Selecionar opções de envio", "taxes-back": "Voltar", "taxes-add": "<PERSON><PERSON><PERSON><PERSON>", "taxes-code": "Código", "users-invite-users": "<PERSON><PERSON><PERSON>", "users-back-to-settings": "Voltar para Configurações", "users-the-team": "A Equipe", "users-manage-users-of-your-medusa-store": "Gerencie os usuários de sua loja Medusa", "users-count_one": "{{count}}", "users-count_other": "{{count}}"}