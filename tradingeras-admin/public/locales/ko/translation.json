{"back-button-go-back": "돌아가기", "filter-menu-trigger": "보다", "filter-menu-clear-button": "분명한", "filter-menu-select-item-default-placeholder": "필터 선택", "filter-menu-select-item-clear-button": "선택한 옵션 지우기", "filter-menu-select-item-selected": "선택된", "filter-menu-date-item-before": "전에", "filter-menu-date-item-after": "후에", "filter-menu-date-item-between": "사이", "sales-channels-display-available-count": "<6>{{totalChannelsCount}}</6>개 판매 채널 중 <2>{{availableChannelsCount}}</2>개에서 사용 가능", "activity-drawer-activity": "활동", "activity-drawer-no-notifications-title": "여기는 조용해요...", "activity-drawer-no-notifications-description": "지금은 알림이 없습니다. 하지만 일단 알림을 받으면 알림이 여기에 표시됩니다.", "activity-drawer-error-title": "안 돼...", "activity-drawer-error-description": "알림을 가져오는 중에 문제가 발생했습니다. 계속 노력하겠습니다!", "activity-drawer-processing": "처리 중...", "analytics-config-form-title": "내 사용 데이터를 익명화하세요", "analytics-config-form-description": "사용 데이터를 익명화하도록 선택할 수 있습니다. ", "analytics-config-form-opt-out": "내 사용 데이터 공유 거부", "analytics-config-form-opt-out-later": "귀하는 언제든지 사용 데이터 공유를 거부할 수 있습니다.", "analytics-preferences-success": "성공", "analytics-preferences-your-preferences-were-successfully-updated": "환경설정이 성공적으로 업데이트되었습니다.", "analytics-preferences-error": "오류", "analytics-preferences-help-us-get-better": "우리가 더 나아질 수 있도록 도와주세요", "analytics-preferences-disclaimer": "가장 매력적인 전자상거래 경험을 만들기 위해 우리는 귀하가 Medusa를 어떻게 사용하는지에 대한 통찰력을 얻고 싶습니다. ", "analytics-preferences-documentation": "선적 서류 비치", "analytics-preferences-please-enter-a-valid-email": "유효한 이메일을 입력해주세요", "analytics-preferences-continue": "계속하다", "currency-input-currency": "통화", "currency-input-amount-is-not-valid": "금액이 유효하지 않습니다.", "organisms-success": "성공", "organisms-delete-successful": "삭제 성공", "organisms-are-you-sure-you-want-to-delete": "삭제 하시겠습니까?", "organisms-no-cancel": "아니요, 취소합니다.", "organisms-yes-remove": "예, 삭제합니다", "details-collapsible-hide-additional-details": "추가 세부정보 숨기기", "details-collapsible-show-additional-details": "추가 세부정보 표시", "edit-user-modal-success": "성공", "edit-user-modal-user-was-updated": "사용자가 업데이트되었습니다.", "edit-user-modal-error": "오류", "edit-user-modal-edit-user": "사용자 편집", "edit-user-modal-first-name-label": "이름", "edit-user-modal-first-name-placeholder": "이름...", "edit-user-modal-last-name-label": "성", "edit-user-modal-last-name-placeholder": "성...", "edit-user-modal-email": "이메일", "edit-user-modal-cancel": "취소", "edit-user-modal-save": "구하다", "error-boundary-back-to-dashboard": "대시보드로 돌아가기", "error-boundary-an-unknown-error-occured": "알 수 없는 오류가 발생했습니다.", "error-boundary-bad-request": "잘못된 요청", "error-boundary-you-are-not-logged-in": "로그인되어 있지 않습니다.", "error-boundary-you-do-not-have-permission-perform-this-action": "이 작업을 수행할 권한이 없습니다.", "error-boundary-page-was-not-found": "페이지를 찾을 수 없습니다", "error-boundary-an-unknown-server-error-occured": "알 수 없는 서버 오류가 발생했습니다.", "error-boundary-503": "현재 서버를 사용할 수 없습니다", "error-boundary-500": "불특정 원인으로 인해 오류가 발생했습니다. 이는 당사 측의 기술적인 문제로 인한 것일 가능성이 높습니다. ", "error-boundary-400": "요청 형식이 잘못되었습니다. 요청을 수정한 후 다시 시도해 주세요.", "error-boundary-401": "로그인되어 있지 않습니다. 계속하려면 로그인하세요.", "error-boundary-403": "이 작업을 수행할 권한이 없습니다. 실수라고 생각되면 관리자에게 문의하세요.", "error-boundary-404": "요청하신 페이지를 찾을 수 없습니다. URL을 확인하신 후 다시 시도해 주세요.", "error-boundary-500-2": "서버가 귀하의 요청을 처리할 수 없습니다. 이는 당사 측의 기술적 문제로 인한 것일 가능성이 높습니다. ", "error-boundary-503-2": "서버를 일시적으로 사용할 수 없어 요청을 처리할 수 없습니다. ", "export-modal-title": "데이터 내보내기 초기화", "export-modal-cancel": "취소", "export-modal-export": "내보내다", "file-upload-modal-upload-a-new-photo": "새 사진 업로드", "gift-card-banner-edit": "편집하다", "gift-card-banner-unpublish": "게시 취소", "gift-card-banner-publish": "게시", "gift-card-banner-delete": "삭제", "gift-card-banner-published": "게시됨", "gift-card-banner-unpublished": "널리 알려지지 않은", "gift-card-denominations-section-denomination-added": "명칭이 추가됨", "gift-card-denominations-section-a-new-denomination-was-successfully-added": "새로운 액면가가 성공적으로 추가되었습니다", "gift-card-denominations-section-a-denomination-with-that-default-value-already-exists": "해당 기본값을 가진 금액이 이미 존재합니다.", "gift-card-denominations-section-error": "오류", "gift-card-denominations-section-add-denomination": "명칭 추가", "gift-card-denominations-section-cancel": "취소", "gift-card-denominations-section-save-and-close": "저장하고 닫습니다", "gift-card-denominations-section-denomination-updated": "액면가 업데이트됨", "gift-card-denominations-section-a-new-denomination-was-successfully-updated": "새로운 단위가 성공적으로 업데이트되었습니다", "gift-card-denominations-section-edit-denomination": "명칭 편집", "gift-card-denominations-section-denominations": "교단", "gift-card-denominations-section-denomination": "명칭", "gift-card-denominations-section-in-other-currencies": "다른 통화로", "gift-card-denominations-section-and-more_one": ", 외 {{count}}개", "gift-card-denominations-section-and-more_other": ", 외 {{count}}개", "gift-card-denominations-section-delete-denomination": "명칭 삭제", "gift-card-denominations-section-confirm-delete": "이 액면가를 삭제하시겠습니까?", "gift-card-denominations-section-denomination-deleted": "명칭이 삭제되었습니다.", "gift-card-denominations-section-denomination-was-successfully-deleted": "액면가가 성공적으로 삭제되었습니다", "gift-card-denominations-section-edit": "편집하다", "gift-card-denominations-section-delete": "삭제", "help-dialog-how-can-we-help": "어떻게 도와드릴까요?", "help-dialog-we-usually-respond-in-a-few-hours": "우리는 보통 몇 시간 안에 응답합니다", "help-dialog-subject": "주제", "help-dialog-what-is-it-about": "무슨 내용인가요?...", "help-dialog-write-a-message": "메시지를 작성하세요...", "help-dialog-feel-free-to-join-our-community-of": "우리 커뮤니티에 자유롭게 가입하세요.", "help-dialog-merchants-and-e-commerce-developers": "판매자 및 전자상거래 개발자", "help-dialog-send-a-message": "메세지를 보내다", "invite-modal-success": "성공", "invite-modal-invitation-sent-to": "{{user}}님에게 초대장이 전송되었습니다.", "invite-modal-error": "오류", "invite-modal-member": "회원", "invite-modal-admin": "관리자", "invite-modal-developer": "개발자", "invite-modal-invite-users": "사용자 초대", "invite-modal-email": "이메일", "invite-modal-role": "역할", "invite-modal-select-role": "역할 선택", "invite-modal-cancel": "취소", "invite-modal-invite": "초대하다", "login-card-no-match": "이 자격 증명은 우리 기록과 일치하지 않습니다.", "login-card-log-in-to-medusa": "메두사에 로그인하세요", "login-card-email": "이메일", "login-card-password": "비밀번호", "login-card-forgot-your-password": "비밀번호를 잊어 버렸습니까?", "metadata-add-metadata": "메타데이터 추가", "product-attributes-section-edit-attributes": "속성 편집", "product-attributes-section-dimensions": "치수", "product-attributes-section-configure-to-calculate-the-most-accurate-shipping-rates": "가장 정확한 배송료를 계산하도록 구성", "product-attributes-section-customs": "세관", "product-attributes-section-cancel": "취소", "product-attributes-section-save": "구하다", "product-attributes-section-title": "속성", "product-attributes-section-height": "키", "product-attributes-section-width": "너비", "product-attributes-section-length": "길이", "product-attributes-section-weight": "무게", "product-attributes-section-mid-code": "MID 코드", "product-attributes-section-hs-code": "HS 코드", "product-attributes-section-country-of-origin": "원산지", "product-general-section-success": "성공", "product-general-section-successfully-updated-sales-channels": "판매 채널이 성공적으로 업데이트되었습니다.", "product-general-section-error": "오류", "product-general-section-failed-to-update-sales-channels": "판매 채널을 업데이트하지 못했습니다.", "product-general-section-edit-general-information": "일반 정보 편집", "product-general-section-gift-card": "기프트 카드", "product-general-section-product": "제품", "product-general-section-metadata": "메타데이터", "product-general-section-cancel": "취소", "product-general-section-save": "구하다", "product-general-section-delete": "삭제", "product-general-section-edit-sales-channels": "판매 채널 편집", "product-general-section-published": "게시됨", "product-general-section-draft": "초안", "product-general-section-details": "세부", "product-general-section-subtitle": "부제", "product-general-section-handle": "핸들", "product-general-section-type": "유형", "product-general-section-collection": "수집", "product-general-section-category": "범주", "product-general-section-discountable": "할인 가능", "product-general-section-true": "진실", "product-general-section-false": "거짓", "product-general-section-count_one": "{{세다}}", "product-general-section-count_other": "{{세다}}", "product-general-section-sales-channels": "판매채널", "product-media-section-edit-media": "미디어 편집", "product-media-section-upload-images-error": "이미지를 업로드하는 중에 문제가 발생했습니다.", "product-media-section-file-service-not-configured": "파일 서비스가 구성되어 있지 않을 수 있습니다. ", "product-media-section-error": "오류", "product-media-section-media": "미디어", "product-media-section-add-images-to-your-product": "제품에 이미지를 추가하세요.", "product-media-section-cancel": "취소", "product-media-section-save-and-close": "저장하고 닫습니다", "product-raw-section-raw-gift-card": "원시 기프트 카드", "product-raw-section-raw-product": "원시 제품", "product-thumbnail-section-success": "성공", "product-thumbnail-section-successfully-deleted-thumbnail": "미리보기 이미지를 삭제했습니다.", "product-thumbnail-section-error": "오류", "product-thumbnail-section-edit": "편집하다", "product-thumbnail-section-upload": "업로드", "product-thumbnail-section-upload-thumbnail-error": "미리보기 이미지를 업로드하는 중에 문제가 발생했습니다.", "product-thumbnail-section-you-might-not-have-a-file-service-configured-please-contact-your-administrator": "파일 서비스가 구성되어 있지 않을 수 있습니다. ", "product-thumbnail-section-upload-thumbnail": "썸네일 업로드", "product-thumbnail-section-thumbnail": "썸네일", "product-thumbnail-section-used-to-represent-your-product-during-checkout-social-sharing-and-more": "결제, 소셜 공유 등의 과정에서 제품을 나타내는 데 사용됩니다.", "product-thumbnail-section-cancel": "취소", "product-thumbnail-section-save-and-close": "저장하고 닫습니다", "product-variant-tree-count_one": "{{세다}}", "product-variant-tree-count_other": "{{세다}}", "product-variant-tree-add-prices": "가격 추가", "product-variants-section-add-variant": "변형 추가", "product-variants-section-cancel": "취소", "product-variants-section-save-and-close": "저장하고 닫습니다", "product-variants-section-edit-stock-inventory": "재고 및 재고 편집", "product-variants-section-edit-variant": "변형 편집", "edit-variants-modal-cancel": "취소", "edit-variants-modal-save-and-go-back": "저장하고 돌아가기", "edit-variants-modal-save-and-close": "저장하고 닫습니다", "edit-variants-modal-edit-variant": "변형 편집", "edit-variants-modal-update-success": "변형이 성공적으로 업데이트되었습니다.", "edit-variants-modal-edit-variants": "변형 편집", "edit-variants-modal-product-variants": "제품 변형", "edit-variants-modal-variant": "변종", "edit-variants-modal-inventory": "목록", "product-variants-section-edit-prices": "가격 편집", "product-variants-section-edit-variants": "변형 편집", "product-variants-section-edit-options": "편집 옵션", "product-variants-section-product-variants": "제품 변형", "product-variants-section-error": "오류", "product-variants-section-failed-to-update-product-options": "제품 옵션을 업데이트하지 못했습니다.", "product-variants-section-success": "성공", "product-variants-section-successfully-updated-product-options": "제품 옵션이 업데이트되었습니다.", "product-variants-section-product-options": "제품 옵션", "product-variants-section-option-title": "옵션 제목", "product-variants-section-option-title-is-required": "옵션 제목이 필요합니다", "product-variants-section-add-an-option": "옵션 추가", "product-variants-section-inventory": "목록", "product-variants-section-title": "제목", "product-variants-section-sku": "SKU", "product-variants-section-ean": "이안", "product-variants-section-manage-inventory": "재고 관리", "product-variants-section-duplicate-variant": "중복 변형", "product-variants-section-delete-variant-label": "변형 삭제", "product-variants-section-yes-delete": "예, 삭제합니다", "product-variants-section-delete-variant-heading": "변형 삭제", "product-variants-section-confirm-delete": "이 변형을 삭제하시겠습니까?", "product-variants-section-note-deleting-the-variant-will-also-remove-inventory-items-and-levels": "참고: 변형을 삭제하면 인벤토리 항목과 레벨도 제거됩니다.", "reset-token-card-error": "오류", "reset-token-card-reset-your-password": "비밀번호를 재설정", "reset-token-card-password-reset-description": "아래에 이메일 주소를 입력하시면<1></1>비밀번호 재설정 방법<3></3>에 대한 지침을 보내드립니다.", "reset-token-card-email": "이메일", "reset-token-card-this-is-not-a-valid-email": "유효한 이메일이 아닙니다.", "reset-token-card-send-reset-instructions": "재설정 지침 보내기", "reset-token-card-successfully-sent-you-an-email": "이메일을 성공적으로 보냈습니다.", "reset-token-card-go-back-to-sign-in": "다시 로그인하기", "rma-return-product-table-product-details": "제품 세부 정보", "rma-return-product-table-quantity": "수량", "rma-select-product-table-product-details": "제품 세부 정보", "rma-select-product-table-quantity": "수량", "rma-select-product-table-refundable": "환불 가능", "rma-select-product-table-images-witch-count_one": "{{세다}}", "rma-select-product-table-images-witch-count_other": "{{세다}}", "rma-select-product-table-select-reason": "이유 선택", "sidebar-store": "가게", "sidebar-orders": "명령", "sidebar-products": "제품", "sidebar-categories": "카테고리", "sidebar-customers": "고객", "sidebar-inventory": "목록", "sidebar-discounts": "할인", "sidebar-gift-cards": "기프트 카드", "sidebar-pricing": "가격", "sidebar-settings": "설정", "table-container-soothed-offset_one": "{{sooothedOffset}} - {{count}} {{title}} 중 {{pageSize}}", "table-container-soothed-offset_other": "{{sooothedOffset}} - {{count}} {{title}} 중 {{pageSize}}", "table-container-current-page": "{{sothedPageCount}} 중 {{currentPage}}", "timeline-request-return": "반품요청", "timeline-register-exchange": "거래소 등록", "timeline-register-claim": "청구 등록", "timeline-success": "성공", "timeline-added-note": "메모를 추가했습니다.", "timeline-error": "오류", "timeline-timeline": "타임라인", "upload-modal-new": "새로운", "upload-modal-updates": "업데이트", "upload-modal-drop-your-file-here-or": "여기에 파일을 놓거나", "upload-modal-click-to-browse": "찾아보려면 클릭하세요.", "upload-modal-only-csv-files-are-supported": ".csv 파일만 지원됩니다.", "upload-modal-import-file-title": "{{fileTitle}} 가져오기", "upload-modal-cancel": "취소", "upload-modal-import-list": "가져오기 목록", "add-products-modal-add-products": "제품 추가", "add-products-modal-search-by-name-or-description": "이름이나 설명으로 검색하세요...", "add-products-modal-cancel": "취소", "add-products-modal-save": "구하다", "add-products-modal-product-details": "제품 세부 정보", "add-products-modal-status": "상태", "add-products-modal-variants": "변형", "templates-general": "일반적인", "templates-first-name": "이름", "templates-last-name": "성", "templates-company": "회사", "templates-phone": "핸드폰", "templates-billing-address": "청구 지 주소", "templates-shipping-address": "배송 주소", "templates-address": "주소", "templates-address-1": "주소 1", "templates-address-2": "주소 2", "templates-postal-code": "우편 번호", "templates-city": "도시", "templates-province": "주", "templates-country": "국가", "templates-metadata": "메타데이터", "collection-modal-success": "성공", "collection-modal-successfully-updated-collection": "컬렉션이 업데이트되었습니다.", "collection-modal-error": "오류", "collection-modal-successfully-created-collection": "컬렉션이 생성되었습니다.", "collection-modal-edit-collection": "컬렉션 편집", "collection-modal-add-collection": "컬렉션 추가", "collection-modal-description": "컬렉션을 만들려면 제목과 핸들만 있으면 됩니다.", "collection-modal-details": "세부", "collection-modal-title-label": "제목", "collection-modal-title-placeholder": "색안경", "collection-modal-handle-label": "핸들", "collection-modal-handle-placeholder": "색안경", "collection-modal-slug-description": "컬렉션의 URL Slug입니다. ", "collection-modal-metadata": "메타데이터", "collection-modal-cancel": "취소", "collection-modal-save-collection": "컬렉션 저장", "collection-modal-publish-collection": "컬렉션 게시", "collection-product-table-add-products": "제품 추가", "collection-product-table-products": "제품", "collection-product-table-search-products": "제품 검색", "collection-product-table-cancel": "취소", "collection-product-table-save": "구하다", "collection-product-table-sort-by": "정렬 기준", "collection-product-table-all": "모두", "collection-product-table-newest": "최신", "collection-product-table-oldest": "가장 오래된", "collection-product-table-title": "제목", "collection-product-table-decide-status-published": "게시됨", "collection-product-table-draft": "초안", "collection-product-table-proposed": "제안됨", "collection-product-table-rejected": "거부됨", "collection-product-table-remove-product-from-collection": "컬렉션에서 제품 제거", "collection-product-table-product-removed-from-collection": "컬렉션에서 제품이 제거되었습니다.", "collections-table-delete-collection": "컬렉션 삭제", "collections-table-confirm-delete": "이 컬렉션을 삭제하시겠습니까?", "collections-table-edit": "편집하다", "collections-table-delete": "삭제", "collections-table-title": "제목", "collections-table-handle": "핸들", "collections-table-created-at": "생성 날짜", "collections-table-updated-at": "업데이트 날짜", "collections-table-products": "제품", "customer-group-table-details": "세부", "customer-group-table-delete": "삭제", "customer-group-table-success": "성공", "customer-group-table-group-deleted": "그룹이 삭제되었습니다.", "customer-group-table-error": "오류", "customer-group-table-failed-to-delete-the-group": "그룹을 삭제하지 못했습니다.", "customer-group-table-customer-groups": "고객 그룹", "customer-group-table-delete-from-the-group": "그룹에서 삭제", "customer-group-table-customer-groups-title": "고객 그룹", "customer-group-table-groups": "여러 떼", "customer-group-table-all": "모두", "customer-group-table-edit-customers": "고객 편집", "customer-group-table-customers": "고객", "customer-group-table-cancel": "취소", "customer-group-table-save": "구하다", "customer-orders-table-orders": "명령", "customer-orders-table-transfer-order": "이체 주문", "customer-orders-table-paid": "유급의", "customer-orders-table-awaiting": "대기 중", "customer-orders-table-requires-action": "조치가 필요함", "customer-orders-table-n-a": "해당 없음", "customer-orders-table-fulfilled": "이행됨", "customer-orders-table-shipped": "배송됨", "customer-orders-table-not-fulfilled": "충족되지 않음", "customer-orders-table-partially-fulfilled": "부분적으로 이행됨", "customer-orders-table-partially-shipped": "부분적으로 배송됨", "customer-orders-table-order": "주문하다", "customer-orders-table-remainder-more": "+ {{나머지}}개 더보기", "customer-orders-table-date": "날짜", "customer-orders-table-fulfillment": "이행", "customer-orders-table-status": "상태", "customer-orders-table-total": "총", "customer-table-customers": "고객", "customer-table-edit": "편집하다", "customer-table-details": "세부", "customer-table-date-added": "추가된 날짜", "customer-table-name": "이름", "customer-table-email": "이메일", "customer-table-orders": "명령", "discount-filter-dropdown-filters": "필터", "discount-table-discounts": "할인", "discount-table-search-by-code-or-description": "코드 또는 설명으로 검색...", "discount-table-success": "성공", "discount-table-successfully-copied-discount": "할인을 복사했습니다.", "discount-table-error": "오류", "discount-table-scheduled": "예정됨", "discount-table-expired": "만료됨", "discount-table-active": "활동적인", "discount-table-disabled": "장애가 있는", "discount-table-free-shipping": "무료 배송", "discount-table-code": "암호", "discount-table-description": "설명", "discount-table-amount": "양", "discount-table-status": "상태", "discount-table-redemptions": "상환", "discount-table-delete-discount": "할인 삭제", "discount-table-confirm-delete": "이 할인을 삭제하시겠습니까?", "discount-table-publish": "게시", "discount-table-unpublish": "게시 취소", "discount-table-successfully-published-discount": "할인이 게시되었습니다.", "discount-table-successfully-unpublished-discount": "할인이 게시 취소되었습니다.", "discount-table-duplicate": "복제하다", "discount-table-delete": "삭제", "draft-order-table-draft-orders": "초안 주문", "draft-order-table-completed": "완전한", "draft-order-table-open": "열려 있는", "draft-order-table-draft": "초안", "draft-order-table-order": "주문하다", "draft-order-table-date-added": "추가된 날짜", "draft-order-table-customer": "고객", "draft-order-table-status": "상태", "gift-card-filter-dropdown-is-in-the-last": "마지막에 있어요", "gift-card-filter-dropdown-is-older-than": "보다 나이가 많다", "gift-card-filter-dropdown-is-after": "이후이다", "gift-card-filter-dropdown-is-before": "이전입니다", "gift-card-filter-dropdown-is-equal-to": "동일하다", "gift-card-filter-dropdown-filters": "필터", "gift-card-filter-dropdown-status": "상태", "gift-card-filter-dropdown-payment-status": "지불 상태", "gift-card-filter-dropdown-fulfillment-status": "이행 상태", "gift-card-filter-dropdown-date": "날짜", "gift-card-table-gift-cards": "기프트 카드", "gift-card-table-code": "암호", "gift-card-table-order": "주문하다", "gift-card-table-original-amount": "원래 금액", "gift-card-table-balance": "균형", "gift-card-table-region-has-been-deleted": "지역이 삭제되었습니다.", "gift-card-table-none": "없음", "gift-card-table-created": "만들어진", "image-table-file-name": "파일 이름", "image-table-thumbnail": "썸네일", "image-table-select-thumbnail-image-for-product": "이 제품의 썸네일로 사용할 이미지를 선택하세요.", "inventory-table-inventory-items": "인벤토리 아이템", "inventory-table-actions-adjust-availability": "가용성 조정", "inventory-table-view-product": "제품보기", "inventory-table-success": "성공", "inventory-table-inventory-item-updated-successfully": "인벤토리 항목이 성공적으로 업데이트되었습니다.", "inventory-table-adjust-availability": "가용성 조정", "inventory-table-cancel": "취소", "inventory-table-save-and-close": "저장하고 닫습니다", "inventory-table-item": "안건", "inventory-table-variant": "변종", "inventory-table-sku": "SKU", "inventory-table-reserved": "예약된", "inventory-table-in-stock": "재고", "order-filter-dropdown-filters": "필터", "order-filter-dropdown-status": "상태", "order-filter-dropdown-payment-status": "지불 상태", "order-filter-dropdown-fulfillment-status": "이행 상태", "order-filter-dropdown-regions": "지역", "order-filter-dropdown-sales-channel": "판매채널", "order-filter-dropdown-date": "날짜", "order-table-paid": "유급의", "order-table-awaiting": "대기 중", "order-table-requires-action": "조치가 필요함", "order-table-canceled": "취소 된", "order-table-n-a": "해당 없음", "order-table-order": "주문하다", "order-table-date-added": "추가된 날짜", "order-table-customer": "고객", "order-table-fulfillment": "이행", "order-table-payment-status": "지불 상태", "order-table-sales-channel": "판매채널", "order-table-total": "총", "order-table-filters-complete": "완벽한", "order-table-filters-incomplete": "불완전한", "price-list-table-filters": "필터", "price-list-table-status": "상태", "price-list-table-type": "유형", "price-list-table-price-lists": "가격표", "price-list-table-success": "성공", "price-list-table-successfully-copied-price-list": "가격표를 복사했습니다.", "price-list-table-error": "오류", "price-list-table-delete-price-list": "가격표 삭제", "price-list-table-confirm-delete": "이 가격표를 삭제하시겠습니까?", "price-list-table-successfully-deleted-the-price-list": "가격표를 삭제했습니다.", "price-list-table-successfully-unpublished-price-list": "가격표 게시가 취소되었습니다.", "price-list-table-successfully-published-price-list": "가격표가 성공적으로 게시되었습니다.", "price-list-table-unpublish": "게시 취소", "price-list-table-publish": "게시", "price-list-table-delete": "삭제", "price-list-table-name": "이름", "price-list-table-description": "설명", "price-list-table-groups": "여러 떼", "price-list-table-other-more": "+ {{other}}개 더보기", "price-overrides-apply-overrides-on-selected-variants": "선택한 변형에 재정의 적용", "price-overrides-apply-on-all-variants": "모든 변형에 적용", "price-overrides-prices": "물가", "price-overrides-cancel": "취소", "price-overrides-save-and-close": "저장하고 닫습니다", "price-overrides-show-regions": "지역 표시", "product-table-products": "제품", "product-table-copy-success": "성공", "product-table-copy-created-a-new-product": "새로운 제품을 만들었습니다", "product-table-copy-error": "오류", "product-table-delete-product": "제품 삭제", "product-table-confirm-delete": "이 제품을 삭제하시겠습니까?", "product-table-edit": "편집하다", "product-table-unpublish": "게시 취소", "product-table-publish": "게시", "product-table-draft": "초안", "product-table-published": "출판됨", "product-table-success": "성공", "product-table-successfully-unpublished-product": "제품이 게시 취소되었습니다.", "product-table-successfully-published-product": "성공적으로 게시된 제품", "product-table-error": "오류", "product-table-duplicate": "복제하다", "product-table-delete": "삭제", "product-table-proposed": "제안됨", "product-table-published-title": "게시됨", "product-table-rejected": "거부됨", "product-table-draft-title": "초안", "product-table-name": "이름", "product-table-collection": "수집", "product-table-status": "상태", "product-table-availability": "유효성", "product-table-inventory": "목록", "product-table-inventory-in-stock-count_one": "{{count}}개 변형 재고 있음", "product-table-inventory-in-stock-count_other": "{{count}}개 변형 재고 있음", "reservation-form-location": "위치", "reservation-form-choose-where-you-wish-to-reserve-from": "예약하고 싶은 곳을 선택하세요.", "reservation-form-item-to-reserve": "예약아이템", "reservation-form-select-the-item-that-you-wish-to-reserve": "예약하고 싶은 상품을 선택하세요.", "reservation-form-item": "안건", "reservation-form-in-stock": "재고", "reservation-form-available": "사용 가능", "reservation-form-reserve": "예약하다", "reservation-form-remove-item": "항목 삭제", "reservation-form-description": "설명", "reservation-form-what-type-of-reservation-is-this": "이것은 어떤 종류의 예약인가요?", "reservations-table-reservations": "전세", "reservations-table-edit": "편집하다", "reservations-table-delete": "삭제", "reservations-table-confirm-delete": "이 예약을 삭제하시겠습니까?", "reservations-table-remove-reservation": "예약 삭제", "reservations-table-reservation-has-been-removed": "예약이 삭제되었습니다", "new-success": "성공", "new-successfully-created-reservation": "예약이 생성되었습니다.", "new-error": "오류", "new-cancel": "취소", "new-save-reservation": "예약 저장", "new-reserve-item": "아이템 예약", "new-metadata": "메타데이터", "reservations-table-order-id": "주문 아이디", "reservations-table-description": "설명", "reservations-table-created": "만들어진", "reservations-table-quantity": "수량", "search-modal-start-typing-to-search": "검색하려면 입력을 시작하세요...", "search-modal-clear-search": "검색 지우기", "search-modal-or": "또는", "search-modal-to-navigate": "길을 찾다", "search-modal-to-select-and": "선택하고,", "search-modal-to-search-anytime": "언제든지 검색하려면", "templates-settings": "설정", "templates-manage-the-settings-for-your-medusa-store": "Medusa 매장 설정을 관리하세요.", "transfer-orders-modal-info": "정보", "transfer-orders-modal-customer-is-already-the-owner-of-the-order": "고객은 이미 주문의 소유자입니다.", "transfer-orders-modal-success": "성공", "transfer-orders-modal-successfully-transferred-order-to-different-customer": "주문을 다른 고객에게 성공적으로 이전했습니다.", "transfer-orders-modal-error": "오류", "transfer-orders-modal-could-not-transfer-order-to-different-customer": "다른 고객에게 주문을 이전할 수 없습니다.", "transfer-orders-modal-transfer-order": "이체 주문", "transfer-orders-modal-order": "주문하다", "transfer-orders-modal-current-owner": "현재 소유자", "transfer-orders-modal-the-customer-currently-related-to-this-order": "현재 이 주문과 관련된 고객", "transfer-orders-modal-new-owner": "새 소유자", "transfer-orders-modal-the-customer-to-transfer-this-order-to": "이 주문을 전달할 고객", "transfer-orders-modal-cancel": "취소", "transfer-orders-modal-confirm": "확인하다", "templates-edit-user": "사용자 편집", "templates-remove-user": "사용자 제거", "templates-resend-invitation": "초대장 다시 보내기", "templates-success": "성공", "templates-invitiation-link-has-been-resent": "초대 링크가 다시 전송되었습니다", "templates-copy-invite-link": "초대 링크 복사", "templates-invite-link-copied-to-clipboard": "초대 링크가 클립보드에 복사되었습니다.", "templates-remove-invitation": "초대 삭제", "templates-expired": "만료됨", "templates-pending": "보류 중", "templates-all": "모두", "templates-member": "회원", "templates-admin": "관리자", "templates-no-team-permissions": "팀 권한 없음", "templates-status": "상태", "templates-active": "활동적인", "templates-name": "이름", "templates-email": "이메일", "templates-team-permissions": "팀 권한", "templates-confirm-remove": "이 사용자를 삭제하시겠습니까?", "templates-remove-user-heading": "사용자 삭제", "templates-user-has-been-removed": "사용자가 삭제되었습니다.", "templates-confirm-remove-invite": "이 초대를 삭제하시겠습니까?", "templates-remove-invite": "초대 삭제", "templates-invitiation-has-been-removed": "초대장이 삭제되었습니다.", "multiselect-choose-categories": "카테고리 선택", "domain-categories-multiselect-selected-with-counts_one": "{{세다}}", "domain-categories-multiselect-selected-with-counts_other": "{{세다}}", "details-success": "성공", "details-updated-products-in-collection": "컬렉션의 업데이트된 제품", "details-error": "오류", "details-back-to-collections": "컬렉션으로 돌아가기", "details-edit-collection": "컬렉션 편집", "details-delete": "삭제", "details-metadata": "메타데이터", "details-edit-products": "제품 편집", "details-products-in-this-collection": "이 컬렉션의 제품", "details-raw-collection": "원시 컬렉션", "details-delete-collection": "컬렉션 삭제", "details-successfully-deleted-collection": "컬렉션을 삭제했습니다.", "details-yes-delete": "예, 삭제합니다", "details-successfully-updated-customer": "고객을 업데이트했습니다.", "details-customer-details": "고객 정보", "details-general": "일반적인", "details-first-name": "이름", "details-lebron": "르브론", "details-last-name": "성", "details-james": "제임스", "details-email": "이메일", "details-phone-number": "전화 번호", "details-cancel": "취소", "details-save-and-close": "저장하고 닫습니다", "details-edit": "편집하다", "details-back-to-customers": "고객으로 돌아가기", "details-first-seen": "처음 본", "details-phone": "핸드폰", "details-orders": "명령", "details-user": "사용자", "details-orders_one": "주문 {{count}}", "details-orders_other": "주문 {{count}}", "details-an-overview-of-customer-orders": "고객 주문 개요", "details-raw-customer": "원시 고객", "groups-group-updated": "그룹이 업데이트되었습니다.", "groups-group-created": "그룹이 생성되었습니다.", "groups-the-customer-group-has-been-updated": "고객 그룹이 업데이트되었습니다.", "groups-the-customer-group-has-been-created": "고객그룹이 생성되었습니다.", "groups-edit-customer-group": "고객 그룹 편집", "groups-create-a-new-customer-group": "새 고객 그룹 생성", "groups-details": "세부", "groups-metadata": "메타데이터", "groups-cancel": "취소", "groups-edit-group": "그룹 편집", "groups-publish-group": "게시 그룹", "groups-no-customers-in-this-group-yet": "이 그룹에는 아직 고객이 없습니다.", "groups-customers": "고객", "groups-edit": "편집하다", "groups-delete": "삭제", "groups-yes-delete": "예, 삭제합니다", "groups-delete-the-group": "그룹 삭제", "groups-group-deleted": "그룹이 삭제되었습니다.", "groups-confirm-delete-customer-group": "이 고객 그룹을 삭제하시겠습니까?", "groups-back-to-customer-groups": "고객 그룹으로 돌아가기", "groups-new-group": "새 그룹", "add-condition-conditions-were-successfully-added": "조건이 추가되었습니다.", "add-condition-discount-conditions-updated": "할인 조건이 업데이트되었습니다.", "add-condition-use-conditions-must-be-used-within-a-conditions-provider": "useConditions는 ConditionsProvider 내에서 사용해야 합니다.", "collections-search": "찾다...", "collections-cancel": "취소", "collections-save-and-go-back": "저장하고 돌아가기", "collections-save-and-close": "저장하고 닫습니다", "customer-groups-search": "찾다...", "customer-groups-cancel": "취소", "customer-groups-save-and-go-back": "저장하고 돌아가기", "customer-groups-save-and-close": "저장하고 닫습니다", "product-types-search": "찾다...", "product-types-cancel": "취소", "product-types-save-and-go-back": "저장하고 돌아가기", "product-types-save-and-close": "저장하고 닫습니다", "products-search": "찾다...", "products-cancel": "취소", "products-save-and-go-back": "저장하고 돌아가기", "products-save-and-close": "저장하고 닫습니다", "tags-search": "찾다...", "tags-cancel": "취소", "tags-save-and-go-back": "저장하고 돌아가기", "tags-save-and-close": "저장하고 닫습니다", "edit-condition-add-conditions": "조건 추가", "edit-condition-selected-with-count_one": "{{세다}}", "edit-condition-selected-with-count_other": "{{세다}}", "edit-condition-deselect": "선택 취소", "edit-condition-remove": "제거하다", "edit-condition-add": "추가하다", "edit-condition-title": "할인 조건에서 {{type}} 수정", "edit-condition-close": "닫다", "edit-condition-success": "성공", "edit-condition-the-resources-were-successfully-added": "리소스가 성공적으로 추가되었습니다.", "edit-condition-error": "오류", "edit-condition-failed-to-add-resources": "리소스를 추가하지 못했습니다.", "edit-condition-the-resources-were-successfully-removed": "리소스가 성공적으로 제거되었습니다.", "edit-condition-failed-to-remove-resources": "리소스를 제거하지 못했습니다.", "edit-condition-use-edit-condition-context-must-be-used-within-an-edit-condition-provider": "useEditConditionContext는 EditConditionProvider 내에서 사용해야 합니다.", "conditions-conditions": "정황", "conditions-add-condition-label": "조건 추가", "conditions-this-discount-has-no-conditions": "이 할인에는 조건이 없습니다.", "conditions-success": "성공", "conditions-condition-removed": "조건이 제거되었습니다.", "conditions-error": "오류", "conditions-edit-condition": "조건 편집", "conditions-delete-condition": "조건 삭제", "conditions-discount-is-applicable-to-specific-products": "특정상품에 할인이 적용됩니다", "conditions-discount-is-applicable-to-specific-collections": "특정 컬렉션에 할인이 적용됩니다.", "conditions-discount-is-applicable-to-specific-product-tags": "특정 제품 태그에 할인이 적용됩니다.", "conditions-discount-is-applicable-to-specific-product-types": "특정 제품 유형에 할인이 적용됩니다.", "conditions-discount-is-applicable-to-specific-customer-groups": "특정 고객 그룹에 할인이 적용됩니다.", "configurations-success": "성공", "configurations-discount-updated-successfully": "할인이 업데이트되었습니다.", "configurations-error": "오류", "configurations-edit-configurations": "구성 편집", "configurations-cancel": "취소", "configurations-save": "구하다", "configurations-configurations": "구성", "configurations-start-date": "시작일", "configurations-end-date": "종료일", "configurations-delete-configuration": "구성 삭제", "configurations-discount-end-date-removed": "할인 종료일이 삭제되었습니다.", "configurations-number-of-redemptions": "상환 횟수", "configurations-redemption-limit-removed": "상환 한도가 제거되었습니다.", "configurations-delete-setting": "설정 삭제", "configurations-discount-duration-removed": "할인 기간이 삭제되었습니다.", "general-success": "성공", "general-discount-updated-successfully": "할인이 업데이트되었습니다.", "general-error": "오류", "general-edit-general-information": "일반 정보 편집", "general-details": "세부", "general-metadata": "메타데이터", "general-cancel": "취소", "general-save-and-close": "저장하고 닫습니다", "general-delete-promotion": "프로모션 삭제", "general-confirm-delete-promotion": "이 프로모션을 삭제하시겠습니까?", "general-promotion-deleted-successfully": "프로모션이 삭제되었습니다.", "general-discount-published-successfully": "할인이 성공적으로 게시되었습니다.", "general-discount-drafted-successfully": "할인 초안이 성공적으로 작성되었습니다.", "general-delete-discount": "할인 삭제", "general-template-discount": "템플릿 할인", "general-published": "게시됨", "general-draft": "초안", "general-discount-amount": "할인 량", "general-valid-regions": "유효한 지역", "general-total-redemptions": "총 상환", "general-free-shipping": "무료 배송", "general-unknown-discount-type": "알 수 없는 할인 유형", "details-discount-deleted": "할인이 삭제되었습니다.", "details-confirm-delete-discount": "이 할인을 삭제하시겠습니까?", "details-delete-discount": "할인 삭제", "details-back-to-discounts": "할인으로 돌아가기", "details-raw-discount": "원시 할인", "discounts-add-discount": "할인 추가", "discount-form-add-conditions": "조건 추가", "discount-form-choose-a-condition-type": "조건 유형을 선택하세요", "discount-form-you-can-only-add-one-of-each-type-of-condition": "각 조건 유형 중 하나만 추가할 수 있습니다.", "discount-form-you-cannot-add-any-more-conditions": "더 이상 조건을 추가할 수 없습니다.", "discount-form-cancel": "취소", "discount-form-save": "구하다", "add-condition-tables-cancel": "취소", "add-condition-tables-save-and-add-more": "저장하고 더 추가하세요", "add-condition-tables-save-and-close": "저장하고 닫습니다", "add-condition-tables-search-by-title": "제목으로 검색...", "add-condition-tables-search-groups": "그룹 검색...", "add-condition-tables-search-products": "제품 검색...", "add-condition-tables-search-by-tag": "태그로 검색...", "add-condition-tables-search-by-type": "유형으로 검색...", "details-condition-tables-search-by-title": "제목으로 검색...", "details-condition-tables-search-groups": "그룹 검색...", "details-condition-tables-cancel": "취소", "details-condition-tables-save-and-add-more": "저장하고 더 추가하세요", "details-condition-tables-save-and-close": "저장하고 닫습니다", "details-condition-tables-search-products": "제품 검색...", "details-condition-tables-search-by-tag": "태그로 검색...", "details-condition-tables-search-by-type": "유형으로 검색...", "edit-condition-tables-search-by-title": "제목으로 검색...", "edit-condition-tables-title": "제목", "edit-condition-tables-search-groups": "그룹 검색...", "edit-condition-tables-cancel": "취소", "edit-condition-tables-delete-condition": "조건 삭제", "edit-condition-tables-save": "구하다", "edit-condition-tables-search-products": "제품 검색...", "edit-condition-tables-search-by-tag": "태그로 검색...", "edit-condition-tables-search-by-type": "유형으로 검색...", "shared-title": "제목", "shared-products": "제품", "shared-applies-to-the-selected-items": "선택한 항목에 적용됩니다.", "shared-applies-to-all-items-except-the-selected-items": "선택한 항목을 제외한 모든 항목에 적용됩니다.", "shared-members": "회원", "shared-status": "상태", "shared-variants": "변형", "shared-tag": "꼬리표", "shared-type": "유형", "edit-conditions-modal-title": "{{제목}} 수정", "form-use-discount-form-must-be-a-child-of-discount-form-context": "useDiscountForm은 할인FormContext의 하위 항목이어야 합니다.", "discount-form-error": "오류", "discount-form-save-as-draft": "임시 보관함에 저장", "discount-form-publish-discount": "게시 할인", "discount-form-create-new-discount": "새 할인 만들기", "discount-form-discount-type": "할인 유형", "discount-form-select-a-discount-type": "할인 유형을 선택하세요", "discount-form-allocation": "배당", "discount-form-general": "일반적인", "discount-form-configuration": "구성", "discount-form-discount-code-application-disclaimer": "할인 코드는 게시 버튼을 누른 순간부터 적용되며, 그대로 두면 영원히 적용됩니다.", "discount-form-conditions": "정황", "discount-form-discount-code-apply-to-all-products-if-left-untouched": "그대로 두면 모든 제품에 할인 코드가 적용됩니다.", "discount-form-add-conditions-to-your-discount": "할인에 조건 추가", "discount-form-metadata": "메타데이터", "discount-form-metadata-usage-description": "메타데이터를 사용하면 할인에 추가 정보를 추가할 수 있습니다.", "condition-item-remainder-more": "+{{나머지}}개 더보기", "conditions-edit": "편집하다", "conditions-product": "제품", "conditions-collection": "수집", "conditions-tag": "꼬리표", "conditions-customer-group": "고객그룹", "conditions-type": "유형", "conditions-add-condition": "조건 추가", "sections-start-date": "시작일", "sections-schedule-the-discount-to-activate-in-the-future": "나중에 활성화할 할인을 예약하세요.", "sections-select-discount-start-date": "나중에 할인이 활성화되도록 예약하려면 여기에서 시작 날짜를 설정하세요. 그렇지 않으면 할인이 즉시 활성화됩니다.", "sections-start-time": "시작 시간", "sections-discount-has-an-expiry-date": "할인에 유효기간이 있나요?", "sections-schedule-the-discount-to-deactivate-in-the-future": "나중에 비활성화할 할인을 예약하세요.", "sections-select-discount-end-date": "나중에 할인이 비활성화되도록 예약하려면 여기에서 만료 날짜를 설정할 수 있습니다.", "sections-expiry-date": "만료일", "sections-expiry-time": "만료 시간", "sections-limit-the-number-of-redemptions": "사용 횟수를 제한하시겠습니까?", "sections-limit-applies-across-all-customers-not-per-customer": "한도는 고객별로 적용되는 것이 아니라 모든 고객에게 적용됩니다.", "sections-limit-discount-number-of-uses": "고객이 이 할인을 사용할 수 있는 횟수를 제한하려면 여기에서 제한을 설정할 수 있습니다.", "sections-number-of-redemptions": "상환 횟수", "sections-availability-duration": "가용성 기간?", "sections-set-the-duration-of-the-discount": "할인 기간을 설정합니다.", "sections-select-a-discount-type": "할인 유형을 선택하세요", "sections-total-amount": "총액", "sections-apply-to-the-total-amount": "총금액에 적용", "sections-item-specific": "특정 품목", "sections-apply-to-every-allowed-item": "허용된 모든 항목에 적용", "sections-percentage": "백분율", "sections-fixed-amount": "정액", "sections-discount-in-whole-numbers": "정수 할인", "sections-you-can-only-select-one-valid-region-if-you-want-to-use-the-fixed-amount-type": "고정금액 유형을 사용하려면 유효한 지역을 하나만 선택할 수 있습니다.", "sections-free-shipping": "무료 배송", "sections-override-delivery-amount": "배송 금액 무시", "sections-at-least-one-region-is-required": "리전이 1개 이상 필요합니다.", "sections-choose-valid-regions": "유효한 지역을 선택하세요", "sections-code": "암호", "sections-summersale-10": "여름 세일10", "sections-code-is-required": "코드가 필요합니다", "sections-amount-is-required": "금액은 필수 항목입니다.", "sections-amount": "양", "sections-customer-invoice-code": "고객이 결제 시 입력하는 코드입니다. ", "sections-uppercase-letters-and-numbers-only": "대문자와 숫자만 가능합니다.", "sections-description": "설명", "sections-summer-sale-2022": "2022년 여름 세일", "sections-this-is-a-template-discount": "템플릿 할인입니다", "sections-template-discounts-description": "템플릿 할인을 사용하면 할인 그룹 전체에 사용할 수 있는 규칙 집합을 정의할 수 있습니다. ", "discount-form-product": "제품", "discount-form-only-for-specific-products": "특정 제품에만 해당", "discount-form-choose-products": "제품을 선택하세요", "discount-form-customer-group": "고객그룹", "discount-form-only-for-specific-customer-groups": "특정 고객 그룹에만 해당", "discount-form-choose-groups": "그룹 선택", "discount-form-tag": "꼬리표", "discount-form-only-for-specific-tags": "특정 태그에만 해당", "discount-form-collection": "수집", "discount-form-only-for-specific-product-collections": "특정 제품 컬렉션에만 해당", "discount-form-choose-collections": "컬렉션 선택", "discount-form-type": "유형", "discount-form-only-for-specific-product-types": "특정 제품 유형에만 해당", "discount-form-choose-types": "유형 선택", "utils-products": "제품", "utils-groups": "여러 떼", "utils-tags": "태그", "utils-collections": "컬렉션", "utils-types": "종류", "gift-cards-created-gift-card": "기프트 카드가 생성되었습니다.", "gift-cards-custom-gift-card-was-created-successfully": "맞춤 기프트 카드가 생성되었습니다.", "gift-cards-error": "오류", "gift-cards-custom-gift-card": "맞춤형 기프트 카드", "gift-cards-details": "세부", "gift-cards-receiver": "수화기", "gift-cards-cancel": "취소", "gift-cards-create-and-send": "작성 및 보내기", "details-updated-gift-card": "업데이트된 기프트 카드", "details-gift-card-was-successfully-updated": "기프트카드가 업데이트되었습니다.", "details-failed-to-update-gift-card": "기프트 카드를 업데이트하지 못했습니다.", "details-edit-gift-card": "기프트 카드 수정", "details-details": "세부", "details-edit-details": "세부정보 수정", "details-update-balance-label": "잔액 업데이트", "details-updated-status": "업데이트된 상태", "details-successfully-updated-the-status-of-the-gift-card": "기프트 카드 상태가 업데이트되었습니다.", "details-back-to-gift-cards": "기프트 카드로 돌아가기", "details-original-amount": "원래 금액", "details-balance": "균형", "details-region": "지역", "details-expires-on": "만료 날짜", "details-created": "만들어진", "details-raw-gift-card": "원시 기프트 카드", "details-balance-updated": "잔액이 업데이트되었습니다.", "details-gift-card-balance-was-updated": "기프트 카드 잔액이 업데이트되었습니다.", "details-failed-to-update-balance": "잔액을 업데이트하지 못했습니다.", "details-update-balance": "잔액 업데이트", "manage-back-to-gift-cards": "기프트 카드로 돌아가기", "gift-cards-please-enter-a-name-for-the-gift-card": "기프트 카드의 이름을 입력하세요.", "gift-cards-please-add-at-least-one-denomination": "단위를 하나 이상 추가하세요.", "gift-cards-denominations": "교단", "gift-cards-success": "성공", "gift-cards-successfully-created-gift-card": "기프트 카드가 생성되었습니다.", "gift-cards-create-gift-card": "기프트 카드 만들기", "gift-cards-gift-card-details": "기프트 카드 세부정보", "gift-cards-name": "이름", "gift-cards-the-best-gift-card": "최고의 기프트 카드", "gift-cards-description": "설명", "gift-cards-the-best-gift-card-of-all-time": "역대 최고의 기프트 카드", "gift-cards-thumbnail": "썸네일", "gift-cards-delete": "삭제", "gift-cards-size-recommended": "1200 x 1600(3:4) 권장, 각각 최대 10MB", "gift-cards-amount": "양", "gift-cards-add-denomination": "명칭 추가", "gift-cards-create-publish": "만들기 및 게시", "gift-cards-successfully-updated-gift-card": "기프트 카드가 업데이트되었습니다.", "gift-cards-gift-cards": "기프트 카드", "gift-cards-manage": "Medusa 매장의 기프트 카드를 관리하세요", "gift-cards-are-you-ready-to-sell-your-first-gift-card": "첫 번째 기프트 카드를 판매할 준비가 되셨나요?", "gift-cards-no-gift-card-has-been-added-yet": "아직 기프트 카드가 추가되지 않았습니다.", "gift-cards-history": "역사", "gift-cards-see-the-history-of-purchased-gift-cards": "구매한 기프트 카드 내역 보기", "gift-cards-successfully-deleted-gift-card": "기프트 카드를 삭제했습니다.", "gift-cards-yes-delete": "예, 삭제합니다", "gift-cards-delete-gift-card": "기프트 카드 삭제", "inventory-filters": "필터", "address-form-address": "주소", "address-form-company": "회사", "address-form-address-1": "주소 1", "address-form-this-field-is-required": "이 필드는 필수입니다", "address-form-address-2": "주소 2", "address-form-postal-code": "우편 번호", "address-form-city": "도시", "address-form-country": "국가", "edit-sales-channels-edit-channels": "채널 편집", "edit-sales-channels-add-channels": "채널 추가", "general-form-location-name": "위치 명", "general-form-flagship-store-warehouse": "플래그십 스토어, 창고", "general-form-name-is-required": "이름은 필수입니다", "location-card-delete-location": "위치 삭제", "location-card-confirm-delete": "이 위치를 삭제하시겠습니까? ", "location-card-success": "성공", "location-card-location-deleted-successfully": "위치가 삭제되었습니다.", "location-card-error": "오류", "location-card-edit-details": "세부정보 수정", "location-card-delete": "삭제", "location-card-connected-sales-channels": "연결된 판매 채널", "sales-channels-form-add-sales-channels": "판매 채널 추가", "sales-channels-form-edit-channels": "채널 편집", "sales-channels-section-not-connected-to-any-sales-channels-yet": "아직 어떤 판매 채널에도 연결되어 있지 않습니다", "edit-success": "성공", "edit-location-edited-successfully": "위치가 수정되었습니다.", "edit-error": "오류", "edit-edit-location-details": "위치 세부정보 편집", "edit-metadata": "메타데이터", "edit-cancel": "취소", "edit-save-and-close": "저장하고 닫습니다", "new-location-added-successfully": "위치가 추가되었습니다.", "new-location-created": "위치가 생성되었지만 판매 채널을 연결하는 중에 오류가 발생했습니다.", "new-cancel-location-changes": "저장되지 않은 변경사항을 취소하시겠습니까?", "new-yes-cancel": "예, 취소합니다", "new-no-continue-creating": "아니요, 계속 만듭니다.", "new-add-location": "위치 추가", "new-add-new-location": "새 위치 추가", "new-general-information": "일반 정보", "new-location-details": "이 위치에 대한 세부정보를 지정하세요.", "new-select-location-channel": "이 위치의 품목을 구매할 수 있는 판매 채널을 지정합니다.", "oauth-complete-installation": "설치 완료", "claim-type-form-refund": "환불하다", "claim-type-form-replace": "바꾸다", "items-to-receive-form-items-to-receive": "받을 품목", "items-to-receive-form-product": "제품", "items-to-receive-form-quantity": "수량", "items-to-receive-form-refundable": "환불 가능", "add-return-reason-reason-for-return": "반품 사유", "add-return-reason-reason": "이유", "add-return-reason-choose-a-return-reason": "반품 이유를 선택하세요", "add-return-reason-note": "메모", "add-return-reason-product-was-damaged-during-shipping": "배송중 제품이 파손되었습니다", "add-return-reason-cancel": "취소", "add-return-reason-save-and-go-back": "저장하고 돌아가기", "add-return-reason-select-reason-title": "이유 선택", "add-return-reason-edit-reason": "이유 수정", "add-return-reason-select-reason": "이유를 선택하세요", "items-to-return-form-items-to-claim": "청구할 항목", "items-to-return-form-items-to-return": "반품할 품목", "items-to-return-form-product": "제품", "items-to-return-form-quantity": "수량", "items-to-return-form-refundable": "환불 가능", "add-additional-items-screen-go-back": "돌아가기", "add-additional-items-screen-add-products": "제품 추가", "add-additional-items-screen-add-product-variants": "제품 변형 추가", "add-additional-items-screen-search-products": "제품 검색", "add-additional-items-screen-variant-price-missing": "이 변형에는 이 주문의 지역/통화에 대한 가격이 없으므로 선택할 수 없습니다.", "add-additional-items-screen-stock": "재고", "add-additional-items-screen-price": "가격", "add-additional-items-screen-price-overridden-in-price-list-applicable-to-this-order": "이 주문에 적용할 수 있는 가격표에서 가격이 재정의되었습니다.", "items-to-send-form-items-to-send": "보낼 품목", "items-to-send-form-add-products": "제품 추가", "items-to-send-form-product": "제품", "items-to-send-form-quantity": "수량", "items-to-send-form-price": "가격", "items-to-send-form-price-overridden-in-price-list-applicable-to-this-order": "이 주문에 적용할 수 있는 가격표에서 가격이 재정의되었습니다.", "refund-amount-form-cancel-editing-refund-amount": "환불 금액 수정 취소", "refund-amount-form-edit-refund-amount": "환불 금액 수정", "refund-amount-form-refund-amount-cannot-be-negative": "환불 금액은 음수일 수 없습니다.", "refund-amount-form-the-refund-amount-must-be-at-least-0": "환불 금액은 0 이상이어야 합니다.", "reservation-indicator-awaiting-reservation-count": "{{awaitingReservation}} 항목이 예약되지 않았습니다.", "reservation-indicator-this-item-has-been-fulfilled": "이 항목이 충족되었습니다.", "edit-reservation-button-quantity-item-location-name": "{{수량}}개 품목: ${{locationName}}", "reservation-indicator-edit-reservation": "예약 수정", "rma-summaries-claimed-items": "청구된 항목", "rma-summaries-replacement-items": "교체 품목", "rma-summaries-customer-refund-description": "교체 품목 및 배송 비용은 공제되지 않으므로 고객은 청구된 품목에 대해 전액 환불을 받습니다. ", "rma-summaries-refund-amount": "환불 금액", "rma-summaries-the-customer-will-be-refunded-once-the-returned-items-are-received": "반품된 상품이 수령되면 고객에게 환불됩니다.", "rma-summaries-the-customer-will-be-refunded-immediately": "고객에게 즉시 환불됩니다.", "rma-summaries-receiving": "전수", "rma-summaries-free": "무료", "send-notification-form-return": "반품", "send-notification-form-exchange": "교환", "send-notification-form-claim": "주장하다", "send-notification-form-send-notifications": "알림 보내기", "send-notification-form-if-unchecked-the-customer-will-not-receive-communication": "선택 취소하면 고객은 이 {{제목}}에 대한 커뮤니케이션을 받을 수 없습니다.", "shipping-address-form-shipping-address": "배송 주소", "shipping-address-form-ship-to-a-different-address": "다른 주소로 배송", "shipping-address-form-cancel": "취소", "shipping-address-form-save-and-go-back": "저장하고 돌아가기", "shipping-address-form-shipping-information": "배송 정보", "shipping-form-shipping-for-return-items": "반품 상품 배송", "shipping-form-shipping-for-replacement-items": "교체 품목 배송", "shipping-form-shipping-method-is-required": "배송 방법이 필요합니다", "shipping-form-choose-shipping-method": "배송 방법을 선택하세요", "shipping-form-shipping-method": "배송 방법", "shipping-form-add-custom-price": "맞춤 가격 추가", "shipping-form-return-shipping-for-items-claimed-by-the-customer-is-complimentary": "고객이 청구한 품목의 반품 배송비는 무료입니다.", "shipping-form-shipping-for-replacement-items-is-complimentary": "교체품 배송은 무료입니다.", "components-decrease-quantity": "수량 감소", "components-increase-quantity": "수량 늘리기", "details-successfully-updated-address": "주소가 업데이트되었습니다.", "details-billing-address": "청구 지 주소", "details-shipping-address": "배송 주소", "details-contact": "연락하다", "details-location": "위치", "claim-are-you-sure-you-want-to-close": "정말로 닫으시겠어요?", "claim-you-have-unsaved-changes-are-you-sure-you-want-to-close": "저장되지 않은 변경사항이 있습니다. 닫으시겠습니까?", "claim-please-select-a-reason": "이유를 선택해주세요.", "claim-a-shipping-method-for-replacement-items-is-required": "교체 품목의 배송 방법이 필요합니다.", "claim-successfully-created-claim": "소유권 주장이 성공적으로 생성되었습니다.", "claim-created": "주문 #{{display_id}}에 대한 청구가 성공적으로 생성되었습니다.", "claim-error-creating-claim": "소유권 주장을 만드는 중에 오류가 발생했습니다.", "claim-create-claim": "소유권 주장 만들기", "claim-location": "위치", "claim-choose-which-location-you-want-to-return-the-items-to": "물품을 반품할 위치를 선택하세요.", "claim-select-location-to-return-to": "돌아갈 위치를 선택하세요", "claim-cancel": "취소", "claim-submit-and-close": "제출 및 종료", "create-fulfillment-error": "오류", "create-fulfillment-please-select-a-location-to-fulfill-from": "이행할 위치를 선택하세요.", "create-fulfillment-cant-allow-this-action": "이 작업을 허용할 수 없습니다.", "create-fulfillment-trying-to-fulfill-more-than-in-stock": "재고 그 이상을 충족시키려고 노력 중", "create-fulfillment-successfully-fulfilled-order": "주문이 성공적으로 완료되었습니다.", "create-fulfillment-successfully-fulfilled-swap": "성공적으로 교환 완료", "create-fulfillment-successfully-fulfilled-claim": "청구가 성공적으로 이행되었습니다.", "create-fulfillment-success": "성공", "create-fulfillment-cancel": "취소", "create-fulfillment-create-fulfillment": "이행 만들기", "create-fulfillment-create-fulfillment-title": "이행 생성", "create-fulfillment-locations": "위치", "create-fulfillment-choose-where-you-wish-to-fulfill-from": "이행하려는 곳을 선택하세요.", "create-fulfillment-items-to-fulfill": "이행해야 할 항목", "create-fulfillment-select-the-number-of-items-that-you-wish-to-fulfill": "이행하려는 항목 수를 선택하십시오.", "create-fulfillment-send-notifications": "알림 보내기", "create-fulfillment-when-toggled-notification-emails-will-be-sent": "전환하면 알림 이메일이 전송됩니다.", "create-fulfillment-quantity-is-not-valid": "수량이 유효하지 않습니다.", "detail-cards-allocated": "할당됨", "detail-cards-not-fully-allocated": "완전히 할당되지 않음", "detail-cards-subtotal": "소계", "detail-cards-shipping": "배송", "detail-cards-tax": "세", "detail-cards-total": "총", "detail-cards-edit-order": "주문 수정", "detail-cards-allocate": "할당하다", "detail-cards-discount": "할인:", "detail-cards-original-total": "원래 합계", "details-successfully-updated-the-email-address": "이메일 주소를 성공적으로 업데이트했습니다.", "details-email-address": "이메일 주소", "details-save": "구하다", "details-order-id-copied": "주문 ID가 복사되었습니다.", "details-email-copied": "이메일이 복사되었습니다", "details-cancel-order-heading": "주문 취소", "details-are-you-sure-you-want-to-cancel-the-order": "주문을 취소하시겠습니까?", "order-details-display-id": "주문 번호{{display_id}}", "details-successfully-canceled-order": "주문이 취소되었습니다.", "details-go-to-customer": "고객으로 이동", "details-transfer-ownership": "소유권 이전", "details-edit-shipping-address": "배송 주소 수정", "details-edit-billing-address": "청구서 수신 주소 편집", "details-edit-email-address": "이메일 주소 편집", "details-back-to-orders": "주문으로 돌아가기", "details-cancel-order": "주문 취소", "details-payment": "지불", "details-refunded": "환불됨", "details-total-paid": "전체 지불금", "details-fulfillment": "이행", "details-create-fulfillment": "이행 생성", "details-shipping-method": "배송 방법", "details-customer": "고객", "details-shipping": "배송", "details-billing": "청구", "details-raw-order": "원시 주문", "mark-shipped-successfully-marked-order-as-shipped": "주문을 배송됨으로 표시했습니다.", "mark-shipped-successfully-marked-swap-as-shipped": "스왑을 배송됨으로 표시했습니다.", "mark-shipped-successfully-marked-claim-as-shipped": "청구가 배송됨으로 성공적으로 표시되었습니다.", "mark-shipped-success": "성공", "mark-shipped-error": "오류", "mark-shipped-mark-fulfillment-shipped": "주문 처리를 배송됨으로 표시", "mark-shipped-tracking": "추적", "mark-shipped-tracking-number-label": "추적 번호", "mark-shipped-tracking-number": "추적 번호...", "mark-shipped-add-additional-tracking-number": "+ 추가 추적 번호 추가", "mark-shipped-send-notifications": "알림 보내기", "mark-shipped-cancel": "취소", "mark-shipped-complete": "완벽한", "order-line-warning": "경고", "order-line-cannot-duplicate-an-item-without-a-variant": "변형이 없으면 항목을 복제할 수 없습니다.", "order-line-error": "오류", "order-line-failed-to-duplicate-item": "항목을 복제하지 못했습니다.", "order-line-success": "성공", "order-line-item-removed": "항목이 삭제되었습니다.", "order-line-failed-to-remove-item": "항목을 제거하지 못했습니다.", "order-line-item-added": "항목이 추가됨", "order-line-failed-to-replace-the-item": "항목을 교체하지 못했습니다.", "order-line-replace-product-variants": "제품 변형 교체", "order-line-replace-with-other-item": "다른 아이템으로 교체", "order-line-duplicate-item": "중복된 항목", "order-line-remove-item": "항목 삭제", "order-line-line-item-cannot-be-edited": "이 품목은 처리의 일부이므로 편집할 수 없습니다. ", "order-line-new": "새로운", "order-line-modified": "수정됨", "receive-return-please-select-at-least-one-item-to-receive": "받을 항목을 하나 이상 선택하십시오.", "receive-return-successfully-received-return": "성공적으로 반품 접수됨", "receive-return-received-return-for-order": "주문 번호{{display_id}}에 대한 반품을 받았습니다.", "receive-return-failed-to-receive-return": "반품을 받지 못했습니다.", "receive-return-receive-return": "반품 접수", "receive-return-location": "위치", "receive-return-choose-location": "물품을 반품할 위치를 선택하세요.", "receive-return-select-location-to-return-to": "돌아갈 위치를 선택하세요", "receive-return-no-inventory-levels-exist-for-the-items-at-the-selected-location": "선택한 위치의 품목에 대한 재고 수준이 없습니다.", "receive-return-cancel": "취소", "receive-return-save-and-close": "저장하고 닫습니다", "refund-success": "성공", "refund-successfully-refunded-order": "주문이 성공적으로 환불되었습니다.", "refund-error": "오류", "refund-create-a-refund": "환불 생성", "refund-attention": "주목!", "refund-system-payment-disclaimer": "귀하의 결제 중 하나 이상이 시스템 결제입니다. ", "refund-details": "세부", "refund-cannot-refund-more-than-the-orders-net-total": "주문의 총 총액보다 많은 금액을 환불할 수 없습니다.", "refund-discount": "할인", "refund-reason": "이유", "refund-note": "메모", "refund-discount-for-loyal-customer": "충성 고객을 위한 할인", "refund-send-notifications": "알림 보내기", "refund-cancel": "취소", "refund-complete": "완벽한", "reservation-reservation-was-deleted": "예약이 삭제되었습니다", "reservation-the-allocated-items-have-been-released": "할당된 항목이 해제되었습니다.", "reservation-error": "오류", "reservation-failed-to-delete-the-reservation": "예약을 삭제하지 못했습니다.", "reservation-reservation-was-updated": "예약이 업데이트되었습니다", "reservation-the-reservation-change-was-saved": "예약 변경사항이 저장되었습니다.", "reservation-errors": "오류", "reservation-failed-to-update-reservation": "예약을 업데이트하지 못했습니다.", "reservation-edit-reservation": "예약 수정", "reservation-location": "위치", "reservation-choose-which-location-you-want-to-ship-the-items-from": "물품을 배송할 위치를 선택하세요.", "reservation-items-to-allocate-title": "할당 항목", "reservation-select-the-number-of-items-that-you-wish-to-allocate": "할당하려는 항목 수를 선택합니다.", "reservation-max-reservation-requested": "/ {{maxReservation}} 요청됨", "reservation-reserved": "예약된", "reservation-description": "설명", "reservation-what-type-of-reservation-is-this": "이것은 어떤 종류의 예약인가요?", "reservation-metadata": "메타데이터", "reservation-remove-metadata": "메타데이터 제거", "reservation-add-metadata": "메타데이터 추가", "reservation-delete-reservation": "예약 삭제", "reservation-cancel": "취소", "reservation-save-and-close": "저장하고 닫습니다", "reservation-couldnt-allocate-items": "항목을 할당할 수 없습니다.", "reservation-items-allocated": "할당된 항목", "reservation-items-have-been-allocated-successfully": "항목이 성공적으로 할당되었습니다.", "reservation-save-reservation": "예약 저장", "reservation-loading": "로드 중...", "reservation-allocate-order-items": "주문 항목 할당", "reservation-choose-where-you-wish-to-allocate-from": "할당할 위치를 선택하세요.", "reservation-items-to-allocate": "할당 항목", "returns-success": "성공", "returns-successfully-returned-order": "주문이 성공적으로 반품되었습니다.", "returns-error": "오류", "returns-request-return": "반품요청", "returns-items-to-return": "반품할 품목", "returns-choose-which-location-you-want-to-return-the-items-to": "물품을 반품할 위치를 선택하세요.", "returns-select-location-to-return-to": "돌아갈 위치를 선택하세요", "returns-selected-location-has-no-inventory-levels": "선택한 위치에는 선택한 품목에 대한 재고 수준이 없습니다. ", "returns-shipping": "배송", "returns-choose-retur,-shipping-method": "이번 반품에 사용할 배송 방법을 선택하세요.", "returns-total-refund": "총 환불", "returns-amount": "양", "returns-send-notifications": "알림 보내기", "returns-notify-customer-of-created-return": "생성된 반품을 고객에게 알림", "returns-back": "뒤쪽에", "returns-submit": "제출하다", "rma-sub-modals-search-for-additional": "추가 검색", "rma-sub-modals-general": "일반적인", "rma-sub-modals-first-name": "이름", "rma-sub-modals-last-name": "성", "rma-sub-modals-phone": "핸드폰", "rma-sub-modals-shipping-address": "배송 주소", "rma-sub-modals-address-1": "주소 1", "rma-sub-modals-address-2": "주소 2", "rma-sub-modals-province": "주", "rma-sub-modals-postal-code": "우편 번호", "rma-sub-modals-city": "도시", "rma-sub-modals-country": "국가", "rma-sub-modals-back": "뒤쪽에", "rma-sub-modals-add": "추가하다", "rma-sub-modals-name": "이름", "rma-sub-modals-status": "상태", "rma-sub-modals-in-stock": "재고", "rma-sub-modals-products": "제품", "rma-sub-modals-search-products": "제품 검색..", "rma-sub-modals-reason-for-return": "반품 사유", "rma-sub-modals-reason": "이유", "rma-sub-modals-note": "메모", "swap-success": "성공", "swap-successfully-created-exchange": "교환이 성공적으로 생성되었습니다.", "swap-error": "오류", "swap-register-exchange": "거래소 등록", "swap-items-to-return": "반품할 품목", "swap-shipping": "배송", "swap-shipping-method": "배송 방법", "swap-add-a-shipping-method": "배송 방법 추가", "swap-location": "위치", "swap-choose-which-location-you-want-to-return-the-items-to": "물품을 반품할 위치를 선택하세요.", "swap-select-location-to-return-to": "돌아갈 위치를 선택하세요", "swap-items-to-send": "보낼 품목", "swap-add-product": "제품 추가", "swap-return-total": "반품 합계", "swap-additional-total": "추가 합계", "swap-outbond-shipping": "아웃본드 배송", "swap-calculated-at-checkout": "결제 시 계산됨", "swap-estimated-difference": "추정된 차이", "swap-send-notifications": "알림 보내기", "swap-if-unchecked-the-customer-will-not-receive-communication-about-this-exchange": "선택 취소하면 고객은 이 교환에 대한 통신을 받을 수 없습니다.", "swap-complete": "완벽한", "templates-shipped": "배송됨", "templates-fulfilled": "이행됨", "templates-canceled": "취소 된", "templates-partially-fulfilled": "부분적으로 이행됨", "templates-fulfillment-status-requires-action": "조치가 필요함", "templates-awaiting-fulfillment": "이행 대기 중", "templates-partially-shipped": "부분적으로 배송됨", "templates-cancel-fulfillment-heading": "처리를 취소하시겠습니까?", "templates-are-you-sure-you-want-to-cancel-the-fulfillment": "처리를 취소하시겠습니까?", "templates-successfully-canceled-swap": "교환이 취소되었습니다.", "templates-error": "오류", "templates-successfully-canceled-claim": "청구가 취소되었습니다.", "templates-successfully-canceled-fulfillment": "처리가 취소되었습니다.", "templates-fulfillment-has-been-canceled": "이행이 취소되었습니다.", "templates-fulfilled-by-provider": "{{title}} {{provider}}에 의해 이행됨", "templates-not-shipped": "배송되지 않음", "templates-tracking": "추적", "templates-shipped-from": "배송지", "templates-shipping-from": "배송지", "templates-mark-shipped": "배송됨으로 표시", "templates-cancel-fulfillment": "이행 취소", "templates-completed": "완전한", "templates-processing": "처리", "templates-requires-action": "조치가 필요함", "templates-capture-payment": "결제 캡처", "templates-successfully-captured-payment": "성공적으로 결제를 포착했습니다.", "templates-refund": "환불하다", "templates-total-for-swaps": "스왑 합계", "templates-refunded-for-swaps": "교환 환불됨", "templates-refunded-for-returns": "반품에 대해 환불됨", "templates-manually-refunded": "수동으로 환불됨", "templates-net-total": "순총액", "templates-paid": "유급의", "templates-awaiting-payment": "결제 대기 중", "templates-payment-status-requires-action": "조치가 필요함", "draft-orders-completed": "완전한", "draft-orders-open": "열려 있는", "draft-orders-mark-as-paid": "결제 완료로 표시", "draft-orders-success": "성공", "draft-orders-successfully-mark-as-paid": "결제 완료로 표시됨", "draft-orders-error": "오류", "draft-orders-successfully-canceled-order": "주문이 취소되었습니다.", "draft-orders-back-to-draft-orders": "초안 주문으로 돌아가기", "on-mark-as-paid-confirm-order-id": "주문 번호{{display_id}}", "draft-orders-go-to-order": "주문으로 이동", "draft-orders-cancel-draft-order": "초안 주문 취소", "draft-orders-draft-order": "초안 주문", "draft-orders-email": "이메일", "draft-orders-phone": "핸드폰", "draft-orders-amount": "금액 {{currency_code}}", "draft-orders-payment": "지불", "draft-orders-subtotal": "소계", "draft-orders-shipping": "배송", "draft-orders-tax": "세", "draft-orders-total-to-pay": "지불할 총액", "draft-orders-payment-link": "결제 링크:", "draft-orders-configure-payment-link-in-store-settings": "매장 설정에서 결제 링크 구성", "draft-orders-shipping-method": "배송 방법", "draft-orders-data": "데이터", "draft-orders-1-item": "(1개 항목)", "draft-orders-customer": "고객", "draft-orders-edit-shipping-address": "배송 주소 수정", "draft-orders-edit-billing-address": "청구서 수신 주소 편집", "draft-orders-go-to-customer": "고객으로 이동", "draft-orders-contact": "연락하다", "draft-orders-billing": "청구", "draft-orders-raw-draft-order": "원시 초안 주문", "draft-orders-are-you-sure": "확실합니까?", "draft-orders-remove-resource-heading": "{{resource}} 제거", "draft-orders-remove-resource-success-text": "{{resource}}이(가) 제거되었습니다", "draft-orders-this-will-create-an-order-mark-this-as-paid-if-you-received-the-payment": "이렇게 하면 주문이 생성됩니다. ", "draft-orders-mark-paid": "결제 완료로 표시", "draft-orders-cancel": "취소", "draft-orders-create-draft-order": "발주 주문 생성", "edit-amount-paid": "금액 지급", "edit-new-total": "새로운 합계", "edit-difference-due": "차액 지불", "edit-back": "뒤쪽에", "edit-save-and-go-back": "저장하고 돌아가기", "edit-order-edit-set-as-requested": "요청한 대로 편집 세트 주문", "edit-failed-to-request-confirmation": "확인을 요청하지 못했습니다.", "edit-added-successfully": "성공적으로 추가되었습니다", "edit-error-occurred": "오류가 발생했습니다", "edit-add-product-variants": "제품 변형 추가", "edit-edit-order": "주문 수정", "edit-items": "품목", "edit-add-items": "항목 추가", "edit-filter-items": "항목 필터링...", "edit-note": "메모", "edit-add-a-note": "메모 추가...", "variants-table-location": "{{위치}}에서", "edit-product": "제품", "edit-in-stock": "재고", "edit-price": "가격", "edit-products": "제품", "edit-search-product-variants": "제품 변형 검색...", "orders-success": "성공", "orders-successfully-initiated-export": "내보내기가 성공적으로 시작되었습니다.", "orders-error": "오류", "orders-export-orders": "수출 주문", "components-billing-address": "청구 지 주소", "components-use-same-as-shipping": "배송과 동일하게 사용", "components-e-g-gift-wrapping": "예: ", "components-title": "제목", "components-price": "가격", "components-quantity": "수량", "components-back": "뒤쪽에", "components-add": "추가하다", "components-items-for-the-order": "주문 품목", "components-details": "세부", "components-price-excl-taxes": "가격(세금 별도)", "components-add-custom": "맞춤 추가", "components-add-existing": "기존 추가", "components-add-products": "제품 추가", "components-add-custom-item": "맞춤 항목 추가", "components-choose-region": "지역 선택", "components-region": "지역", "select-shipping-to-name": "({{이름}}에게)", "components-attention": "주목!", "components-no-options-for-orders-without-shipping": "배송 없이 주문하는 경우 옵션이 없습니다. ", "components-choose-a-shipping-method": "배송 방법을 선택하세요", "components-set-custom-price": "맞춤 가격 설정", "components-custom-price": "맞춤 가격", "components-customer-and-shipping-details": "고객 및 배송 세부정보", "components-find-existing-customer": "기존 고객 찾기", "components-email": "이메일", "components-choose-existing-addresses": "기존 주소 선택", "components-create-new": "새로 만들기", "components-the-discount-is-not-applicable-to-the-selected-region": "선택한 지역에는 할인이 적용되지 않습니다.", "components-the-discount-code-is-invalid": "할인 코드가 유효하지 않습니다", "components-add-discount": "할인 추가", "components-summer-10": "여름10", "components-discount": "할인", "select-shipping-code": "(코드: {{코드}})", "components-type": "유형", "components-value": "값", "components-address": "주소", "components-shipping-method": "배송 방법", "components-billing-details": "결제 세부 정보", "components-edit": "편집하다", "form-use-new-order-form-must-be-used-within-new-order-form-provider": "useNewOrderForm은 NewOrderFormProvider 내에서 사용해야 합니다.", "new-order-created": "주문이 생성되었습니다.", "new-create-draft-order": "초안 주문 생성", "price-list-product-filter-created-at": "생성 날짜", "price-list-product-filter-updated-at": "업데이트 날짜", "price-list-details-drawer-prompt-title": "확실합니까?", "price-list-details-drawer-prompt-description": "저장되지 않은 변경사항이 있습니다. 종료하시겠습니까?", "price-list-details-notification-succes-title": "가격표가 업데이트되었습니다.", "price-list-details-drawer-notification-success-message": "가격표가 업데이트되었습니다.", "price-list-details-drawer-notification-error-title": "에러 발생됨", "price-list-details-drawer-title": "가격표 세부정보 편집", "price-list-details-drawer-cancel-button": "취소", "price-list-details-drawer-save-button": "구하다", "price-list-details-section-prompt-confirm-text": "삭제", "price-list-details-section-prompt-cancel-text": "취소", "price-list-details-section-prompt-title": "가격표 삭제", "price-list-details-section-prompt-description": "{{name}}", "price-list-details-section-delete-notification-success-title": "가격표를 삭제했습니다.", "price-list-details-section-delete-notification-success-message": "{{name}}", "price-list-details-section-delete-notification-error-title": "가격표를 삭제하지 못했습니다.", "price-list-details-section-customer-groups": "고객 그룹", "price-list-details-section-last-edited": "마지막으로 수정됨", "price-list-details-section-number-of-prices": "물가", "price-list-details-section-status-menu-expired": "만료됨", "price-list-details-section-status-menu-draft": "초안", "price-list-details-section-status-menu-scheduled": "예정됨", "price-list-details-section-status-active": "활동적인", "price-list-details-section-status-menu-notification-success-title": "가격표 상태가 업데이트되었습니다.", "price-list-details-section-status-menu-notification-success-message": "가격표 상태가 {{status}}(으)로 업데이트되었습니다.", "price-list-details-section-status-menu-notification-error-title": "가격표 상태를 업데이트하지 못했습니다.", "price-list-details-section-status-menu-item-draft": "초안", "price-list-details-section-status-menu-item-activate": "활성화", "price-list-details-menu-item-edit": "세부정보 수정", "price-list-details-menu-item-delete": "삭제", "price-list-edit-error": "가격표를 로드하는 중에 오류가 발생했습니다. ", "price-list-new-form-prompt-title": "확실합니까?", "price-list-new-form-prompt-exit-description": "저장되지 않은 변경사항이 있습니다. 종료하시겠습니까?", "price-list-new-form-prompt-back-description": "저장되지 않은 변경사항이 있습니다. 돌아가시겠습니까?", "price-list-add-products-modal-no-prices-error": "하나 이상의 제품에 가격을 지정하세요.", "price-list-add-products-modal-missing-prices-title": "불완전한 가격표", "price-list-add-products-modal-missing-prices-description": "선택한 모든 제품에 가격이 할당되지 않았습니다. ", "price-list-add-products-modal-success-title": "새로운 가격이 추가되었습니다.", "price-list-add-products-modal-success-message": "가격표에 새로운 가격이 추가되었습니다.", "price-list-add-products-modal-error-title": "에러 발생됨", "price-list-add-products-modal-back-button-cancel": "취소", "price-list-add-products-modal-back-button": "뒤쪽에", "price-list-add-products-modal-next-button-continue": "계속하다", "price-list-add-products-modal-next-button-submit-and-close": "제출 및 종료", "price-list-add-products-modal-next-button-continue-save-prices": "가격 저장", "price-list-add-products-modal-products-tab": "제품 선택", "price-list-add-products-modal-prices-tab": "가격 편집", "price-list-add-products-modal-error": "양식을 준비하는 동안 오류가 발생했습니다. ", "price-list-edit-prices-modal-prompt-title": "저장되지 않은 변경사항", "price-list-edit-prices-modal-prompt-exit-description": "저장되지 않은 변경사항이 있습니다. 종료하시겠습니까?", "price-list-edit-prices-modal-prompt-back-description": "저장되지 않은 변경사항이 있습니다. 돌아가시겠습니까?", "price-list-edit-prices-modal-notification-update-error": "에러 발생됨", "price-list-edit-prices-modal-notification-remove-error-title": "에러 발생됨", "price-list-edit-prices-modal-notification-remove-error-description": "일부 가격이 올바르게 업데이트되지 않았습니다. ", "price-list-edit-prices-modal-notification-update-success-title": "가격이 업데이트되었습니다.", "price-list-edit-prices-modal-notification-update-success-description": "가격이 업데이트되었습니다.", "price-list-edit-prices-modal-next-button-save-and-close": "저장하고 닫습니다", "price-list-edit-prices-modal-next-button-save": "가격 저장", "price-list-edit-prices-modal-back-button-cancel": "취소", "price-list-edit-prices-modal-back-button-back": "뒤쪽에", "price-list-edit-prices-modal-overview-tab": "가격 편집", "price-list-edit-prices-modal-error-loading": "양식을 준비하는 동안 오류가 발생했습니다. ", "price-list-prices-section-prompt-title": "확실합니까?", "price-list-prices-section-prompt-description": "목록에서 제품 가격이 영구적으로 삭제됩니다.", "price-list-prices-secton-delete-success-title": "가격이 삭제되었습니다.", "price-list-prices-section-delete-success-description_one": "{{count}}개 제품의 가격을 성공적으로 삭제했습니다.", "price-list-prices-section-delete-success-description_other": "{{count}}개 제품의 가격을 성공적으로 삭제했습니다.", "price-list-prices-section-delete-error-title": "에러 발생됨", "price-list-prices-section-heading": "물가", "price-list-prices-section-search-placeholder": "제품 검색", "price-list-prices-section-prices-menu-edit": "가격 수정", "price-list-prices-section-prices-menu-add": "제품 추가", "price-list-prices-section-table-load-error": "제품을 가져오는 중에 오류가 발생했습니다. ", "price-list-prices-section-bar-count_one": "{{count}}개 선택됨", "price-list-prices-section-bar-count_other": "{{count}}개 선택됨", "price-list-prices-section-edit-command": "편집하다", "price-list-prices-section-delete-command": "삭제", "price-list-prices-section-select-all-checkbox-label": "현재 페이지의 모든 제품을 선택하세요.", "price-list-prices-section-select-checkbox-label": "행 선택", "price-list-prices-section-table-product": "제품", "price-list-prices-section-table-thumbnail-alt": "{{제목}} 미리보기 이미지", "price-list-prices-section-table-collection": "수집", "price-list-prices-section-table-variants": "변형", "price-list-details-form-type-heading": "유형", "price-list-details-form-type-description": "생성하려는 가격표 유형을 선택합니다.", "price-list-details-form-type-label-sale": "판매", "price-list-details-form-type-hint-sale": "판매를 생성하는 경우 이 방법을 사용하세요.", "price-list-details-form-type-label-override": "우세하다", "price-list-details-form-type-hint-override": "가격을 재정의하는 경우 이 방법을 사용하세요.", "price-list-details-form-general-heading": "일반적인", "price-list-details-form-general-description": "가격표의 제목과 설명을 선택합니다.", "price-list-details-form-general-name-label": "이름", "price-list-details-form-general-name-placeholder": "블랙 프라이데이 세일", "price-list-details-form-general-description-label": "설명", "price-list-details-form-general-description-placeholder": "블랙프라이데이 세일 가격은..", "price-list-details-form-tax-inclusive-label": "세금 포함 가격", "price-list-details-form-tax-inclusive-hint": "이 목록의 모든 가격에 세금이 포함되도록 선택하세요.", "price-list-details-form-dates-starts-at-heading": "가격표에는 시작일이 있나요?", "price-list-details-form-dates-starts-at-description": "나중에 활성화되도록 가격 재정의를 예약하세요.", "price-list-details-form-dates-starts-at-label": "시작일", "price-list-details-form-ends-at-heading": "가격표에 유효기간이 있나요?", "price-list-details-form-ends-at-description": "나중에 비활성화되도록 가격 재정의를 예약하세요.", "price-list-details-form-ends-at-label": "만료일", "price-list-details-form-customer-groups-name": "이름", "price-list-details-form-customer-groups-members": "회원", "price-list-details-form-customer-groups-error": "고객 그룹을 로드하는 중 오류가 발생했습니다. ", "price-list-details-form-customer-groups-no-groups": "고객 그룹을 찾을 수 없습니다.", "price-list-details-form-customer-groups-heading": "고객 가용성", "price-list-details-form-customer-groups-description": "가격 재정의를 적용해야 하는 고객 그룹을 지정합니다.", "price-list-details-form-customer-groups-content-heading": "고객 그룹", "price-list-details-form-customer-groups-search-placeholder": "찾다", "price-list-prices-form-products-error": "양식을 준비하는 동안 오류가 발생했습니다. ", "price-list-prices-form-heading": "가격 수정", "price-list-prices-form-variant": "변종", "price-list-prices-form-sku": "SKU", "price-list-prices-form-prices": "물가", "price-list-prices-form-prices-variant-count_one": "{{count}}개 변형", "price-list-prices-form-prices-variant-count_other": "{{count}}개 변형", "price-list-prices-form-add-prices-button": "가격 추가", "price-list-prices-form-prices-count_one": "{{count}} 가격", "price-list-prices-form-prices-count_other": "{{count}} 가격", "price-list-product-prices-form-invalid-data-title": "유효하지 않은 데이터", "price-list-product-prices-form-invalid-data-body": "붙여넣은 데이터에 숫자가 아닌 값이 포함되어 있습니다.", "price-list-product-prices-form-column-visibility-button": "보다", "price-list-product-prices-form-column-visibility-currencies-label": "통화", "price-list-product-prices-form-column-visibility-regions-label": "지역", "price-list-product-prices-form-column-product-label": "제품", "price-list-product-prices-form-column-currencies-price-label": "가격 {{코드}}", "price-list-product-prices-form-column-regions-price-label": "가격 {{이름}}({{코드}})", "price-list-products-form-select-all": "현재 페이지의 모든 제품을 선택하세요.", "price-list-products-form-select-row": "행 선택", "price-list-products-form-product-label": "제품", "price-list-products-form-product-thumbnail": "{{제목}} 미리보기 이미지", "price-list-products-form-collection-label": "수집", "price-list-products-form-sales-channels-label": "유효성", "price-list-products-form-sales-channels-value": "{{첫 번째}} + {{나머지}}개 더보기", "price-list-products-form-status-label": "상태", "price-list-products-form-inventory-label": "목록", "price-list-products-form-inventory-value": "{{variants}}개 변형에 걸쳐 {{totalStock}}개의 재고가 있습니다.", "price-list-products-form-loading": "제품 로딩", "price-list-products-form-error": "제품을 로드하는 중 오류가 발생했습니다. ", "price-list-products-form-no-products": "검색된 상품이 없습니다.", "price-list-products-form-heading": "제품을 선택하세요", "price-list-products-form-search-placeholder": "찾다", "price-list-new-form-no-prices-error": "하나 이상의 제품에 대한 가격을 설정하세요.", "price-list-new-form-missing-prices-title": "불완전한 가격표", "price-list-new-products-modal-missing-prices-description": "선택한 모든 제품에 가격이 할당되지 않았습니다. ", "price-list-new-form-notification-success-title": "가격표가 생성되었습니다.", "price-list-new-form-notification-success-message": "가격표가 성공적으로 생성되었습니다.", "price-list-new-form-notification-error-title": "에러 발생됨", "price-list-new-form-next-button-save-and-publish": "저장 및 게시", "price-list-new-form-next-button-save": "가격 저장", "price-list-new-form-next-button-continue": "계속하다", "price-list-new-form-back-button-cancel": "취소", "price-list-new-form-back-button-back": "뒤쪽에", "price-list-new-form-details-tab": "가격표 생성", "price-list-new-form-products-tab": "제품 선택", "price-list-new-form-prices-tab": "가격 편집", "price-list-new-form-save-as-draft": "임시 보관함에 저장", "price-list-new-form-error-loading-products": "양식을 준비하는 동안 오류가 발생했습니다. ", "components-success": "성공", "components-successfully-updated-category-tree": "카테고리 트리가 업데이트되었습니다.", "components-error": "오류", "components-failed-to-update-category-tree": "카테고리 트리를 업데이트하지 못했습니다.", "components-delete": "삭제", "components-category-deleted": "카테고리가 삭제되었습니다.", "components-category-deletion-failed": "카테고리 삭제 실패", "components-category-status-is-inactive": "카테고리 상태가 비활성 상태입니다.", "components-category-visibility-is-private": "카테고리 공개는 비공개입니다", "components-add-category-item-to": "카테고리 항목 추가", "modals-public": "공공의", "modals-private": "사적인", "modals-active": "활동적인", "modals-inactive": "비활성", "modals-success": "성공", "modals-successfully-created-a-category": "카테고리가 생성되었습니다.", "modals-failed-to-create-a-new-category": "새 카테고리를 생성하지 못했습니다.", "modals-error": "오류", "modals-save-category": "카테고리 저장", "modals-add-category-to": "{{name}}에 카테고리 추가", "modals-add-category": "카테고리 추가", "modals-details": "세부", "modals-name": "이름", "modals-give-this-category-a-name": "이 카테고리에 이름을 지정하세요", "modals-handle": "핸들", "modals-custom-handle": "맞춤형 핸들", "modals-description": "설명", "modals-give-this-category-a-description": "이 카테고리에 대한 설명을 입력하세요", "modals-status": "상태", "modals-visibility": "시계", "modals-successfully-updated-the-category": "카테고리를 업데이트했습니다.", "modals-failed-to-update-the-category": "카테고리를 업데이트하지 못했습니다.", "modals-edit-product-category": "제품 카테고리 수정", "modals-cancel": "취소", "modals-save-and-close": "저장하고 닫습니다", "pages-no-product-categories-yet": "아직 제품 카테고리가 없습니다. 위 버튼을 사용하여 첫 번째 카테고리를 만드세요.", "pages-add-category": "카테고리 추가", "pages-product-categories": "제품 카테고리", "pages-helps-you-to-keep-your-products-organized": "제품을 체계적으로 정리하는 데 도움이 됩니다.", "batch-job-success": "성공", "batch-job-import-confirmed-for-processing-progress-info-is-available-in-the-activity-drawer": "처리를 위해 가져오기가 확인되었습니다. ", "batch-job-error": "오류", "batch-job-import-failed": "가져오기에 실패했습니다.", "batch-job-failed-to-delete-the-csv-file": "CSV 파일을 삭제하지 못했습니다.", "batch-job-failed-to-cancel-the-batch-job": "일괄 작업을 취소하지 못했습니다.", "batch-job-products-list": "제품 목록", "batch-job-unsure-about-how-to-arrange-your-list": "목록을 어떻게 정리해야 할지 모르시나요?", "batch-job-download-template": "아래 템플릿을 다운로드하여 올바른 형식을 따르고 있는지 확인하세요.", "batch-job-imports-description": "가져오기를 통해 제품을 추가하거나 업데이트할 수 있습니다. ", "products-filters": "필터", "products-status": "상태", "products-tags": "태그", "products-spring-summer": "봄 여름...", "new-sales-channels": "판매채널", "new-this-product-will-only-be-available-in-the-default-sales-channel-if-left-untouched": "이 제품은 그대로 두면 기본 판매 채널에서만 구입할 수 있습니다.", "new-change-availablity": "가용성 변경", "add-variants-a-variant-with-these-options-already-exists": "이러한 옵션이 포함된 변형이 이미 존재합니다.", "add-variants-product-options": "제품 옵션", "add-variants-options-are-used-to-define-the-color-size-etc-of-the-product": "옵션은 제품의 색상, 크기 등을 정의하는 데 사용됩니다.", "add-variants-option-title": "옵션 제목", "add-variants-variations-comma-separated": "변형(쉼표로 구분)", "add-variants-color": "색상...", "add-variants-already-exists": "이미 존재 함", "add-variants-blue-red-black": "블루, 레드, 블랙...", "add-variants-add-an-option": "옵션 추가", "add-variants-product-variants": "제품 변형", "add-variants-you-must-add-at-least-one-product-option-before-you-can-begin-adding-product-variants": "제품 이형 상품 추가를 시작하려면 먼저 제품 옵션을 하나 이상 추가해야 합니다.", "add-variants-variant": "변종", "add-variants-inventory": "목록", "add-variants-add-a-variant": "변형 추가", "add-variants-create-variant": "변형 생성", "add-variants-cancel": "취소", "add-variants-save-and-close": "저장하고 닫습니다", "new-variant-a-variant-with-these-options-already-exists": "이러한 옵션이 포함된 변형이 이미 존재합니다.", "new-variant-are-you-sure-you-want-to-delete-this-variant": "이 변형을 삭제하시겠습니까?", "new-variant-delete-variant": "변형 삭제", "new-variant-edit": "편집하다", "new-variant-delete": "삭제", "new-variant-edit-variant": "변형 편집", "new-variant-cancel": "취소", "new-variant-save-and-close": "저장하고 닫습니다", "new-something-went-wrong-while-trying-to-upload-images": "이미지를 업로드하는 중에 문제가 발생했습니다.", "new-no-file-service-configured": "파일 서비스가 구성되어 있지 않을 수 있습니다. ", "new-upload-thumbnail-error": "미리보기 이미지를 업로드하는 중에 문제가 발생했습니다.", "new-save-as-draft": "임시 보관함에 저장", "new-publish-product": "제품 게시", "new-general-information-title": "일반 정보", "new-to-start-selling-all-you-need-is-a-name-and-a-price": "판매를 시작하려면 이름과 가격만 있으면 됩니다.", "new-organize-product": "제품 정리", "new-add-variations-of-this-product": "이 제품의 변형을 추가하세요.", "new-offer-your-customers-different-options-for-color-format-size-shape-etc": "고객에게 색상, 형식, 크기, 모양 등에 대한 다양한 옵션을 제공하십시오.", "new-used-for-shipping-and-customs-purposes": "배송 및 통관 목적으로 사용됩니다.", "new-dimensions": "치수", "new-customs": "세관", "new-used-to-represent-your-product-during-checkout-social-sharing-and-more": "결제, 소셜 공유 등의 과정에서 제품을 나타내는 데 사용됩니다.", "new-media": "미디어", "new-add-images-to-your-product": "제품에 이미지를 추가하세요.", "overview-import-products": "수입제품", "overview-export-products": "수출제품", "overview-new-product": "새로운 제품", "overview-new-collection": "새 컬렉션", "overview-success": "성공", "overview-successfully-created-collection": "컬렉션이 생성되었습니다.", "overview-error": "오류", "overview-successfully-initiated-export": "내보내기가 성공적으로 시작되었습니다.", "modals-add-sales-channels": "판매 채널 추가", "modals-find-channels": "채널 찾기", "modals-updated-the-api-key": "API 키를 업데이트했습니다.", "modals-failed-to-update-the-api-key": "API 키를 업데이트하지 못했습니다.", "modals-edit-api-key-details": "API 키 세부정보 수정", "modals-title": "제목", "modals-name-your-key": "키 이름 지정", "modals-sales-channels-added-to-the-scope": "범위에 추가된 판매 채널", "modals-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "키 범위에 판매 채널을 추가하는 중 오류가 발생했습니다.", "modals-add-and-go-back": "추가하고 돌아가기", "modals-add-and-close": "추가 및 닫기", "modals-sales-channels-removed-from-the-scope": "범위에서 제거된 판매 채널", "modals-error-occurred-while-removing-sales-channels-from-the-scope-of-the-key": "키 범위에서 판매 채널을 제거하는 중 오류가 발생했습니다.", "modals-edit-sales-channels": "판매 채널 편집", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_one": "{{세다}}", "publishable-api-keys-modals-manage-sales-channels-selected-with-counts_other": "{{세다}}", "modals-deselect": "선택 취소", "modals-remove": "제거하다", "modals-add-channels": "채널 추가", "modals-close": "닫다", "pages-sales-channels": "판매채널", "pages-connect-as-many-sales-channels-to-your-api-key-as-you-need": "필요한 만큼 많은 판매 채널을 API 키에 연결하세요.", "pages-add-sales-channels": "판매 채널 추가", "pages-edit-sales-channels": "판매 채널 편집", "pages-success": "성공", "pages-created-a-new-api-key": "새 API 키를 만들었습니다.", "pages-error": "오류", "pages-failed-to-create-a-new-api-key": "새 API 키를 생성하지 못했습니다.", "pages-sales-channels-added-to-the-scope": "범위에 추가된 판매 채널", "pages-error-occurred-while-adding-sales-channels-to-the-scope-of-the-key": "키 범위에 판매 채널을 추가하는 중 오류가 발생했습니다.", "pages-publish-api-key": "API 키 게시", "pages-create-api-key": "API 키 생성", "pages-create-and-manage-api-keys-right-now-this-is-only-related-to-sales-channels": "API 키를 생성하고 관리합니다. ", "pages-create-api-key-label": "API 키 생성", "pages-back-to-settings": "설정으로 돌아가기", "pages-publishable-api-keys": "게시 가능한 API 키", "pages-these-publishable-keys-will-allow-you-to-authenticate-api-requests": "이러한 게시 가능한 키를 사용하면 API 요청을 인증할 수 있습니다.", "tables-name": "이름", "tables-token": "토큰", "tables-done": "완료", "tables-copy-to-clipboard": "클립 보드에 복사", "tables-created": "만들어진", "tables-status": "상태", "tables-revoked": "취소됨", "tables-live": "살다", "tables-edit-api-key-details": "API 키 세부정보 수정", "tables-edit-sales-channels": "판매 채널 편집", "tables-copy-token": "토큰 복사", "tables-revoke-token": "토큰 취소", "tables-delete-api-key": "API 키 삭제", "tables-yes-delete": "예, 삭제합니다", "tables-api-key-deleted": "API 키가 삭제되었습니다.", "tables-are-you-sure-you-want-to-delete-this-public-key": "이 공개 키를 삭제하시겠습니까?", "tables-delete-key": "키 삭제", "tables-yes-revoke": "응, 취소해", "tables-api-key-revoked": "API 키가 취소되었습니다.", "tables-are-you-sure-you-want-to-revoke-this-public-key": "이 공개 키를 취소하시겠습니까?", "tables-revoke-key": "키 취소", "tables-api-keys": "API 키", "tables-no-keys-yet-use-the-above-button-to-create-your-first-publishable-key": "아직 키가 없습니다. 위 버튼을 사용하여 첫 번째 게시 가능한 키를 만드세요.", "tables-title": "제목", "tables-description": "설명", "tables-no-added-sales-channels": "추가된 판매 채널 없음", "tables-sales-channels": "판매채널", "form-title": "제목", "form-website-app-amazon-physical-store-pos-facebook-product-feed": "웹사이트, 앱, Amazon, 오프라인 매장 POS, Facebook 제품 피드...", "form-description": "설명", "form-available-products-at-our-website-app": "홈페이지, 앱에서 구매 가능한 제품...", "form-success": "성공", "form-the-sales-channel-is-successfully-updated": "판매 채널이 성공적으로 업데이트되었습니다.", "form-error": "오류", "form-failed-to-update-the-sales-channel": "판매 채널을 업데이트하지 못했습니다.", "form-sales-channel-details": "판매채널 세부정보", "form-general-info": "일반 정보", "form-name": "이름", "form-close": "닫다", "form-save": "구하다", "pages-draft": "초안", "pages-control-which-products-are-available-in-which-channels": "어떤 채널에서 어떤 제품을 사용할 수 있는지 제어", "pages-search-by-title-or-description": "제목이나 설명으로 검색", "pages-confirm-delete-sales-channel": "이 판매 채널을 삭제하시겠습니까? ", "pages-delete-channel-heading": "채널 삭제", "pages-edit-general-info": "일반 정보 수정", "pages-add-products": "제품 추가", "pages-delete-channel": "채널 삭제", "pages-disabled": "장애가 있는", "pages-enabled": "활성화됨", "tables-collection": "수집", "tables-start-building-your-channels-setup": "채널 설정 구축을 시작하세요...", "tables-no-products-in-channels": "아직 이 채널에 제품을 추가하지 않았습니다. 추가하면 해당 제품이 여기에 게시됩니다.", "tables-add-products": "제품 추가", "tables-details": "세부", "tables-remove-from-the-channel": "채널에서 삭제", "tables-products": "제품", "sales-channels-table-placeholder-selected-with-counts_one": "{{세다}}", "sales-channels-table-placeholder-selected-with-counts_other": "{{세다}}", "tables-remove": "제거하다", "components-successfully-updated-currency": "통화가 업데이트되었습니다.", "components-default": "기본", "default-store-currency-success": "성공", "default-store-currency-successfully-updated-default-currency": "기본 통화가 성공적으로 업데이트되었습니다.", "default-store-currency-error": "오류", "default-store-currency-default-store-currency": "기본 매장 통화", "default-store-currency-this-is-the-currency-your-prices-are-shown-in": "가격이 표시되는 통화입니다.", "store-currencies-success": "성공", "store-currencies-successfully-updated-currencies": "통화가 성공적으로 업데이트되었습니다.", "store-currencies-error": "오류", "store-currencies-cancel": "취소", "store-currencies-save-and-go-back": "저장하고 돌아가기", "store-currencies-save-and-close": "저장하고 닫습니다", "store-currencies-add-store-currencies": "상점 통화 추가", "store-currencies-current-store-currencies": "현재 상점 통화", "store-currencies-close": "닫다", "current-currencies-screen-selected-with-count_one": "{{세다}}", "current-currencies-screen-selected-with-count_other": "{{세다}}", "store-currencies-deselect": "선택 취소", "store-currencies-remove": "제거하다", "store-currencies-add-currencies": "통화 추가", "store-currencies-store-currencies": "매장 통화", "store-currencies-all-the-currencies-available-in-your-store": "귀하의 매장에서 사용 가능한 모든 통화.", "store-currencies-edit-currencies": "통화 수정", "currencies-an-unknown-error-occurred": "알 수없는 오류가 발생했습니다", "currencies-error": "오류", "currencies-back-to-settings": "설정으로 돌아가기", "currencies-manage-the-markets-that-you-will-operate-within": "귀하가 운영할 시장을 관리하십시오.", "currencies-include-or-exclude-taxes": "이 통화로 가격을 정의할 때마다 세금을 포함할지 제외할지 결정하세요.", "currencies-tax-incl-prices": "세금 포함. ", "settings-error": "오류", "settings-malformed-swap-url": "잘못된 스왑 URL", "settings-malformed-payment-url": "잘못된 결제 URL", "settings-malformed-invite-url": "잘못된 초대 URL", "settings-success": "성공", "settings-successfully-updated-store": "매장이 업데이트되었습니다.", "settings-back-to-settings": "설정으로 돌아가기", "settings-save": "구하다", "settings-cancel": "취소", "settings-store-details": "매장 세부정보", "settings-manage-your-business-details": "비즈니스 세부정보 관리", "settings-general": "일반적인", "settings-store-name": "가게 이름", "settings-medusa-store": "메두사 스토어", "settings-advanced-settings": "고급 설정", "settings-swap-link-template": "링크 템플릿 교체", "settings-draft-order-link-template": "초안 주문 링크 템플릿", "settings-invite-link-template": "링크 템플릿 초대", "settings-manage-the-general-settings-for-your-store": "매장의 일반 설정을 관리하세요.", "settings-manage-the-settings-for-your-store-apos-s-extensions": "추가 매장 정보 설정 관리", "edit-user-information-success": "성공", "edit-user-information-your-information-was-successfully-updated": "귀하의 정보가 성공적으로 업데이트되었습니다", "edit-user-information-edit-information": "정보 편집", "edit-user-information-cancel": "취소", "edit-user-information-submit-and-close": "제출 및 종료", "personal-information-back-to-settings": "설정으로 돌아가기", "personal-information-personal-information": "개인 정보", "personal-information-manage-your-medusa-profile": "메두사 프로필 관리", "personal-information-language-settings-title": "언어", "personal-information-language-settings-description": "Medusa Admin의 언어 조정", "personal-information-language-settings-help-us-translate": "번역을 도와주세요", "personal-information-usage-insights-title": "사용량 통계", "usage-insights-disabled": "장애가 있는", "usage-insights-active": "활동적인", "usage-insights-share-usage-insights-and-help-us-improve-medusa": "사용 정보를 공유하고 Medusa 개선에 도움을 주세요.", "usage-insights-edit-preferences": "환경설정 수정", "usage-insights-success": "성공", "usage-insights-your-information-was-successfully-updated": "귀하의 정보가 성공적으로 업데이트되었습니다", "usage-insights-error": "오류", "usage-insights-cancel": "취소", "usage-insights-submit-and-close": "제출 및 종료", "region-form-title": "제목", "region-form-europe": "유럽", "region-form-currency-code-is-required": "통화 코드가 필요합니다", "region-form-currency": "통화", "region-form-choose-currency": "통화 선택", "region-form-default-tax-rate": "기본 세율", "region-form-tax-rate-is-required": "세율은 필수 항목입니다.", "region-form-tax-rate-must-be-equal-to-or-less-than-100": "세율은 100 이하여야 합니다.", "region-form-default-tax-code": "기본 세금 코드", "region-form-countries": "국가", "region-form-choose-countries": "국가 선택", "region-form-tax-inclusive-prices": "세금 포함 가격", "region-form-when-enabled-region-prices-will-be-tax-inclusive": "활성화되면 지역 가격에 세금이 포함됩니다.", "region-form-payment-providers-are-required": "결제 서비스 제공업체가 필요합니다.", "region-form-payment-providers": "결제 제공업체", "region-form-choose-payment-providers": "결제 제공업체를 선택하세요...", "region-form-fulfillment-providers-are-required": "주문 처리 제공업체가 필요합니다.", "region-form-fulfillment-providers": "이행 제공업체", "region-form-choose-fulfillment-providers": "주문 처리 제공업체 선택...", "shipping-option-card-success": "성공", "shipping-option-card-shipping-option-updated": "배송 옵션이 업데이트되었습니다.", "shipping-option-card-error": "오류", "shipping-option-card-edit-shipping-option": "배송 옵션 수정", "shipping-option-card-fulfillment-method": "이행 방법", "shipping-option-card-cancel": "취소", "shipping-option-card-save-and-close": "저장하고 닫습니다", "shipping-option-card-shipping-option-has-been-deleted": "배송 옵션이 삭제되었습니다.", "shipping-option-card-flat-rate": "고정 요금", "shipping-option-card-calcualted": "계획된", "shipping-option-card-min-subtotal": "최소 ", "shipping-option-card-max-subtotal": "최대. ", "shipping-option-card-admin": "관리자", "shipping-option-card-store": "가게", "shipping-option-card-edit": "편집하다", "shipping-option-card-delete": "삭제", "shipping-option-form-visible-in-store": "매장에서 볼 수 있음", "shipping-option-form-enable-or-disable-the-shipping-option-visiblity-in-store": "매장에서 배송 옵션 표시 여부를 활성화하거나 비활성화합니다.", "shipping-option-form-details": "세부", "shipping-option-form-title": "제목", "shipping-option-form-title-is-required": "제목은 필수 항목입니다.", "shipping-option-form-price-type": "가격 유형", "shipping-option-form-flat-rate": "고정 요금", "shipping-option-form-calculated": "계획된", "shipping-option-form-choose-a-price-type": "가격 유형을 선택하세요", "shipping-option-form-price": "가격", "shipping-option-form-shipping-profile": "배송 프로필", "shipping-option-form-choose-a-shipping-profile": "배송 프로필을 선택하세요", "shipping-option-form-fulfillment-method": "이행 방법", "shipping-option-form-choose-a-fulfillment-method": "이행 방법을 선택하세요", "shipping-option-form-requirements": "요구사항", "shipping-option-form-min-subtotal-must-be-less-than-max-subtotal": "최소 ", "shipping-option-form-min-subtotal": "최소 ", "shipping-option-form-max-subtotal": "최대. ", "shipping-option-form-metadata": "메타데이터", "general-section-success": "성공", "general-section-region-was-successfully-updated": "지역이 업데이트되었습니다.", "general-section-error": "오류", "general-section-edit-region-details": "지역 세부정보 편집", "general-section-details": "세부", "general-section-providers": "공급자", "general-section-metadata": "메타데이터", "general-section-cancel": "취소", "general-section-save-and-close": "저장하고 닫습니다", "edit-something-went-wrong": "문제가 발생했습니다.", "edit-no-region-found": "해당 ID를 가진 지역을 찾을 수 없습니다. 왼쪽 메뉴를 사용하여 지역을 선택하세요.", "return-shipping-options-success": "성공", "return-shipping-options-shipping-option-created": "배송 옵션이 생성되었습니다.", "return-shipping-options-error": "오류", "return-shipping-options-add-return-shipping-option": "반품 배송 옵션 추가", "return-shipping-options-cancel": "취소", "return-shipping-options-save-and-close": "저장하고 닫습니다", "return-shipping-options-return-shipping-options": "반품 배송 옵션", "return-shipping-options-add-option": "옵션 추가", "return-shipping-options-enter-specifics-about-available-regional-return-shipment-methods": "사용 가능한 지역별 반품 배송 방법에 대한 세부 정보를 입력하세요.", "shipping-options-success": "성공", "shipping-options-shipping-option-created": "배송 옵션이 생성되었습니다.", "shipping-options-error": "오류", "shipping-options-add-shipping-option": "배송 옵션 추가", "shipping-options-cancel": "취소", "shipping-options-save-and-close": "저장하고 닫습니다", "shipping-options-shipping-options": "배송 옵션", "shipping-options-add-option": "옵션 추가", "shipping-options-enter-specifics-about-available-regional-shipment-methods": "사용 가능한 지역 배송 방법에 대한 세부 정보를 입력하세요.", "new-region-created": "지역이 생성되었습니다.", "new-create-region": "지역 만들기", "new-details": "세부", "new-add-the-region-details": "지역 세부정보를 추가합니다.", "new-providers": "공급자", "new-add-which-fulfillment-and-payment-providers-should-be-available-in-this-region": "이 지역에서 사용할 수 있는 주문 처리 및 결제 제공업체를 추가하세요.", "region-overview-regions": "지역", "region-overview-manage-the-markets-that-you-will-operate-within": "귀하가 운영할 시장을 관리하십시오.", "region-overview-not-configured": "구성되지 않음", "region-overview-fulfillment-providers": "주문 처리 제공업체:", "return-reasons-notification-success": "성공", "return-reasons-created-a-new-return-reason": "새 반품 사유를 만들었습니다.", "return-reasons-success": "성공", "return-reasons-error": "오류", "return-reasons-cannot-create-a-return-reason-with-an-existing-value": "기존 값으로 반품 사유를 생성할 수 없습니다.", "return-reasons-add-reason": "이유 추가", "return-reasons-value-is-required": "값은 필수 항목입니다.", "return-reasons-value": "값", "return-reasons-label-is-required": "라벨은 필수 항목입니다.", "return-reasons-label": "상표", "return-reasons-description": "설명", "return-reasons-customer-received-the-wrong-size": "고객이 잘못된 사이즈를 받았습니다.", "return-reasons-cancel": "취소", "return-reasons-create": "만들다", "return-reasons-success-title": "성공", "return-reasons-successfully-updated-return-reason": "반품 이유가 업데이트되었습니다.", "return-reasons-duplicate-reason": "중복된 사유", "return-reasons-delete-reason": "삭제 이유", "return-reasons-save": "구하다", "return-reasons-details": "세부", "return-reasons-delete-return-reason": "반품 사유 삭제", "return-reasons-are-you-sure-you-want-to-delete-this-return-reason": "이 반품 이유를 삭제하시겠습니까?", "return-reasons-back-to-settings": "설정으로 돌아가기", "return-reasons-return-reasons": "반품 이유", "return-reasons-add-reason-label": "이유 추가", "return-reasons-manage-reasons-for-returned-items": "반품 사유 관리", "taxes-details": "세부", "taxes-new-tax-rate": "새로운 세율", "taxes-tax-calculation-settings": "세금 계산 설정", "taxes-success": "성공", "taxes-successfully-updated-tax-rate": "세율을 성공적으로 업데이트했습니다.", "taxes-error": "오류", "taxes-overrides": "재정의", "taxes-product-rules": "제품 규칙", "taxes-product-rules-description_one": "{{count}}개의 productWithCount에 적용됩니다.", "taxes-product-rules-description_other": "{{count}}개의 productWithCount에 적용됩니다.", "taxes-product-type-rules": "상품 유형 규칙", "taxes-product-type-rules-description_one": "{{count}}개의 제품 유형에 적용됩니다WithCount", "taxes-product-type-rules-description_other": "{{count}}개의 제품 유형에 적용됩니다WithCount", "taxes-shipping-option-rules": "배송 옵션 규칙", "taxes-applies-to-shipping-option-with-count_one": "{{count}}개의 배송 옵션에 적용됩니다WithCount", "taxes-applies-to-shipping-option-with-count_other": "{{count}}개의 배송 옵션에 적용됩니다WithCount", "taxes-add-overrides": "재정의 추가", "taxes-cancel": "취소", "taxes-save": "구하다", "taxes-name": "이름", "taxes-default": "기본", "taxes-rate-name": "요금명", "taxes-tax-rate": "세율", "taxes-tax-code": "세금 코드", "taxes-edit-tax-rate": "세율 편집", "taxes-back-to-settings": "설정으로 돌아가기", "taxes-regions": "지역", "taxes-select-the-region-you-wish-to-manage-taxes-for": "세금을 관리하려는 지역을 선택하세요.", "taxes-go-to-region-settings": "지역 설정으로 이동", "taxes-successfully-created-tax-rate": "세율이 생성되었습니다.", "taxes-add-tax-rate": "세율 추가", "taxes-applies-to-product-type-with-count_one": "{{count}}개의 제품 유형에 적용됩니다WithCount", "taxes-applies-to-product-type-with-count_other": "{{count}}개의 제품 유형에 적용됩니다WithCount", "taxes-create": "만들다", "taxes-select-products": "제품 선택", "taxes-select-product-types-label": "제품 유형 선택", "taxes-product-types": "제품 유형", "taxes-system-tax-provider": "시스템 세금 제공자", "taxes-region-tax-settings-were-successfully-updated": "지역세 설정이 업데이트되었습니다.", "taxes-tax-provider": "세금 제공자", "taxes-calculate-taxes-automatically": "세금을 자동으로 계산하시겠습니까?", "taxes-automatically-apply-tax-calculations-to-carts": "선택하면 Medusa가 자동으로 이 지역의 카트에 세금 계산을 적용합니다. ", "taxes-apply-tax-to-gift-cards": "기프트 카드에 세금을 적용하시겠습니까?", "taxes-apply-taxes-to-gift-cards": "체크아웃 시 기프트 카드에 세금이 부과됩니다. ", "taxes-search-products": "제품 검색..", "taxes-select-shipping-option": "배송 옵션 선택", "taxes-shipping-options": "배송 옵션", "taxes-delete-tax-rate-heading": "세율 삭제", "taxes-confirm-delete": "이 세율을 삭제하시겠습니까?", "taxes-tax-rate-was-deleted": "세율이 삭제되었습니다.", "taxes-edit": "편집하다", "taxes-delete-tax-rate": "세율 삭제", "taxes-delete-rule": "규칙 삭제", "taxes-type": "유형", "taxes-products": "제품", "taxes-select-individual-products": "개별 제품 선택", "taxes-select-product-types": "제품 유형 선택", "taxes-select-shipping-options": "배송 옵션을 선택하세요", "taxes-back": "뒤쪽에", "taxes-add": "추가하다", "taxes-code": "암호", "users-invite-users": "사용자 초대", "users-back-to-settings": "설정으로 돌아가기", "users-the-team": "팀", "users-manage-users-of-your-medusa-store": "Medusa Store 사용자 관리", "users-count_one": "{{세다}}", "users-count_other": "{{세다}}"}