import { Metadata } from "next"

import { getApprovedReviews, getRegion } from "@lib/data"
import Hero from "@modules/home/<USER>/hero"
import { cache } from "react"
import HowItWorks from "@modules/home/<USER>/how-it-works"
import ReviewCarousel from "@modules/reviews/components/ReviewCarousel"
import NextSteps from "@modules/home/<USER>/next-steps"
import CTA from "@modules/home/<USER>/cta"

export const metadata: Metadata = {
  title: "Trading Eras™ - The first bracelet trading platform!",
  description:
    "A place for Swifties to trade friendship bracelets hassle-free!",
}

const getCachedApprovedReviews = cache(async () => {
  return await getApprovedReviews()
})

export default async function Home({
  params: { countryCode },
}: {
  params: { countryCode: string }
}) {
  const region = await getRegion(countryCode)
  const reviews = await getCachedApprovedReviews()

  return (
    <>
      <Hero />
      <ReviewCarousel reviews={reviews} />
      <NextSteps />
      <HowItWorks />
      <CTA />
    </>
  )
}
