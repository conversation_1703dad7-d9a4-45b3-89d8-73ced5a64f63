import Image from "next/image"
import { ArrowLongRight } from "@medusajs/icons"
import { Text } from "@medusajs/ui"
import LocalizedClientLink from "@modules/common/components/localized-client-link"

const NextSteps = () => {
  return (
    <>
      <div className="flex flex-col content-container my-10 items-center justify-center">
        <div className="flex flex-col sm:flex-row gap-8 w-full my-6">
          <LocalizedClientLink href="/guidelines">
            <div className="flex flex-col items-center justify-center border border-violet-600 bg-violet-50 shadow-sm hover:shadow-lg hover:cursor-pointer rounded-xl w-full overflow-clip">
              <Image
                src="/images/bracelets-guidelines.JPG"
                width={800}
                height={800}
                alt="Read our guidelines"
                className="aspect-square object-cover"
              />
              <div className="flex flex-col items-center justify-center p-6">
                <div className="text-violet-600 font-semibold text-2xl">
                  Guidelines
                </div>
                <Text className="text-base text-gray-800 text-center">
                  Before trading, check out our guidelines on bracelet-making,
                  timelines and shipping.
                </Text>
                <Text className="text-base text-violet-600 font-semibold flex gap-2 items-center mt-4">
                  Read guidelines
                  <ArrowLongRight />
                </Text>
              </div>
            </div>
          </LocalizedClientLink>
          <LocalizedClientLink href="/trade-groups">
            <div className="flex flex-col items-center justify-center border border-teal-600 bg-teal-50 shadow-sm r hover:shadow-lg hover:cursor-pointer rounded-xl w-full overflow-clip">
              <Image
                src="/images/shipping-guidelines.JPG"
                width={800}
                height={800}
                alt="Start trading breacelets"
                className="aspect-square object-cover"
              />
              <div className="flex flex-col items-center justify-center p-6">
                <div className="text-teal-600 font-semibold text-2xl">
                  Start Trading
                </div>
                <Text className="text-base text-gray-800 text-center">
                  Join a group and start trading bracelets with other Swifties!
                </Text>
                <Text className="text-base text-teal-600 font-semibold flex gap-2 items-center mt-4">
                  Explore trade groups
                  <ArrowLongRight />
                </Text>
              </div>
            </div>
          </LocalizedClientLink>
          <LocalizedClientLink href="/faqs">
            <div className="flex flex-col items-center justify-center border border-pink-600 bg-pink-50 shadow-sm r hover:shadow-lg hover:cursor-pointer rounded-xl w-full overflow-clip">
              <Image
                src="/images/about-us.JPG"
                width={800}
                height={800}
                alt="Read our FAQs"
                className="aspect-square object-cover"
              />
              <div className="flex flex-col items-center justify-center p-6">
                <div className="text-pink-600 font-semibold text-2xl">
                  Got Questions?
                </div>
                <Text className="text-base text-gray-800 text-center">
                  We&apos;ve got answers on the most common ones in our FAQs.
                </Text>
                <Text className="text-base text-pink-600 font-semibold flex gap-2 items-center mt-4">
                  Check out FAQs
                  <ArrowLongRight />
                </Text>
              </div>
            </div>
          </LocalizedClientLink>
        </div>
      </div>
    </>
  )
}

export default NextSteps
