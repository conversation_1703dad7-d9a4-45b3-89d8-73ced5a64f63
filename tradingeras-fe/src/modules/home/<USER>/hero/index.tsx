import { But<PERSON>, Head<PERSON> } from "@medusajs/ui"
import Image from "next/image"

const Hero = () => {
  return (
    <div className="w-full border-ui-border-base relative flex flex-col justify-center items-center">
      <div className="content-container pt-20 pb-8 flex flex-col lg:flex-row justify-between lg:items-center text-center gap-10 sm:gap-6 m-auto overflow-hidden">
        <div className="w-full shrink-0 relative">
          <Heading
            level="h1"
            className="text-[56px] leading-[60px] font-bold text-violet-950"
          >
            Join. <span className="line-clamp-1">Get matched. </span>
            <span className="text-pink-600 font-bold">Trade bracelets!</span>
          </Heading>
          <Heading
            level="h2"
            className="text-2xl leading-8 py-4 font-normal md:max-w-lg lg:max-w-full w-full"
          >
            Welcome to{" "}
            <span className="text-pink-600 font-bold">Trading Eras!</span> The
            first friendship bracelet trading platform.
          </Heading>
        </div>
      </div>
    </div>
  )
}

export default Hero
