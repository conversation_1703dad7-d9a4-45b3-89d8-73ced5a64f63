import { Github } from "@medusajs/icons"
import { But<PERSON>, Head<PERSON> } from "@medusajs/ui"
import Image from "next/image"

const HowItWorks = () => {
  return (
    <div className="small:min-h-[25vh] w-full border-ui-border-base relative flex flex-col justify-center items-center">
      <div className="inset-0 z-10 flex flex-col sm:flex-row justify-center items-center p-6 sm:p-10 gap-6 my-auto mx-4 sm:mx-10 bg-teal-50 rounded-3xl max-w-7xl">
        <Image
          src="/images/giving-back.JPG"
          width={500}
          height={500}
          alt="How Trading Eras works"
          className="rounded-xl aspect-square object-cover"
        />
        <div className="flex flex-col justify-center items-center text-center gap-6">
          <span>
            <Heading
              level="h2"
              className="text-[48px] text-violet-900 leading-[54px] font-bold"
            >
              How It Works
            </Heading>
          </span>
          <div className="flex flex-col justify-center items-center gap-8 w-full sm:px-10">
            <div className="flex flex-row items-center gap-6 sm:gap-4">
              <div className="w-1/4 sm:w-1/5">
                <div className="w-16 h-16 bg-violet-600 flex justify-center items-center text-3xl font-bold text-white rounded-full">
                  1
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Heading level="h3" className="text-lg  font-bold text-left">
                  Sign Up and Join a Trading Group
                </Heading>
                <p className="text-left text-teal-600 text-base">
                  To begin trading, sign up, fill out your profile, and join a
                  trading group!{" "}
                  <span className="font-semibold italic">
                    1, 2, 3, Let&apos;s go, Swifties!
                  </span>
                </p>
              </div>
            </div>
            <div className="flex flex-row items-center gap-6 sm:gap-4">
              <div className="w-1/4 sm:w-1/5">
                <div className="w-16 h-16 bg-violet-600 flex justify-center items-center text-3xl font-bold text-white rounded-full">
                  2
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Heading level="h3" className="text-lg font-bold text-left">
                  Make Your Bracelets
                </Heading>
                <p className="text-left text-teal-600 text-base">
                  Once you have joined a trade group, begin to make friendship
                  bracelets -{" "}
                  <span className="font-semibold italic">
                    take the moment and taste it.
                  </span>
                </p>
              </div>
            </div>
            <div className="flex flex-row items-center gap-6 sm:gap-4">
              <div className="w-1/4 sm:w-1/5">
                <div className="w-16 h-16 bg-violet-600 flex justify-center items-center text-3xl font-bold text-white rounded-full">
                  3
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Heading level="h3" className="text-lg font-bold text-left">
                  Ship Your Bracelets
                </Heading>
                <p className="text-left text-teal-600 text-base">
                  After you&apos;ve created your 3 beautiful bracelets, ship
                  them out to the Swifite you were paired with.{" "}
                  <span className="font-semibold italic">
                    It&apos;s nice to have a friend.
                  </span>
                </p>
              </div>
            </div>
            <div className="flex flex-row items-center gap-6 sm:gap-4">
              <div className="w-1/4 sm:w-1/5">
                <div className="w-16 h-16 bg-violet-600 flex justify-center items-center text-3xl font-bold text-white rounded-full">
                  4
                </div>
              </div>
              <div className="flex flex-col gap-2">
                <Heading level="h3" className="text-lg font-bold text-left">
                  Receive, Enjoy and Rate!
                </Heading>
                <p className="text-left text-teal-600 text-base">
                  Best believe you&apos;ll be bejeweled when your bracelets
                  arrive. Don&apos;t forget to rate the bracelets you receive
                  with <span className="font-semibold italic">Karma</span>{" "}
                  points!
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HowItWorks
