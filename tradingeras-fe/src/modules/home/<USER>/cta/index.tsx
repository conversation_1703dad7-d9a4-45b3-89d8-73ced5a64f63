import { Github } from "@medusajs/icons"
import { But<PERSON>, Head<PERSON> } from "@medusajs/ui"
import LocalizedClientLink from "@modules/common/components/localized-client-link"
import Image from "next/image"

const CTA = () => {
  return (
    <div className="small:min-h-[25vh] w-full border-ui-border-base relative flex flex-col justify-center items-center my-8">
      <div className="inset-0 z-10 flex flex-col sm:flex-row justify-center items-center p-6 sm:p-10 gap-6 my-auto mx-4 sm:mx-10 max-w-7xl">
        <Heading
          level="h2"
          className="text-[48px] text-violet-900 leading-[54px] font-bold"
        >
          Ready to get started?
        </Heading>
        <div className="flex flex-col gap-6">
          <Heading level="h3" className="text-2xl text-black">
            Show off your unique bracelet making Style, and trade with the rest
            of the Swiftie community.
          </Heading>
          <LocalizedClientLink href="/trade-groups">
            <Button
              size="large"
              variant="primary"
              className="bg-violet-600 hover:bg-violet-800"
            >
              Get Started!
            </Button>
          </LocalizedClientLink>
        </div>
      </div>
    </div>
  )
}

export default CTA
