"use client"

import React, { useEffect, useRef } from "react"
import { Reviews } from "types/global"
import useEmblaCarousel from "embla-carousel-react"
import AutoScroll from "embla-carousel-auto-scroll"
import Image from "next/image"

interface ReviewCarouselProps {
  reviews: Reviews[]
}

const getInitials = (firstName?: string, lastName?: string) => {
  const firstInitial = firstName ? firstName.charAt(0).toUpperCase() : ""
  const lastInitial = lastName ? lastName.charAt(0).toUpperCase() : ""
  return `${firstInitial}${lastInitial}`
}

const ReviewCarousel: React.FC<ReviewCarouselProps> = ({ reviews }) => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true }, [
    AutoScroll({ playOnInit: true, speed: 1, stopOnInteraction: true }),
  ])

  useEffect(() => {
    if (emblaApi) {
      emblaApi.reInit()
    }
  }, [emblaApi])

  return (
    <div className="small:min-h-[25vh] w-full border-ui-border-base relative flex flex-col justify-center items-center">
      <div className="embla max-w-full mx-auto">
        <div className="embla__viewport overflow-hidden" ref={emblaRef}>
          <div className="embla__container flex touch-pan-y touch-pinch-zoom">
            {reviews.map((review) => (
              <div key={review.id} className="embla__slide">
                <div className="mb-4 p-4 bg-pink-50/60 rounded-lg shadow-md flex flex-col">
                  <div className="flex gap-2 items-center mb-2">
                    <div className="font-bold">To:</div>
                    <div>
                      {review.receiver.profile_picture ? (
                        <Image
                          src={review.receiver.profile_picture}
                          alt="Profile Picture"
                          className="w-10 h-10 rounded-full object-cover"
                          width={96}
                          height={96}
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-violet-200 flex items-center justify-center">
                          <span className="text-violet-700 font-extrabold">
                            {getInitials(
                              review.receiver.first_name,
                              review.receiver.last_name
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>{review.receiver.first_name}</div>
                  </div>
                  <div className="flex gap-2 items-center mb-2">
                    <div className="font-bold">From:</div>
                    <div>
                      {review.customer.profile_picture ? (
                        <Image
                          src={review.customer.profile_picture}
                          alt="Profile Picture"
                          className="w-10 h-10 rounded-full object-cover"
                          width={96}
                          height={96}
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-pink-200 flex items-center justify-center">
                          <span className="text-pink-600 font-extrabold">
                            {getInitials(
                              review.customer.first_name,
                              review.customer.last_name
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                    <div>{review.customer.first_name}</div>
                  </div>
                  <p className="text-lg italic text-gray-800 mb-2 line-clamp-2">
                    {review.review}
                  </p>
                  <div className="text-pink-600 text-lg flex items-center gap-2">
                    Karma Rating:{" "}
                    <div className="flex">
                      {Array.from({ length: 5 }).map((_, index) => (
                        <svg
                          key={index}
                          className="text-lg w-4 h-4"
                          fill="currentColor"
                          stroke="currentColor"
                          strokeWidth="2"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 1.5l3.668 7.568L24 10.5l-6 5.847L19.417 24 12 19.5 4.583 24 6 16.347 0 10.5l8.332-1.432L12 1.5z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default ReviewCarousel
