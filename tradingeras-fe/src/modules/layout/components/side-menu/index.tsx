"use client"

import { Popover, Transition } from "@headlessui/react"
import { ArrowRightMini, XMark, BarsThree } from "@medusajs/icons"
import { Region } from "@medusajs/medusa"
import { Text, clx, useToggleState } from "@medusajs/ui"
import { Fragment } from "react"

import LocalizedClientLink from "@modules/common/components/localized-client-link"
import CountrySelect from "../country-select"

const SideMenuItems = {
  Home: "/",
  "Trade Groups": "/trade-groups",
  "Gift Cards": "/giftcards",
  Guidelines: "/guidelines",
  Account: "/account",
  Cart: "/cart",
  FAQs: "/faqs",
  "About Us": "/about-us",
}

const SideMenu = ({ regions }: { regions: Region[] | null }) => {
  const toggleState = useToggleState()

  return (
    <div className="h-full">
      <div className="flex items-center h-full">
        <Popover className="h-full flex">
          {({ open, close }) => (
            <>
              <div className="relative flex h-full">
                <Popover.Button
                  aria-label="Open menu"
                  className="relative h-full flex items-center transition-all ease-out duration-200 font-bold text-purple-950 text-lg focus:outline-none hover:text-ui-fg-base"
                >
                  <BarsThree />
                </Popover.Button>
              </div>

              <Transition
                show={open}
                as={Fragment}
                enter="transition ease-out duration-150"
                enterFrom="opacity-0"
                enterTo="opacity-100 backdrop-blur-2xl"
                leave="transition ease-in duration-150"
                leaveFrom="opacity-100 backdrop-blur-2xl"
                leaveTo="opacity-0"
              >
                <Popover.Panel className="flex flex-col absolute w-full sm:w-1/3 2xl:w-1/4 sm:min-w-min h-[100vh] right-0 z-30 text-sm text-ui-fg-on-color backdrop-blur-2xl">
                  {/* <div className="flex flex-col h-full bg-[rgba(3,7,18,0.5)] justify-between p-6"> */}
                  <div className="flex flex-col h-full bg-violet-900 bg-opacity-50 justify-between p-6">
                    <div className="flex justify-end" id="xmark">
                      <button onClick={close} aria-label="Close menu">
                        <XMark />
                      </button>
                    </div>
                    <ul className="flex flex-col gap-6 items-center justify-center">
                      {Object.entries(SideMenuItems).map(([name, href]) => {
                        return (
                          <li key={name}>
                            <LocalizedClientLink
                              href={href}
                              className="text-3xl leading-10 hover:text-purple-950"
                              onClick={close}
                            >
                              {name}
                            </LocalizedClientLink>
                          </li>
                        )
                      })}
                    </ul>
                    <div className="flex flex-col gap-y-6">
                      {/* <div
                        className="flex justify-between"
                        onMouseEnter={toggleState.open}
                        onMouseLeave={toggleState.close}
                      >
                        {regions && (
                          <CountrySelect
                            toggleState={toggleState}
                            regions={regions}
                          />
                        )}
                        <ArrowRightMini
                          className={clx(
                            "transition-transform duration-150",
                            toggleState.state ? "-rotate-90" : ""
                          )}
                        />
                      </div> */}
                      {/* <Text className="flex justify-center txt-compact-small">
                        © {new Date().getFullYear()} Trading Eras. All rights
                        reserved.
                      </Text> */}
                    </div>
                  </div>
                </Popover.Panel>
              </Transition>
            </>
          )}
        </Popover>
      </div>
    </div>
  )
}

export default SideMenu
