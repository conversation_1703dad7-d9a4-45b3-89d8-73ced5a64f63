import type { MedusaRequest, MedusaResponse } from "@medusajs/medusa";
import ReviewService from "../../../../../services/review";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const reviewService: ReviewService = req.scope.resolve("reviewService");
  const { id } = req.params;

  try {
    const result = await reviewService.reset(id);
    return res.json(result);
  } catch (err) {
    return res.sendStatus(500);
  }
}
